import { DataTypes, Model, Optional, Op } from 'sequelize'
import { sequelize } from '../config/database'
import { Article } from './Article'
import { User } from './User'

/**
 * 文章点赞模型的属性接口定义
 */
export interface ArticleLikeAttributes {
  id: number
  articleId: number
  userId: number
  createdAt: Date
}

/**
 * 文章点赞创建时的属性接口定义
 */
export interface ArticleLikeCreationAttributes extends Optional<ArticleLikeAttributes, 'id' | 'createdAt'> { }

/**
 * 文章点赞模型类
 * 用于管理文章的点赞功能
 */
export class ArticleLike extends Model<ArticleLikeAttributes, ArticleLikeCreationAttributes> implements ArticleLikeAttributes {
  public id!: number
  public articleId!: number
  public userId!: number
  public createdAt!: Date

  /**
   * 切换点赞状态
   */
  public static async toggleLike(articleId: number, userId: number): Promise<{ liked: boolean; likeCount: number }> {
    const existingLike = await this.findOne({
      where: { articleId, userId }
    })

    if (existingLike) {
      // 取消点赞
      await existingLike.destroy()
      const likeCount = await this.count({ where: { articleId } })
      return { liked: false, likeCount }
    } else {
      // 添加点赞
      await this.create({ articleId, userId })
      const likeCount = await this.count({ where: { articleId } })
      return { liked: true, likeCount }
    }
  }

  /**
   * 检查用户是否已点赞文章
   */
  public static async isLikedByUser(articleId: number, userId: number): Promise<boolean> {
    const like = await this.findOne({
      where: { articleId, userId }
    })
    return !!like
  }

  /**
   * 获取文章的点赞数
   */
  public static async getArticleLikeCount(articleId: number): Promise<number> {
    return this.count({
      where: { articleId }
    })
  }

  /**
   * 获取用户点赞的文章列表
   */
  public static async getUserLikedArticles(userId: number, limit: number = 20, offset: number = 0) {
    return this.findAndCountAll({
      where: { userId },
      order: [['createdAt', 'DESC']],
      limit,
      offset,
      include: [
        {
          model: Article,
          as: 'article',
          attributes: ['id', 'title', 'slug', 'excerpt', 'publishedAt'],
          include: [
            {
              model: User,
              as: 'author',
              attributes: ['id', 'username']
            }
          ]
        }
      ]
    })
  }

  /**
   * 获取最受欢迎的文章（按点赞数排序）
   */
  public static async getMostLikedArticles(limit: number = 10, days?: number) {
    const whereClause: any = {}

    if (days) {
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000)
      whereClause.createdAt = {
        [Op.gte]: startDate
      }
    }

    return this.findAll({
      attributes: [
        'articleId',
        [sequelize.fn('COUNT', sequelize.col('id')), 'likeCount']
      ],
      where: whereClause,
      group: ['articleId'],
      order: [[sequelize.literal('likeCount'), 'DESC']],
      limit,
      include: [
        {
          model: Article,
          as: 'article',
          attributes: ['id', 'title', 'slug', 'excerpt', 'publishedAt'],
          include: [
            {
              model: User,
              as: 'author',
              attributes: ['id', 'username']
            }
          ]
        }
      ]
    })
  }

  /**
   * 获取文章的点赞用户列表
   */
  public static async getArticleLikers(articleId: number, limit: number = 20, offset: number = 0) {
    return this.findAndCountAll({
      where: { articleId },
      order: [['createdAt', 'DESC']],
      limit,
      offset,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username']
        }
      ]
    })
  }
}

/**
 * 初始化文章点赞模型
 */
ArticleLike.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    articleId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'article_id',
      references: {
        model: 'articles',
        key: 'id'
      }
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'id'
      }
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    }
  },
  {
    sequelize,
    modelName: 'ArticleLike',
    tableName: 'article_likes',
    timestamps: true,
    updatedAt: false,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['article_id', 'user_id']
      },
      {
        fields: ['article_id']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['created_at']
      }
    ]
  }
)
