{"version": 3, "file": "014-create-rbac-tables.js", "sourceRoot": "", "sources": ["../../../src/database/migrations/014-create-rbac-tables.ts"], "names": [], "mappings": ";;;AAAA,yCAAqD;AAQ9C,MAAM,EAAE,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IAExE,MAAM,cAAc,CAAC,WAAW,CAAC,OAAO,EAAE;QACxC,EAAE,EAAE;YACF,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK;SACjB;QACD,IAAI,EAAE;YACJ,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,SAAS,EAAE,KAAK;YAChB,MAAM,EAAE,IAAI;SACb;QACD,WAAW,EAAE;YACX,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI;SAChB;QACD,SAAS,EAAE;YACT,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,IAAI;SACnB;QACD,SAAS,EAAE;YACT,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,KAAK;SACpB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;SAC5B;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;SAC5B;KACF,EAAE;QACD,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,oBAAoB;KAC9B,CAAC,CAAA;IAGF,MAAM,cAAc,CAAC,WAAW,CAAC,aAAa,EAAE;QAC9C,EAAE,EAAE;YACF,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK;SACjB;QACD,IAAI,EAAE;YACJ,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,KAAK;YAChB,MAAM,EAAE,IAAI;SACb;QACD,WAAW,EAAE;YACX,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI;SAChB;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,SAAS,EAAE,KAAK;SACjB;QACD,MAAM,EAAE;YACN,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,SAAS,EAAE,KAAK;SACjB;QACD,SAAS,EAAE;YACT,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,IAAI;SACnB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;SAC5B;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;SAC5B;KACF,EAAE;QACD,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,oBAAoB;KAC9B,CAAC,CAAA;IAGF,MAAM,cAAc,CAAC,WAAW,CAAC,YAAY,EAAE;QAC7C,EAAE,EAAE;YACF,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK;SACjB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB;QACD,WAAW,EAAE;YACX,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,IAAI;YACf,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,UAAU;SACrB;QACD,WAAW,EAAE;YACX,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;SAC5B;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;SAC5B;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;SAC5B;KACF,EAAE;QACD,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,oBAAoB;KAC9B,CAAC,CAAA;IAGF,MAAM,cAAc,CAAC,WAAW,CAAC,kBAAkB,EAAE;QACnD,EAAE,EAAE;YACF,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK;SACjB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB;QACD,aAAa,EAAE;YACb,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,UAAU,EAAE;gBACV,KAAK,EAAE,aAAa;gBACpB,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB;QACD,WAAW,EAAE;YACX,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,IAAI;YACf,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,UAAU;SACrB;QACD,WAAW,EAAE;YACX,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;SAC5B;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;SAC5B;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;SAC5B;KACF,EAAE;QACD,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,oBAAoB;KAC9B,CAAC,CAAA;IAIF,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,CAAA;IACxF,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAA;IAC7D,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC,CAAA;IACpF,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAA;IAClE,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC,CAAA;IACpF,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAA;IAClE,CAAC;IAGD,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAA;IACpG,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAA;IACnE,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,6BAA6B,EAAE,CAAC,CAAA;IAC7H,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,+DAA+D,CAAC,CAAA;IAC9E,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE,sBAAsB,EAAE,CAAC,CAAA;IAC9F,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAA;IACvE,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,oBAAoB,EAAE,CAAC,CAAA;IAC1F,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAA;IACrE,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE,uBAAuB,EAAE,CAAC,CAAA;IAChG,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAA;IACxE,CAAC;IAGD,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,sBAAsB,EAAE,CAAC,CAAA;IACrH,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAA;IACvE,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE,IAAI,EAAE,oBAAoB,EAAE,CAAC,CAAA;IAC1F,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAA;IACrE,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE,IAAI,EAAE,oBAAoB,EAAE,CAAC,CAAA;IAC1F,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAA;IACrE,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,EAAE,wBAAwB,EAAE,CAAC,CAAA;IAClG,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAA;IACzE,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,EAAE,wBAAwB,EAAE,CAAC,CAAA;IAClG,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAA;IACzE,CAAC;IAGD,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC,SAAS,EAAE,eAAe,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,kCAAkC,EAAE,CAAC,CAAA;IAC7I,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,oEAAoE,CAAC,CAAA;IACnF,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC,CAAA;IACtG,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAA;IAC3E,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,EAAE,gCAAgC,EAAE,CAAC,CAAA;IAClH,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,kEAAkE,CAAC,CAAA;IACjF,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,EAAE,8BAA8B,EAAE,CAAC,CAAA;IAC9G,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,gEAAgE,CAAC,CAAA;IAC/E,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,EAAE,8BAA8B,EAAE,CAAC,CAAA;IAC9G,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,gEAAgE,CAAC,CAAA;IAC/E,CAAC;AACH,CAAC,CAAA;AA5TY,QAAA,EAAE,MA4Td;AAEM,MAAM,IAAI,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IAE1E,MAAM,cAAc,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAA;IAClD,MAAM,cAAc,CAAC,SAAS,CAAC,YAAY,CAAC,CAAA;IAC5C,MAAM,cAAc,CAAC,SAAS,CAAC,aAAa,CAAC,CAAA;IAC7C,MAAM,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;AACzC,CAAC,CAAA;AANY,QAAA,IAAI,QAMhB"}