import { Request, Response } from 'express'
import { Op } from 'sequelize'
import { Notification, NotificationPreference, User } from '../models'
import { AuthenticatedRequest } from '../middleware/auth'
import { getIdParam } from '../utils/paramValidation'

/**
 * 通知控制器
 * 处理通知的CRUD操作、状态管理和偏好设置
 */
export class NotificationController {
  /**
   * 获取用户通知列表
   * GET /api/notifications
   */
  static async getNotifications(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user!.id
      const {
        page = 1,
        limit = 20,
        type,
        is_read,
        priority
      } = req.query

      const offset = (Number(page) - 1) * Number(limit)
      const whereClause: any = { recipientId: userId }

      // 添加过滤条件
      if (type) {
        whereClause.type = type
      }
      if (is_read !== undefined) {
        whereClause.isRead = is_read === 'true'
      }
      if (priority) {
        whereClause.priority = priority
      }

      const { rows: notifications, count: total } = await Notification.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: 'sender',
            attributes: ['id', 'username'],
            required: false
          }
        ],
        order: [
          ['isRead', 'ASC'],  // 未读在前
          ['priority', 'DESC'], // 高优先级在前
          ['createdAt', 'DESC'] // 最新在前
        ],
        limit: Number(limit),
        offset,
        distinct: true
      })

      res.json({
        success: true,
        data: {
          notifications,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total,
            totalPages: Math.ceil(total / Number(limit))
          }
        }
      })
    } catch (error) {
      console.error('获取通知列表失败:', error)
      res.status(500).json({
        success: false,
        message: '获取通知列表失败'
      })
    }
  }

  /**
   * 获取未读通知数量
   * GET /api/notifications/unread-count
   */
  static async getUnreadCount(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user!.id
      const count = await Notification.getUnreadCount(userId)

      res.json({
        success: true,
        data: { count }
      })
    } catch (error) {
      console.error('获取未读通知数量失败:', error)
      res.status(500).json({
        success: false,
        message: '获取未读通知数量失败'
      })
    }
  }

  /**
   * 标记通知为已读
   * PUT /api/notifications/:id/read
   */
  static async markAsRead(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user!.id
      const notificationId = getIdParam(req)

      const notification = await Notification.findOne({
        where: {
          id: notificationId,
          recipientId: userId
        }
      })

      if (!notification) {
        res.status(404).json({
          success: false,
          message: '通知不存在'
        })
        return
      }

      await notification.markAsRead()

      res.json({
        success: true,
        message: '通知已标记为已读',
        data: notification
      })
    } catch (error) {
      console.error('标记通知已读失败:', error)
      res.status(500).json({
        success: false,
        message: '标记通知已读失败'
      })
    }
  }

  /**
   * 批量标记通知为已读
   * PUT /api/notifications/batch/read
   */
  static async markBatchAsRead(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user!.id
      const { notificationIds } = req.body

      if (!Array.isArray(notificationIds) || notificationIds.length === 0) {
        res.status(400).json({
          success: false,
          message: '请提供有效的通知ID列表'
        })
        return
      }

      const affectedCount = await Notification.markBulkAsRead(notificationIds, userId)

      res.json({
        success: true,
        message: `成功标记 ${affectedCount} 条通知为已读`,
        data: { affectedCount }
      })
    } catch (error) {
      console.error('批量标记通知已读失败:', error)
      res.status(500).json({
        success: false,
        message: '批量标记通知已读失败'
      })
    }
  }

  /**
   * 标记所有通知为已读
   * PUT /api/notifications/all/read
   */
  static async markAllAsRead(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user!.id

      const [affectedCount] = await Notification.update(
        {
          isRead: true,
          readAt: new Date()
        },
        {
          where: {
            recipientId: userId,
            isRead: false
          }
        }
      )

      res.json({
        success: true,
        message: `成功标记 ${affectedCount} 条通知为已读`,
        data: { affectedCount }
      })
    } catch (error) {
      console.error('标记所有通知已读失败:', error)
      res.status(500).json({
        success: false,
        message: '标记所有通知已读失败'
      })
    }
  }

  /**
   * 删除通知
   * DELETE /api/notifications/:id
   */
  static async deleteNotification(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user!.id
      const notificationId = getIdParam(req)

      const notification = await Notification.findOne({
        where: {
          id: notificationId,
          recipientId: userId
        }
      })

      if (!notification) {
        res.status(404).json({
          success: false,
          message: '通知不存在'
        })
        return
      }

      await notification.destroy()

      res.json({
        success: true,
        message: '通知删除成功'
      })
    } catch (error) {
      console.error('删除通知失败:', error)
      res.status(500).json({
        success: false,
        message: '删除通知失败'
      })
    }
  }

  /**
   * 批量删除通知
   * DELETE /api/notifications/batch
   */
  static async deleteBatchNotifications(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user!.id
      const { notificationIds } = req.body

      if (!Array.isArray(notificationIds) || notificationIds.length === 0) {
        res.status(400).json({
          success: false,
          message: '请提供有效的通知ID列表'
        })
        return
      }

      const deletedCount = await Notification.destroy({
        where: {
          id: notificationIds,
          recipientId: userId
        }
      })

      res.json({
        success: true,
        message: `成功删除 ${deletedCount} 条通知`,
        data: { deletedCount }
      })
    } catch (error) {
      console.error('批量删除通知失败:', error)
      res.status(500).json({
        success: false,
        message: '批量删除通知失败'
      })
    }
  }

  /**
   * 获取通知偏好设置
   * GET /api/notifications/preferences
   */
  static async getPreferences(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user!.id
      let preferences = await NotificationPreference.getUserPreferences(userId)

      // 如果用户没有偏好设置，初始化默认设置
      if (preferences.length === 0) {
        preferences = await NotificationPreference.initializeDefaultPreferences(userId)
      }

      res.json({
        success: true,
        data: preferences
      })
    } catch (error) {
      console.error('获取通知偏好设置失败:', error)
      res.status(500).json({
        success: false,
        message: '获取通知偏好设置失败'
      })
    }
  }

  /**
   * 更新通知偏好设置
   * PUT /api/notifications/preferences
   */
  static async updatePreferences(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user!.id
      const { preferences } = req.body

      if (!Array.isArray(preferences)) {
        res.status(400).json({
          success: false,
          message: '请提供有效的偏好设置列表'
        })
        return
      }

      const updatedPreferences = await NotificationPreference.updateUserPreferences(userId, preferences)

      res.json({
        success: true,
        message: '通知偏好设置更新成功',
        data: updatedPreferences
      })
    } catch (error) {
      console.error('更新通知偏好设置失败:', error)
      res.status(500).json({
        success: false,
        message: '更新通知偏好设置失败'
      })
    }
  }
}
