"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    console.log('🔔 开始创建通知系统种子数据...');
    const users = await queryInterface.sequelize.query('SELECT id, username FROM users ORDER BY id LIMIT 3', { type: sequelize_1.QueryTypes.SELECT });
    if (users.length < 2) {
        console.log('⚠️ 用户数据不足，跳过通知种子数据创建');
        return;
    }
    const adminUser = users[0];
    const user1 = users[1];
    const user2 = users[2] || null;
    if (!adminUser || !user1) {
        console.log('⚠️ 缺少必要的用户数据，跳过通知种子数据创建');
        return;
    }
    const now = new Date();
    const notificationData = [
        {
            type: 'interaction',
            title: `${user1.username} 评论了你的文章`,
            content: '在文章《Vue 3 组合式API详解》中评论：这篇文章写得很详细，对我帮助很大！感谢分享。',
            priority: 'medium',
            recipient_id: adminUser.id,
            sender_id: user1.id,
            related_type: 'article',
            related_id: 1,
            action_url: '/articles/1#comments',
            is_read: false,
            created_at: new Date(now.getTime() - 2 * 60 * 60 * 1000),
            updated_at: new Date(now.getTime() - 2 * 60 * 60 * 1000)
        },
        {
            type: 'interaction',
            title: `${user2?.username || 'user2'} 赞了你的说说`,
            content: '"今天学习了新的前端技术栈，感觉收获满满！"',
            priority: 'medium',
            recipient_id: adminUser.id,
            sender_id: user2?.id || user1.id,
            related_type: 'post',
            related_id: 1,
            action_url: '/posts/1',
            is_read: false,
            created_at: new Date(now.getTime() - 1 * 60 * 60 * 1000),
            updated_at: new Date(now.getTime() - 1 * 60 * 60 * 1000)
        },
        {
            type: 'interaction',
            title: `${adminUser.username} 回复了你的评论`,
            content: '在文章中回复：谢谢你的反馈！我会继续分享更多有用的技术文章。',
            priority: 'medium',
            recipient_id: user1.id,
            sender_id: adminUser.id,
            related_type: 'comment',
            related_id: 1,
            action_url: '/articles/1#comments',
            is_read: true,
            read_at: new Date(now.getTime() - 30 * 60 * 1000),
            created_at: new Date(now.getTime() - 45 * 60 * 1000),
            updated_at: new Date(now.getTime() - 30 * 60 * 1000)
        },
        {
            type: 'content',
            title: `${adminUser.username} 发布了新文章`,
            content: '《TypeScript 高级类型系统详解》',
            priority: 'medium',
            recipient_id: user1.id,
            sender_id: adminUser.id,
            related_type: 'article',
            related_id: 2,
            action_url: '/articles/2',
            is_read: false,
            created_at: new Date(now.getTime() - 3 * 60 * 60 * 1000),
            updated_at: new Date(now.getTime() - 3 * 60 * 60 * 1000)
        },
        {
            type: 'content',
            title: '你关注的标签有新内容',
            content: '标签"前端开发"下发布了新文章《React Hooks 最佳实践》',
            priority: 'medium',
            recipient_id: user1.id,
            sender_id: null,
            related_type: 'article',
            related_id: 3,
            action_url: '/articles/3',
            is_read: true,
            read_at: new Date(now.getTime() - 2 * 60 * 60 * 1000),
            created_at: new Date(now.getTime() - 4 * 60 * 60 * 1000),
            updated_at: new Date(now.getTime() - 2 * 60 * 60 * 1000)
        },
        {
            type: 'system',
            title: '系统维护通知',
            content: '系统将于今晚23:00-01:00进行例行维护，期间可能无法正常访问。维护期间我们将优化数据库性能，提升用户体验。',
            priority: 'high',
            recipient_id: adminUser.id,
            sender_id: null,
            related_type: 'system',
            related_id: null,
            action_url: '/system/maintenance',
            is_read: false,
            created_at: new Date(now.getTime() - 6 * 60 * 60 * 1000),
            updated_at: new Date(now.getTime() - 6 * 60 * 60 * 1000)
        },
        {
            type: 'system',
            title: '功能更新通知',
            content: '新增了通知系统功能！现在你可以及时收到评论、点赞等互动通知，还可以在设置中自定义通知偏好。',
            priority: 'medium',
            recipient_id: user1.id,
            sender_id: null,
            related_type: 'system',
            related_id: null,
            action_url: '/notifications/preferences',
            is_read: false,
            created_at: new Date(now.getTime() - 12 * 60 * 60 * 1000),
            updated_at: new Date(now.getTime() - 12 * 60 * 60 * 1000)
        },
        {
            type: 'system',
            title: '安全提醒',
            content: '检测到你的账户在新设备上登录，如果不是本人操作，请立即修改密码并启用两步验证。',
            priority: 'high',
            recipient_id: user2?.id || user1.id,
            sender_id: null,
            related_type: 'system',
            related_id: null,
            action_url: '/settings/security',
            is_read: true,
            read_at: new Date(now.getTime() - 1 * 60 * 60 * 1000),
            created_at: new Date(now.getTime() - 8 * 60 * 60 * 1000),
            updated_at: new Date(now.getTime() - 1 * 60 * 60 * 1000)
        },
        {
            type: 'marketing',
            title: '写作技巧分享',
            content: '想要写出更好的技术博客？查看我们的写作指南，学习如何组织文章结构、选择合适的技术深度。',
            priority: 'low',
            recipient_id: user1.id,
            sender_id: null,
            related_type: 'system',
            related_id: null,
            action_url: '/guides/writing',
            is_read: false,
            created_at: new Date(now.getTime() - 24 * 60 * 60 * 1000),
            updated_at: new Date(now.getTime() - 24 * 60 * 60 * 1000)
        },
        {
            type: 'marketing',
            title: '社区活动邀请',
            content: '本月技术分享活动即将开始！欢迎分享你的技术心得，与其他开发者交流学习。',
            priority: 'low',
            recipient_id: adminUser.id,
            sender_id: null,
            related_type: 'system',
            related_id: null,
            action_url: '/events/tech-sharing',
            is_read: true,
            read_at: new Date(now.getTime() - 12 * 60 * 60 * 1000),
            created_at: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000),
            updated_at: new Date(now.getTime() - 12 * 60 * 60 * 1000)
        }
    ];
    await queryInterface.bulkInsert('notifications', notificationData);
    console.log(`✅ 已创建 ${notificationData.length} 条示例通知`);
    const preferenceData = [];
    for (const user of users) {
        const userPreferences = [
            { user_id: user.id, notification_type: 'interaction', channel: 'in_app', is_enabled: true },
            { user_id: user.id, notification_type: 'interaction', channel: 'email', is_enabled: false },
            { user_id: user.id, notification_type: 'interaction', channel: 'push', is_enabled: false },
            { user_id: user.id, notification_type: 'content', channel: 'in_app', is_enabled: true },
            { user_id: user.id, notification_type: 'content', channel: 'email', is_enabled: false },
            { user_id: user.id, notification_type: 'content', channel: 'push', is_enabled: false },
            { user_id: user.id, notification_type: 'system', channel: 'in_app', is_enabled: true },
            { user_id: user.id, notification_type: 'system', channel: 'email', is_enabled: true },
            { user_id: user.id, notification_type: 'system', channel: 'push', is_enabled: false },
            { user_id: user.id, notification_type: 'marketing', channel: 'in_app', is_enabled: false },
            { user_id: user.id, notification_type: 'marketing', channel: 'email', is_enabled: false },
            { user_id: user.id, notification_type: 'marketing', channel: 'push', is_enabled: false }
        ];
        preferenceData.push(...userPreferences.map(pref => ({
            ...pref,
            created_at: now,
            updated_at: now
        })));
    }
    await queryInterface.bulkInsert('notification_preferences', preferenceData);
    console.log(`✅ 已创建 ${preferenceData.length} 条通知偏好设置`);
    console.log('🔔 通知系统种子数据创建完成');
    console.log('📊 数据统计：');
    console.log(`   - 通知记录：${notificationData.length} 条`);
    console.log(`   - 偏好设置：${preferenceData.length} 条`);
    console.log(`   - 涵盖用户：${users.length} 个`);
};
exports.up = up;
const down = async (queryInterface) => {
    console.log('🗑️ 开始删除通知系统种子数据...');
    await queryInterface.bulkDelete('notification_preferences', {}, {});
    console.log('✅ 已删除通知偏好设置数据');
    await queryInterface.bulkDelete('notifications', {}, {});
    console.log('✅ 已删除通知数据');
    console.log('🔔 通知系统种子数据删除完成');
};
exports.down = down;
//# sourceMappingURL=009-notifications.js.map