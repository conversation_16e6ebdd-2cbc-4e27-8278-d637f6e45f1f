"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auditLog_1 = require("../controllers/auditLog");
const auth_1 = require("../middleware/auth");
const permission_1 = require("../middleware/permission");
const router = (0, express_1.Router)();
router.get('/me', auth_1.authenticateToken, auditLog_1.getMyLogs);
router.get('/me/stats', auth_1.authenticateToken, auditLog_1.getMyStats);
router.get('/', auth_1.authenticateToken, (0, permission_1.requirePermission)('audit.list'), auditLog_1.getLogs);
router.get('/stats', auth_1.authenticateToken, (0, permission_1.requirePermission)('audit.stats'), auditLog_1.getStats);
router.get('/actions', auth_1.authenticateToken, auditLog_1.getActions);
router.get('/resources', auth_1.authenticateToken, auditLog_1.getResources);
router.get('/:id', auth_1.authenticateToken, (0, permission_1.requireAnyRole)(['admin', 'moderator']), auditLog_1.getLogById);
router.post('/cleanup', auth_1.authenticateToken, (0, permission_1.requireRole)('admin'), auditLog_1.cleanupLogs);
router.get('/users/:userId', auth_1.authenticateToken, (0, permission_1.requirePermission)('audit.list'), (req, res, next) => {
    req.query.userId = req.params.userId;
    next();
}, auditLog_1.getLogs);
router.get('/users/:userId/stats', auth_1.authenticateToken, (0, permission_1.requirePermission)('audit.stats'), (req, res, next) => {
    req.query.userId = req.params.userId;
    next();
}, auditLog_1.getStats);
router.get('/resources/:resource', auth_1.authenticateToken, (0, permission_1.requirePermission)('audit.list'), (req, res, next) => {
    req.query.resource = req.params.resource;
    next();
}, auditLog_1.getLogs);
router.get('/resources/:resource/:resourceId', auth_1.authenticateToken, (0, permission_1.requirePermission)('audit.list'), (req, res, next) => {
    req.query.resource = req.params.resource;
    req.query.resourceId = req.params.resourceId;
    next();
}, auditLog_1.getLogs);
exports.default = router;
//# sourceMappingURL=auditLog.js.map