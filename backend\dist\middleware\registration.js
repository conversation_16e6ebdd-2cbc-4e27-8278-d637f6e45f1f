"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.emailVerificationMiddleware = exports.passwordResetMiddleware = exports.passwordResetRequestMiddleware = exports.loginMiddleware = exports.registrationMiddleware = exports.registrationRateLimit = exports.validateEmailVerificationToken = exports.validatePasswordResetToken = exports.checkEmailAvailability = exports.checkUsernameAvailability = exports.validateRequest = exports.emailVerificationRules = exports.passwordResetRules = exports.passwordResetRequestRules = exports.loginValidationRules = exports.registrationValidationRules = void 0;
const express_validator_1 = require("express-validator");
const errorHandler_1 = require("./errorHandler");
const userValidation_1 = require("../utils/userValidation");
const verification_1 = require("../utils/verification");
exports.registrationValidationRules = [
    (0, express_validator_1.body)('username')
        .trim()
        .isLength({ min: 3, max: 50 })
        .withMessage('用户名长度必须在3-50位之间')
        .matches(/^[a-zA-Z0-9_-]+$/)
        .withMessage('用户名只能包含字母、数字、下划线和连字符')
        .custom((value) => {
        if (/^[0-9]/.test(value)) {
            throw new Error('用户名不能以数字开头');
        }
        if (userValidation_1.UserValidationService.containsProfanity(value)) {
            throw new Error('用户名包含不当内容');
        }
        return true;
    }),
    (0, express_validator_1.body)('email')
        .trim()
        .isEmail()
        .withMessage('邮箱格式无效')
        .normalizeEmail()
        .isLength({ max: 255 })
        .withMessage('邮箱长度不能超过255位'),
    (0, express_validator_1.body)('password')
        .isLength({ min: 8, max: 128 })
        .withMessage('密码长度必须在8-128位之间')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])/)
        .withMessage('密码必须包含大小写字母、数字和特殊字符'),
    (0, express_validator_1.body)('confirmPassword')
        .custom((value, { req }) => {
        if (value !== req.body.password) {
            throw new Error('两次输入的密码不一致');
        }
        return true;
    })
];
exports.loginValidationRules = [
    (0, express_validator_1.body)('username')
        .trim()
        .notEmpty()
        .withMessage('用户名不能为空')
        .isLength({ max: 50 })
        .withMessage('用户名长度不能超过50位'),
    (0, express_validator_1.body)('password')
        .notEmpty()
        .withMessage('密码不能为空')
        .isLength({ max: 128 })
        .withMessage('密码长度不能超过128位')
];
exports.passwordResetRequestRules = [
    (0, express_validator_1.body)('email')
        .trim()
        .isEmail()
        .withMessage('邮箱格式无效')
        .normalizeEmail()
];
exports.passwordResetRules = [
    (0, express_validator_1.body)('token')
        .notEmpty()
        .withMessage('重置令牌不能为空'),
    (0, express_validator_1.body)('password')
        .isLength({ min: 8, max: 128 })
        .withMessage('密码长度必须在8-128位之间')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])/)
        .withMessage('密码必须包含大小写字母、数字和特殊字符'),
    (0, express_validator_1.body)('confirmPassword')
        .custom((value, { req }) => {
        if (value !== req.body.password) {
            throw new Error('两次输入的密码不一致');
        }
        return true;
    })
];
exports.emailVerificationRules = [
    (0, express_validator_1.body)('token')
        .notEmpty()
        .withMessage('验证令牌不能为空')
];
const validateRequest = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        const errorMessages = errors.array().map(error => error.msg);
        throw (0, errorHandler_1.createError)(400, errorMessages.join('; '), 'VALIDATION_ERROR');
    }
    next();
};
exports.validateRequest = validateRequest;
const checkUsernameAvailability = async (req, res, next) => {
    try {
        const { username } = req.body;
        if (!username) {
            next();
            return;
        }
        const availability = await userValidation_1.UserValidationService.checkUsernameAvailability(username);
        if (!availability.available) {
            throw (0, errorHandler_1.createError)(400, availability.message || '用户名不可用', 'USERNAME_UNAVAILABLE');
        }
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.checkUsernameAvailability = checkUsernameAvailability;
const checkEmailAvailability = async (req, res, next) => {
    try {
        const { email } = req.body;
        if (!email) {
            next();
            return;
        }
        const availability = await userValidation_1.UserValidationService.checkEmailAvailability(email);
        if (!availability.available) {
            throw (0, errorHandler_1.createError)(400, availability.message || '邮箱不可用', 'EMAIL_UNAVAILABLE');
        }
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.checkEmailAvailability = checkEmailAvailability;
const validatePasswordResetToken = (req, res, next) => {
    try {
        const { token } = req.body;
        const result = verification_1.VerificationService.verifyPasswordResetToken(token);
        if (!result.valid) {
            throw (0, errorHandler_1.createError)(400, result.error || '无效的重置令牌', 'INVALID_RESET_TOKEN');
        }
        req.body.tokenData = result.data;
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.validatePasswordResetToken = validatePasswordResetToken;
const validateEmailVerificationToken = (req, res, next) => {
    try {
        const { token } = req.body;
        const result = verification_1.VerificationService.verifyEmailVerificationToken(token);
        if (!result.valid) {
            throw (0, errorHandler_1.createError)(400, result.error || '无效的验证令牌', 'INVALID_VERIFICATION_TOKEN');
        }
        req.body.tokenData = result.data;
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.validateEmailVerificationToken = validateEmailVerificationToken;
exports.registrationRateLimit = (() => {
    const attempts = new Map();
    const MAX_ATTEMPTS = 5;
    const WINDOW_MS = 15 * 60 * 1000;
    return (req, res, next) => {
        const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
        const now = Date.now();
        for (const [ip, data] of attempts.entries()) {
            if (now > data.resetTime) {
                attempts.delete(ip);
            }
        }
        const clientAttempts = attempts.get(clientIP);
        if (!clientAttempts) {
            attempts.set(clientIP, { count: 1, resetTime: now + WINDOW_MS });
            next();
            return;
        }
        if (now > clientAttempts.resetTime) {
            attempts.set(clientIP, { count: 1, resetTime: now + WINDOW_MS });
            next();
            return;
        }
        if (clientAttempts.count >= MAX_ATTEMPTS) {
            throw (0, errorHandler_1.createError)(429, '注册尝试过于频繁，请稍后再试', 'RATE_LIMIT_EXCEEDED');
        }
        clientAttempts.count++;
        next();
    };
})();
exports.registrationMiddleware = [
    exports.registrationRateLimit,
    ...exports.registrationValidationRules,
    exports.validateRequest,
    exports.checkUsernameAvailability,
    exports.checkEmailAvailability
];
exports.loginMiddleware = [
    ...exports.loginValidationRules,
    exports.validateRequest
];
exports.passwordResetRequestMiddleware = [
    ...exports.passwordResetRequestRules,
    exports.validateRequest
];
exports.passwordResetMiddleware = [
    ...exports.passwordResetRules,
    exports.validateRequest,
    exports.validatePasswordResetToken
];
exports.emailVerificationMiddleware = [
    ...exports.emailVerificationRules,
    exports.validateRequest,
    exports.validateEmailVerificationToken
];
//# sourceMappingURL=registration.js.map