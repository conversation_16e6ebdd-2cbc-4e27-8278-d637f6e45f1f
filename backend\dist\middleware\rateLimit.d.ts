import { Request, Response, NextFunction } from 'express';
interface RateLimitConfig {
    windowMs: number;
    maxRequests: number;
    message?: string;
    skipSuccessfulRequests?: boolean;
    skipFailedRequests?: boolean;
    keyGenerator?: (req: Request) => string;
}
export declare function createRateLimit(config: RateLimitConfig): (req: Request, res: Response, next: NextFunction) => void;
export declare const generalRateLimit: (req: Request, res: Response, next: NextFunction) => void;
export declare const authRateLimit: (req: Request, res: Response, next: NextFunction) => void;
export declare const registrationRateLimit: (req: Request, res: Response, next: NextFunction) => void;
export declare const passwordResetRateLimit: (req: Request, res: Response, next: NextFunction) => void;
export declare const emailRateLimit: (req: Request, res: Response, next: NextFunction) => void;
export declare const uploadRateLimit: (req: Request, res: Response, next: NextFunction) => void;
export declare const commentRateLimit: (req: Request, res: Response, next: NextFunction) => void;
export declare const searchRateLimit: (req: Request, res: Response, next: NextFunction) => void;
export declare const createUserBasedRateLimit: (config: Omit<RateLimitConfig, "keyGenerator">) => (req: Request, res: Response, next: NextFunction) => void;
export declare const strictUserRateLimit: (req: Request, res: Response, next: NextFunction) => void;
export declare const adminRateLimit: (req: Request, res: Response, next: NextFunction) => void;
export declare const dynamicRateLimit: (req: Request, res: Response, next: NextFunction) => void;
export declare const clearRateLimitStore: () => void;
export {};
//# sourceMappingURL=rateLimit.d.ts.map