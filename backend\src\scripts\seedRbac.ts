#!/usr/bin/env ts-node

import { dbConnection } from '../database/connection'
import { sequelize } from '../config/database'
import { up as seedRbacData, down as cleanRbacData } from '../database/seeders/011-rbac-initial-data'
import dotenv from 'dotenv'

// 加载环境变量
dotenv.config()

/**
 * RBAC系统种子数据脚本
 * 用于初始化角色权限系统的基础数据
 */
class SeedRbacScript {
  /**
   * 执行种子数据插入
   */
  async seed(): Promise<void> {
    console.log('🌱 Starting RBAC seed data insertion...')
    console.log('='.repeat(50))

    try {
      // 显示数据库连接信息
      const connectionInfo = dbConnection.getConnectionInfo()
      console.log(`📊 Database: ${connectionInfo.database}`)
      console.log(`🏠 Host: ${connectionInfo.host}:${connectionInfo.port}`)
      console.log(`👤 User: ${connectionInfo.username}`)
      console.log('')

      // 测试数据库连接
      console.log('🔍 Testing database connection...')
      const isConnected = await dbConnection.testConnection()

      if (!isConnected) {
        console.error('❌ Database connection failed!')
        process.exit(1)
      }

      console.log('✅ Database connection successful!')
      console.log('')

      // 获取QueryInterface
      const queryInterface = sequelize.getQueryInterface()

      // 执行种子数据插入
      console.log('🌱 Inserting RBAC seed data...')
      await seedRbacData(queryInterface)

      console.log('')
      console.log('='.repeat(50))
      console.log('🎉 RBAC seed data insertion completed successfully!')
      console.log('')
      console.log('📋 Created data:')
      console.log('  • 4 default roles (super_admin, admin, editor, user)')
      console.log('  • 50+ permissions covering all system resources')
      console.log('  • Role-permission assignments for each role')
      console.log('')
      console.log('🔐 Default roles and their permissions:')
      console.log('  • super_admin: All permissions')
      console.log('  • admin: Most management permissions (except system-level)')
      console.log('  • editor: Content management permissions')
      console.log('  • user: Basic read and create permissions')

    } catch (error) {
      console.error('')
      console.error('='.repeat(50))
      console.error('❌ RBAC seed data insertion failed!')
      console.error('Error details:', error)
      process.exit(1)
    }
  }

  /**
   * 清理种子数据
   */
  async clean(): Promise<void> {
    console.log('🧹 Starting RBAC seed data cleanup...')
    console.log('='.repeat(50))

    try {
      // 显示数据库连接信息
      const connectionInfo = dbConnection.getConnectionInfo()
      console.log(`📊 Database: ${connectionInfo.database}`)
      console.log(`🏠 Host: ${connectionInfo.host}:${connectionInfo.port}`)
      console.log(`👤 User: ${connectionInfo.username}`)
      console.log('')

      // 测试数据库连接
      console.log('🔍 Testing database connection...')
      const isConnected = await dbConnection.testConnection()

      if (!isConnected) {
        console.error('❌ Database connection failed!')
        process.exit(1)
      }

      console.log('✅ Database connection successful!')
      console.log('')

      // 获取QueryInterface
      const queryInterface = sequelize.getQueryInterface()

      // 执行种子数据清理
      console.log('🧹 Cleaning RBAC seed data...')
      await cleanRbacData(queryInterface)

      console.log('')
      console.log('='.repeat(50))
      console.log('🎉 RBAC seed data cleanup completed successfully!')

    } catch (error) {
      console.error('')
      console.error('='.repeat(50))
      console.error('❌ RBAC seed data cleanup failed!')
      console.error('Error details:', error)
      process.exit(1)
    }
  }

  /**
   * 显示帮助信息
   */
  showHelp(): void {
    console.log(`
📚 RBAC Seed Data Tool

Usage:
  npm run seed:rbac              Insert RBAC seed data
  npm run seed:rbac clean        Clean RBAC seed data
  ts-node src/scripts/seedRbac.ts       Insert seed data directly
  ts-node src/scripts/seedRbac.ts clean Clean seed data directly

Environment Variables:
  DB_HOST      Database host (default: localhost)
  DB_PORT      Database port (default: 3306)
  DB_NAME      Database name (default: person-blog)
  DB_USER      Database user (default: person-blog)
  DB_PASSWORD  Database password

Examples:
  npm run seed:rbac
  npm run seed:rbac clean
  NODE_ENV=production npm run seed:rbac

Note:
  Make sure to run database migrations before seeding RBAC data:
  npm run migrate
`)
  }
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  const args = process.argv.slice(2)

  // 检查是否请求帮助
  if (args.includes('--help') || args.includes('-h')) {
    const script = new SeedRbacScript()
    script.showHelp()
    return
  }

  // 检查是否是清理操作
  const isClean = args.includes('clean')

  // 执行相应操作
  const script = new SeedRbacScript()
  if (isClean) {
    await script.clean()
  } else {
    await script.seed()
  }
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error)
  process.exit(1)
})

// 执行主函数
if (require.main === module) {
  main().catch((error) => {
    console.error('Script execution failed:', error)
    process.exit(1)
  })
}
