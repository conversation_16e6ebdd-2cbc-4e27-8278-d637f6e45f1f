/*!
 * Copyright (c) 2018 <PERSON> <<EMAIL>>
 * 
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 * 
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.validator=e()}(this,function(){"use strict";function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function d(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o,a,c=[],s=!0,u=!1;try{if(o=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=o.call(n)).done)&&(c.push(r.value),c.length!==e);s=!0);}catch(t){u=!0,i=t}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw i}}return c}}(t,e)||c(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function H(t){return function(t){if(Array.isArray(t))return r(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||c(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t,e){var n;if(t)return"string"==typeof t?r(t,e):"Map"===(n="Object"===(n=Object.prototype.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(t,e):void 0}function r(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function U(t,e){var n,r,i,o,a="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(a)return r=!(n=!0),{s:function(){a=a.call(t)},n:function(){var t=a.next();return n=t.done,t},e:function(t){r=!0,i=t},f:function(){try{n||null==a.return||a.return()}finally{if(r)throw i}}};if(Array.isArray(t)||(a=c(t))||e&&t&&"number"==typeof t.length)return a&&(t=a),o=0,{s:e=function(){},n:function(){return o>=t.length?{done:!0}:{done:!1,value:t[o++]}},e:function(t){throw t},f:e};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t){var e;if(!("string"==typeof t||t instanceof String))throw e=o(t),null===t?e="null":"object"===e&&(e=t.constructor.name),new TypeError("Expected a string but received a ".concat(e))}function n(t){return u(t),t=Date.parse(t),isNaN(t)?null:new Date(t)}for(var t,i={"en-US":/^[A-Z]+$/i,"az-AZ":/^[A-VXYZÇƏĞİıÖŞÜ]+$/i,"bg-BG":/^[А-Я]+$/i,"cs-CZ":/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]+$/i,"da-DK":/^[A-ZÆØÅ]+$/i,"de-DE":/^[A-ZÄÖÜß]+$/i,"el-GR":/^[Α-ώ]+$/i,"es-ES":/^[A-ZÁÉÍÑÓÚÜ]+$/i,"fa-IR":/^[ابپتثجچحخدذرزژسشصضطظعغفقکگلمنوهی]+$/i,"fi-FI":/^[A-ZÅÄÖ]+$/i,"fr-FR":/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]+$/i,"it-IT":/^[A-ZÀÉÈÌÎÓÒÙ]+$/i,"ja-JP":/^[ぁ-んァ-ヶｦ-ﾟ一-龠ー・。、]+$/i,"nb-NO":/^[A-ZÆØÅ]+$/i,"nl-NL":/^[A-ZÁÉËÏÓÖÜÚ]+$/i,"nn-NO":/^[A-ZÆØÅ]+$/i,"hu-HU":/^[A-ZÁÉÍÓÖŐÚÜŰ]+$/i,"pl-PL":/^[A-ZĄĆĘŚŁŃÓŻŹ]+$/i,"pt-PT":/^[A-ZÃÁÀÂÄÇÉÊËÍÏÕÓÔÖÚÜ]+$/i,"ru-RU":/^[А-ЯЁ]+$/i,"kk-KZ":/^[А-ЯЁ\u04D8\u04B0\u0406\u04A2\u0492\u04AE\u049A\u04E8\u04BA]+$/i,"sl-SI":/^[A-ZČĆĐŠŽ]+$/i,"sk-SK":/^[A-ZÁČĎÉÍŇÓŠŤÚÝŽĹŔĽÄÔ]+$/i,"sr-RS@latin":/^[A-ZČĆŽŠĐ]+$/i,"sr-RS":/^[А-ЯЂЈЉЊЋЏ]+$/i,"sv-SE":/^[A-ZÅÄÖ]+$/i,"th-TH":/^[ก-๐\s]+$/i,"tr-TR":/^[A-ZÇĞİıÖŞÜ]+$/i,"uk-UA":/^[А-ЩЬЮЯЄIЇҐі]+$/i,"vi-VN":/^[A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴĐÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸ]+$/i,"ko-KR":/^[ㄱ-ㅎㅏ-ㅣ가-힣]*$/,"ku-IQ":/^[ئابپتجچحخدرڕزژسشعغفڤقکگلڵمنوۆھەیێيطؤثآإأكضصةظذ]+$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]+$/,he:/^[א-ת]+$/,fa:/^['آاءأؤئبپتثجچحخدذرزژسشصضطظعغفقکگلمنوهةی']+$/i,bn:/^['ঀঁংঃঅআইঈউঊঋঌএঐওঔকখগঘঙচছজঝঞটঠডঢণতথদধনপফবভমযরলশষসহ়ঽািীুূৃৄেৈোৌ্ৎৗড়ঢ়য়ৠৡৢৣৰৱ৲৳৴৵৶৷৸৹৺৻']+$/,eo:/^[ABCĈD-GĜHĤIJĴK-PRSŜTUŬVZ]+$/i,"hi-IN":/^[\u0900-\u0961]+[\u0972-\u097F]*$/i,"si-LK":/^[\u0D80-\u0DFF]+$/},a={"en-US":/^[0-9A-Z]+$/i,"az-AZ":/^[0-9A-VXYZÇƏĞİıÖŞÜ]+$/i,"bg-BG":/^[0-9А-Я]+$/i,"cs-CZ":/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]+$/i,"da-DK":/^[0-9A-ZÆØÅ]+$/i,"de-DE":/^[0-9A-ZÄÖÜß]+$/i,"el-GR":/^[0-9Α-ω]+$/i,"es-ES":/^[0-9A-ZÁÉÍÑÓÚÜ]+$/i,"fi-FI":/^[0-9A-ZÅÄÖ]+$/i,"fr-FR":/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]+$/i,"it-IT":/^[0-9A-ZÀÉÈÌÎÓÒÙ]+$/i,"ja-JP":/^[0-9０-９ぁ-んァ-ヶｦ-ﾟ一-龠ー・。、]+$/i,"hu-HU":/^[0-9A-ZÁÉÍÓÖŐÚÜŰ]+$/i,"nb-NO":/^[0-9A-ZÆØÅ]+$/i,"nl-NL":/^[0-9A-ZÁÉËÏÓÖÜÚ]+$/i,"nn-NO":/^[0-9A-ZÆØÅ]+$/i,"pl-PL":/^[0-9A-ZĄĆĘŚŁŃÓŻŹ]+$/i,"pt-PT":/^[0-9A-ZÃÁÀÂÄÇÉÊËÍÏÕÓÔÖÚÜ]+$/i,"ru-RU":/^[0-9А-ЯЁ]+$/i,"kk-KZ":/^[0-9А-ЯЁ\u04D8\u04B0\u0406\u04A2\u0492\u04AE\u049A\u04E8\u04BA]+$/i,"sl-SI":/^[0-9A-ZČĆĐŠŽ]+$/i,"sk-SK":/^[0-9A-ZÁČĎÉÍŇÓŠŤÚÝŽĹŔĽÄÔ]+$/i,"sr-RS@latin":/^[0-9A-ZČĆŽŠĐ]+$/i,"sr-RS":/^[0-9А-ЯЂЈЉЊЋЏ]+$/i,"sv-SE":/^[0-9A-ZÅÄÖ]+$/i,"th-TH":/^[ก-๙\s]+$/i,"tr-TR":/^[0-9A-ZÇĞİıÖŞÜ]+$/i,"uk-UA":/^[0-9А-ЩЬЮЯЄIЇҐі]+$/i,"ko-KR":/^[0-9ㄱ-ㅎㅏ-ㅣ가-힣]*$/,"ku-IQ":/^[٠١٢٣٤٥٦٧٨٩0-9ئابپتجچحخدرڕزژسشعغفڤقکگلڵمنوۆھەیێيطؤثآإأكضصةظذ]+$/i,"vi-VN":/^[0-9A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴĐÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸ]+$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]+$/,he:/^[0-9א-ת]+$/,fa:/^['0-9آاءأؤئبپتثجچحخدذرزژسشصضطظعغفقکگلمنوهةی۱۲۳۴۵۶۷۸۹۰']+$/i,bn:/^['ঀঁংঃঅআইঈউঊঋঌএঐওঔকখগঘঙচছজঝঞটঠডঢণতথদধনপফবভমযরলশষসহ়ঽািীুূৃৄেৈোৌ্ৎৗড়ঢ়য়ৠৡৢৣ০১২৩৪৫৬৭৮৯ৰৱ৲৳৴৵৶৷৸৹৺৻']+$/,eo:/^[0-9ABCĈD-GĜHĤIJĴK-PRSŜTUŬVZ]+$/i,"hi-IN":/^[\u0900-\u0963]+[\u0966-\u097F]*$/i,"si-LK":/^[0-9\u0D80-\u0DFF]+$/},s={"en-US":".",ar:"٫"},_=["AU","GB","HK","IN","NZ","ZA","ZM"],e=0;e<_.length;e++)t="en-".concat(_[e]),i[t]=i["en-US"],a[t]=a["en-US"],s[t]=s["en-US"];for(var l,b=["AE","BH","DZ","EG","IQ","JO","KW","LB","LY","MA","QM","QA","SA","SD","SY","TN","YE"],f=0;f<b.length;f++)l="ar-".concat(b[f]),i[l]=i.ar,a[l]=a.ar,s[l]=s.ar;for(var w,K=["IR","AF"],y=0;y<K.length;y++)w="fa-".concat(K[y]),a[w]=a.fa,s[w]=s.ar;for(var A,W=["BD","IN"],Y=0;Y<W.length;Y++)A="bn-".concat(W[Y]),i[A]=i.bn,a[A]=a.bn,s[A]=s["en-US"];for(var x=["ar-EG","ar-LB","ar-LY"],k=["bg-BG","cs-CZ","da-DK","de-DE","el-GR","en-ZM","eo","es-ES","fr-CA","fr-FR","id-ID","it-IT","ku-IQ","hi-IN","hu-HU","nb-NO","nn-NO","nl-NL","pl-PL","pt-PT","ru-RU","kk-KZ","si-LK","sl-SI","sr-RS@latin","sr-RS","sv-SE","tr-TR","uk-UA","vi-VN"],V=0;V<x.length;V++)s[x[V]]=s["en-US"];for(var z=0;z<k.length;z++)s[k[z]]=",";function X(t,e){u(t),e=e||{};var n,r=new RegExp("^(?:[-+])?(?:[0-9]+)?(?:\\".concat(e.locale?s[e.locale]:".","[0-9]*)?(?:[eE][\\+\\-]?(?:[0-9]+))?$"));return""!==t&&"."!==t&&","!==t&&"-"!==t&&"+"!==t&&(n=parseFloat(t.replace(",",".")),r.test(t))&&(!e.hasOwnProperty("min")||n>=e.min)&&(!e.hasOwnProperty("max")||n<=e.max)&&(!e.hasOwnProperty("lt")||n<e.lt)&&(!e.hasOwnProperty("gt")||n>e.gt)}i["fr-CA"]=i["fr-FR"],a["fr-CA"]=a["fr-FR"],i["pt-BR"]=i["pt-PT"],a["pt-BR"]=a["pt-PT"],s["pt-BR"]=s["pt-PT"],i["pl-Pl"]=i["pl-PL"],a["pl-Pl"]=a["pl-PL"],s["pl-Pl"]=s["pl-PL"],i["fa-AF"]=i.fa;var J=Object.keys(s);function j(t){return X(t)?parseFloat(t):NaN}function q(t){return"object"===o(t)&&null!==t?t="function"==typeof t.toString?t.toString():"[object Object]":(null==t||isNaN(t)&&!t.length)&&(t=""),String(t)}function $(t,e){var n,r=0<arguments.length&&void 0!==t?t:{},i=1<arguments.length?e:void 0;for(n in i)void 0===r[n]&&(r[n]=i[n]);return r}var Q={ignoreCase:!1,minOccurrences:1};function p(t,e){u(t),e="object"===o(e)?(n=e.min||0,e.max):(n=e,arguments[2]);var n,t=encodeURI(t).split(/%..|./).length-1;return n<=t&&(void 0===e||t<=e)}var tt={require_tld:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_numeric_tld:!1,allow_wildcard:!1,ignore_max_length:!1};function et(t,e){u(t),(e=$(e,tt)).allow_trailing_dot&&"."===t[t.length-1]&&(t=t.substring(0,t.length-1));var t=(t=!0===e.allow_wildcard&&0===t.indexOf("*.")?t.substring(2):t).split("."),n=t[t.length-1];if(e.require_tld){if(t.length<2)return!1;if(!e.allow_numeric_tld&&!/^([a-z\u00A1-\u00A8\u00AA-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}|xn[a-z0-9-]{2,})$/i.test(n))return!1;if(/\s/.test(n))return!1}return!(!e.allow_numeric_tld&&/^\d+$/.test(n))&&t.every(function(t){return!(63<t.length&&!e.ignore_max_length||!/^[a-z_\u00a1-\uffff0-9-]+$/i.test(t)||/[\uff01-\uff5e]/.test(t)||/^-|-$/.test(t)||!e.allow_underscores&&/_/.test(t))})}var h="(?:[0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])",h="(".concat(h,"[.]){3}").concat(h),nt=new RegExp("^".concat(h,"$")),g="(?:[0-9a-fA-F]{1,4})",rt=new RegExp("^("+"(?:".concat(g,":){7}(?:").concat(g,"|:)|")+"(?:".concat(g,":){6}(?:").concat(h,"|:").concat(g,"|:)|")+"(?:".concat(g,":){5}(?::").concat(h,"|(:").concat(g,"){1,2}|:)|")+"(?:".concat(g,":){4}(?:(:").concat(g,"){0,1}:").concat(h,"|(:").concat(g,"){1,3}|:)|")+"(?:".concat(g,":){3}(?:(:").concat(g,"){0,2}:").concat(h,"|(:").concat(g,"){1,4}|:)|")+"(?:".concat(g,":){2}(?:(:").concat(g,"){0,3}:").concat(h,"|(:").concat(g,"){1,5}|:)|")+"(?:".concat(g,":){1}(?:(:").concat(g,"){0,4}:").concat(h,"|(:").concat(g,"){1,6}|:)|")+"(?::((?::".concat(g,"){0,5}:").concat(h,"|(?::").concat(g,"){1,7}|:))")+")(%[0-9a-zA-Z-.:]{1,})?$");function S(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"";return u(t),(e=String(e))?"4"===e?nt.test(t):"6"===e&&rt.test(t):S(t,4)||S(t,6)}var it={allow_display_name:!1,allow_underscores:!1,require_display_name:!1,allow_utf8_local_part:!0,require_tld:!0,blacklisted_chars:"",ignore_max_length:!1,host_blacklist:[],host_whitelist:[]},ot=/^([^\x00-\x1F\x7F-\x9F\cX]+)</i,at=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~]+$/i,ct=/^[a-z\d]+$/,st=/^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f]))*$/i,ut=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~\u00A1-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+$/i,lt=/^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))*$/i;function dt(t,e){if(u(t),(e=$(e,it)).require_display_name||e.allow_display_name){var n=t.match(ot);if(n){n=n[1];if(t=t.replace(n,"").replace(/(^<|>$)/g,""),!function(t){var e=t.replace(/^"(.+)"$/,"$1");if(e.trim()){if(/[\.";<>]/.test(e)){if(e===t)return;if(!(e.split('"').length===e.split('\\"').length))return}return 1}}(n=n.endsWith(" ")?n.slice(0,-1):n))return!1}else if(e.require_display_name)return!1}if(!e.ignore_max_length&&254<t.length)return!1;var n=t.split("@"),t=n.pop(),r=t.toLowerCase();if(e.host_blacklist.includes(r))return!1;if(0<e.host_whitelist.length&&!e.host_whitelist.includes(r))return!1;n=n.join("@");if(e.domain_specific_validation&&("gmail.com"===r||"googlemail.com"===r)){r=(n=n.toLowerCase()).split("+")[0];if(!p(r.replace(/\./g,""),{min:6,max:30}))return!1;for(var i=r.split("."),o=0;o<i.length;o++)if(!ct.test(i[o]))return!1}if(!(!1!==e.ignore_max_length||p(n,{max:64})&&p(t,{max:254})))return!1;if(!et(t,{require_tld:e.require_tld,ignore_max_length:e.ignore_max_length,allow_underscores:e.allow_underscores})){if(!e.allow_ip_domain)return!1;if(!S(t)){if(!t.startsWith("[")||!t.endsWith("]"))return!1;r=t.slice(1,-1);if(0===r.length||!S(r))return!1}}if('"'===n[0])return n=n.slice(1,n.length-1),(e.allow_utf8_local_part?lt:st).test(n);for(var a=e.allow_utf8_local_part?ut:at,c=n.split("."),s=0;s<c.length;s++)if(!a.test(c[s]))return!1;return!e.blacklisted_chars||-1===n.search(new RegExp("[".concat(e.blacklisted_chars,"]+"),"g"))}var ft={protocols:["http","https","ftp"],require_tld:!0,require_protocol:!1,require_host:!0,require_port:!1,require_valid_protocol:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_protocol_relative_urls:!1,allow_fragments:!0,allow_query_components:!0,validate_length:!0},At=/^\[([^\]]+)\](?::([0-9]+))?$/;function $t(t,e){for(var n=0;n<e.length;n++){var r=e[n];if(t===r||"[object RegExp]"===Object.prototype.toString.call(r)&&r.test(t))return!0}return!1}var pt=/^(?:[0-9a-fA-F]{2}([-:\s]))([0-9a-fA-F]{2}\1){4}([0-9a-fA-F]{2})$/,ht=/^([0-9a-fA-F]){12}$/,gt=/^([0-9a-fA-F]{4}\.){2}([0-9a-fA-F]{4})$/,St=/^(?:[0-9a-fA-F]{2}([-:\s]))([0-9a-fA-F]{2}\1){6}([0-9a-fA-F]{2})$/,Et=/^([0-9a-fA-F]){16}$/,mt=/^([0-9a-fA-F]{4}\.){3}([0-9a-fA-F]{4})$/;var Zt=/^\d{1,3}$/;var It={format:"YYYY/MM/DD",delimiters:["/","-"],strictMode:!1};function E(e,n){if(n=$("string"==typeof n?{format:n}:n,It),"string"==typeof e&&/(^(y{4}|y{2})[.\/-](m{1,2})[.\/-](d{1,2})$)|(^(m{1,2})[.\/-](d{1,2})[.\/-]((y{4}|y{2})$))|(^(d{1,2})[.\/-](m{1,2})[.\/-]((y{4}|y{2})$))/gi.test(n.format)){var t,r=n.delimiters.find(function(t){return-1!==n.format.indexOf(t)}),i=n.strictMode?r:n.delimiters.find(function(t){return-1!==e.indexOf(t)}),o={},a=U(function(t,e){for(var n=[],r=Math.min(t.length,e.length),i=0;i<r;i++)n.push([t[i],e[i]]);return n}(e.split(i),n.format.toLowerCase().split(r)));try{for(a.s();!(t=a.n()).done;){var c=d(t.value,2),s=c[0],u=c[1];if(s.length!==u.length)return!1;o[u.charAt(0)]=s}}catch(t){a.e(t)}finally{a.f()}if((i=o.y).startsWith("-"))return!1;if(2===o.y.length){r=parseInt(o.y,10);if(isNaN(r))return!1;i=(r<(new Date).getFullYear()%100?"20":"19").concat(o.y)}var r=o.m,l=(1===o.m.length&&(r="0".concat(o.m)),o.d);return 1===o.d.length&&(l="0".concat(o.d)),new Date("".concat(i,"-").concat(r,"-").concat(l,"T00:00:00.000Z")).getUTCDate()===+o.d}return!n.strictMode&&"[object Date]"===Object.prototype.toString.call(e)&&isFinite(e)}var Rt={hourFormat:"hour24",mode:"default"},vt={hour24:{default:/^([01]?[0-9]|2[0-3]):([0-5][0-9])$/,withSeconds:/^([01]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$/},hour12:{default:/^(0?[1-9]|1[0-2]):([0-5][0-9]) (A|P)M$/,withSeconds:/^(0?[1-9]|1[0-2]):([0-5][0-9]):([0-5][0-9]) (A|P)M$/}};var Lt={loose:!1},Mt=["true","false","1","0"],Bt=[].concat(Mt,["yes","no"]);var h="(([a-zA-Z]{2,3}(-".concat("([A-Za-z]{3}(-[A-Za-z]{3}){0,2})",")?)|([a-zA-Z]{5,8}))"),g="(".concat("(\\d|[A-W]|[Y-Z]|[a-w]|[y-z])","(-[A-Za-z0-9]{2,8})+)"),m="(x(-[A-Za-z0-9]{1,8})+)",Z="(".concat("((en-GB-oed)|(i-ami)|(i-bnn)|(i-default)|(i-enochian)|(i-hak)|(i-klingon)|(i-lux)|(i-mingo)|(i-navajo)|(i-pwn)|(i-tao)|(i-tay)|(i-tsu)|(sgn-BE-FR)|(sgn-BE-NL)|(sgn-CH-DE))","|").concat("((art-lojban)|(cel-gaulish)|(no-bok)|(no-nyn)|(zh-guoyu)|(zh-hakka)|(zh-min)|(zh-min-nan)|(zh-xiang))",")"),I="(-|_)",h="".concat(h,"(").concat(I).concat("([A-Za-z]{4})",")?(").concat(I).concat("([A-Za-z]{2}|\\d{3})",")?(").concat(I).concat("([A-Za-z0-9]{5,8}|(\\d[A-Z-a-z0-9]{3}))",")*(").concat(I).concat(g,")*(").concat(I).concat(m,")?"),Ct=new RegExp("(^".concat(m,"$)|(^").concat(Z,"$)|(^").concat(h,"$)"));var Nt=/^(?!(1[3-9])|(20)|(3[3-9])|(4[0-9])|(5[0-9])|(60)|(7[3-9])|(8[1-9])|(9[0-2])|(9[3-9]))[0-9]{9}$/;g=Object.keys(i);var I=Object.keys(a),Ft=/^[0-9]+$/;var Dt={AM:/^[A-Z]{2}\d{7}$/,AR:/^[A-Z]{3}\d{6}$/,AT:/^[A-Z]\d{7}$/,AU:/^[A-Z]\d{7}$/,AZ:/^[A-Z]{1}\d{8}$/,BE:/^[A-Z]{2}\d{6}$/,BG:/^\d{9}$/,BR:/^[A-Z]{2}\d{6}$/,BY:/^[A-Z]{2}\d{7}$/,CA:/^[A-Z]{2}\d{6}$/,CH:/^[A-Z]\d{7}$/,CN:/^G\d{8}$|^E(?![IO])[A-Z0-9]\d{7}$/,CY:/^[A-Z](\d{6}|\d{8})$/,CZ:/^\d{8}$/,DE:/^[CFGHJKLMNPRTVWXYZ0-9]{9}$/,DK:/^\d{9}$/,DZ:/^\d{9}$/,EE:/^([A-Z]\d{7}|[A-Z]{2}\d{7})$/,ES:/^[A-Z0-9]{2}([A-Z0-9]?)\d{6}$/,FI:/^[A-Z]{2}\d{7}$/,FR:/^\d{2}[A-Z]{2}\d{5}$/,GB:/^\d{9}$/,GR:/^[A-Z]{2}\d{7}$/,HR:/^\d{9}$/,HU:/^[A-Z]{2}(\d{6}|\d{7})$/,IE:/^[A-Z0-9]{2}\d{7}$/,IN:/^[A-Z]{1}-?\d{7}$/,ID:/^[A-C]\d{7}$/,IR:/^[A-Z]\d{8}$/,IS:/^(A)\d{7}$/,IT:/^[A-Z0-9]{2}\d{7}$/,JM:/^[Aa]\d{7}$/,JP:/^[A-Z]{2}\d{7}$/,KR:/^[MS]\d{8}$/,KZ:/^[a-zA-Z]\d{7}$/,LI:/^[a-zA-Z]\d{5}$/,LT:/^[A-Z0-9]{8}$/,LU:/^[A-Z0-9]{8}$/,LV:/^[A-Z0-9]{2}\d{7}$/,LY:/^[A-Z0-9]{8}$/,MT:/^\d{7}$/,MZ:/^([A-Z]{2}\d{7})|(\d{2}[A-Z]{2}\d{5})$/,MY:/^[AHK]\d{8}$/,MX:/^\d{10,11}$/,NL:/^[A-Z]{2}[A-Z0-9]{6}\d$/,NZ:/^([Ll]([Aa]|[Dd]|[Ff]|[Hh])|[Ee]([Aa]|[Pp])|[Nn])\d{6}$/,PH:/^([A-Z](\d{6}|\d{7}[A-Z]))|([A-Z]{2}(\d{6}|\d{7}))$/,PK:/^[A-Z]{2}\d{7}$/,PL:/^[A-Z]{2}\d{7}$/,PT:/^[A-Z]\d{6}$/,RO:/^\d{8,9}$/,RU:/^\d{9}$/,SE:/^\d{8}$/,SL:/^(P)[A-Z]\d{7}$/,SK:/^[0-9A-Z]\d{7}$/,TH:/^[A-Z]{1,2}\d{6,7}$/,TR:/^[A-Z]\d{8}$/,UA:/^[A-Z]{2}\d{6}$/,US:/^\d{9}$/,ZA:/^[TAMD]\d{8}$/};var Tt=/^(?:[-+]?(?:0|[1-9][0-9]*))$/,Gt=/^[-+]?[0-9]+$/;function Ot(t,e){u(t);var n=!1===(e=e||{}).allow_leading_zeroes?Tt:Gt,r=!e.hasOwnProperty("min")||t>=e.min,i=!e.hasOwnProperty("max")||t<=e.max,o=!e.hasOwnProperty("lt")||t<e.lt,e=!e.hasOwnProperty("gt")||t>e.gt;return n.test(t)&&r&&i&&o&&e}var Pt=/^[0-9]{15}$/,Ht=/^\d{2}-\d{6}-\d{6}-\d{1}$/;var Ut=/^[\x00-\x7F]+$/;var _t=/[^\u0020-\u007E\uFF61-\uFF9F\uFFA0-\uFFDC\uFFE8-\uFFEE0-9a-zA-Z]/;var bt=/[\u0020-\u007E\uFF61-\uFF9F\uFFA0-\uFFDC\uFFE8-\uFFEE0-9a-zA-Z]/;var wt=/[^\x00-\x7F]/;m="i",Z=(Z=["^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)","(?:-((?:0|[1-9]\\d*|\\d*[a-z-][0-9a-z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-z-][0-9a-z-]*))*))","?(?:\\+([0-9a-z-]+(?:\\.[0-9a-z-]+)*))?$"]).join("");var Kt=new RegExp(Z,m);var yt=/[\uD800-\uDBFF][\uDC00-\uDFFF]/;var Wt={force_decimal:!1,decimal_digits:"1,",locale:"en-US"},Yt=["","-","+"];var xt=/^(0x|0h)?[0-9A-F]+$/i;function kt(t){return u(t),xt.test(t)}var Vt=/^(0o)?[0-7]+$/i;var zt=/^#?([0-9A-F]{3}|[0-9A-F]{4}|[0-9A-F]{6}|[0-9A-F]{8})$/i;var Xt=/^rgb\((([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5]),){2}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\)$/,Jt=/^rgba\((([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5]),){3}(0?\.\d|1(\.0)?|0(\.0)?)\)$/,jt=/^rgb\((([0-9]%|[1-9][0-9]%|100%),){2}([0-9]%|[1-9][0-9]%|100%)\)$/,qt=/^rgba\((([0-9]%|[1-9][0-9]%|100%),){3}(0?\.\d|1(\.0)?|0(\.0)?)\)$/;var Qt=/^hsla?\(((\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?))(deg|grad|rad|turn)?(,(\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?)%){2}(,((\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?)%?))?\)$/i,te=/^hsla?\(((\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?))(deg|grad|rad|turn)?(\s(\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?)%){2}\s?(\/\s((\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?)%?)\s?)?\)$/i;var ee=/^[A-Z]{2}[0-9A-Z]{3}\d{2}\d{5}$/;var R={AD:/^(AD[0-9]{2})\d{8}[A-Z0-9]{12}$/,AE:/^(AE[0-9]{2})\d{3}\d{16}$/,AL:/^(AL[0-9]{2})\d{8}[A-Z0-9]{16}$/,AT:/^(AT[0-9]{2})\d{16}$/,AZ:/^(AZ[0-9]{2})[A-Z0-9]{4}\d{20}$/,BA:/^(BA[0-9]{2})\d{16}$/,BE:/^(BE[0-9]{2})\d{12}$/,BG:/^(BG[0-9]{2})[A-Z]{4}\d{6}[A-Z0-9]{8}$/,BH:/^(BH[0-9]{2})[A-Z]{4}[A-Z0-9]{14}$/,BR:/^(BR[0-9]{2})\d{23}[A-Z]{1}[A-Z0-9]{1}$/,BY:/^(BY[0-9]{2})[A-Z0-9]{4}\d{20}$/,CH:/^(CH[0-9]{2})\d{5}[A-Z0-9]{12}$/,CR:/^(CR[0-9]{2})\d{18}$/,CY:/^(CY[0-9]{2})\d{8}[A-Z0-9]{16}$/,CZ:/^(CZ[0-9]{2})\d{20}$/,DE:/^(DE[0-9]{2})\d{18}$/,DK:/^(DK[0-9]{2})\d{14}$/,DO:/^(DO[0-9]{2})[A-Z]{4}\d{20}$/,DZ:/^(DZ\d{24})$/,EE:/^(EE[0-9]{2})\d{16}$/,EG:/^(EG[0-9]{2})\d{25}$/,ES:/^(ES[0-9]{2})\d{20}$/,FI:/^(FI[0-9]{2})\d{14}$/,FO:/^(FO[0-9]{2})\d{14}$/,FR:/^(FR[0-9]{2})\d{10}[A-Z0-9]{11}\d{2}$/,GB:/^(GB[0-9]{2})[A-Z]{4}\d{14}$/,GE:/^(GE[0-9]{2})[A-Z0-9]{2}\d{16}$/,GI:/^(GI[0-9]{2})[A-Z]{4}[A-Z0-9]{15}$/,GL:/^(GL[0-9]{2})\d{14}$/,GR:/^(GR[0-9]{2})\d{7}[A-Z0-9]{16}$/,GT:/^(GT[0-9]{2})[A-Z0-9]{4}[A-Z0-9]{20}$/,HR:/^(HR[0-9]{2})\d{17}$/,HU:/^(HU[0-9]{2})\d{24}$/,IE:/^(IE[0-9]{2})[A-Z0-9]{4}\d{14}$/,IL:/^(IL[0-9]{2})\d{19}$/,IQ:/^(IQ[0-9]{2})[A-Z]{4}\d{15}$/,IR:/^(IR[0-9]{2})0\d{2}0\d{18}$/,IS:/^(IS[0-9]{2})\d{22}$/,IT:/^(IT[0-9]{2})[A-Z]{1}\d{10}[A-Z0-9]{12}$/,JO:/^(JO[0-9]{2})[A-Z]{4}\d{22}$/,KW:/^(KW[0-9]{2})[A-Z]{4}[A-Z0-9]{22}$/,KZ:/^(KZ[0-9]{2})\d{3}[A-Z0-9]{13}$/,LB:/^(LB[0-9]{2})\d{4}[A-Z0-9]{20}$/,LC:/^(LC[0-9]{2})[A-Z]{4}[A-Z0-9]{24}$/,LI:/^(LI[0-9]{2})\d{5}[A-Z0-9]{12}$/,LT:/^(LT[0-9]{2})\d{16}$/,LU:/^(LU[0-9]{2})\d{3}[A-Z0-9]{13}$/,LV:/^(LV[0-9]{2})[A-Z]{4}[A-Z0-9]{13}$/,MA:/^(MA[0-9]{26})$/,MC:/^(MC[0-9]{2})\d{10}[A-Z0-9]{11}\d{2}$/,MD:/^(MD[0-9]{2})[A-Z0-9]{20}$/,ME:/^(ME[0-9]{2})\d{18}$/,MK:/^(MK[0-9]{2})\d{3}[A-Z0-9]{10}\d{2}$/,MR:/^(MR[0-9]{2})\d{23}$/,MT:/^(MT[0-9]{2})[A-Z]{4}\d{5}[A-Z0-9]{18}$/,MU:/^(MU[0-9]{2})[A-Z]{4}\d{19}[A-Z]{3}$/,MZ:/^(MZ[0-9]{2})\d{21}$/,NL:/^(NL[0-9]{2})[A-Z]{4}\d{10}$/,NO:/^(NO[0-9]{2})\d{11}$/,PK:/^(PK[0-9]{2})[A-Z0-9]{4}\d{16}$/,PL:/^(PL[0-9]{2})\d{24}$/,PS:/^(PS[0-9]{2})[A-Z0-9]{4}\d{21}$/,PT:/^(PT[0-9]{2})\d{21}$/,QA:/^(QA[0-9]{2})[A-Z]{4}[A-Z0-9]{21}$/,RO:/^(RO[0-9]{2})[A-Z]{4}[A-Z0-9]{16}$/,RS:/^(RS[0-9]{2})\d{18}$/,SA:/^(SA[0-9]{2})\d{2}[A-Z0-9]{18}$/,SC:/^(SC[0-9]{2})[A-Z]{4}\d{20}[A-Z]{3}$/,SE:/^(SE[0-9]{2})\d{20}$/,SI:/^(SI[0-9]{2})\d{15}$/,SK:/^(SK[0-9]{2})\d{20}$/,SM:/^(SM[0-9]{2})[A-Z]{1}\d{10}[A-Z0-9]{12}$/,SV:/^(SV[0-9]{2})[A-Z0-9]{4}\d{20}$/,TL:/^(TL[0-9]{2})\d{19}$/,TN:/^(TN[0-9]{2})\d{20}$/,TR:/^(TR[0-9]{2})\d{5}[A-Z0-9]{17}$/,UA:/^(UA[0-9]{2})\d{6}[A-Z0-9]{19}$/,VA:/^(VA[0-9]{2})\d{18}$/,VG:/^(VG[0-9]{2})[A-Z0-9]{4}\d{16}$/,XK:/^(XK[0-9]{2})\d{16}$/};function ne(t,e){var t=t.replace(/[\s\-]+/gi,"").toUpperCase(),n=t.slice(0,2).toUpperCase(),r=n in R;if(e.whitelist){if(0<e.whitelist.filter(function(t){return!(t in R)}).length)return!1;if(!e.whitelist.includes(n))return!1}if(e.blacklist&&e.blacklist.includes(n))return!1;return r&&R[n].test(t)}var h=Object.keys(R),re=new Set(["AD","AE","AF","AG","AI","AL","AM","AO","AQ","AR","AS","AT","AU","AW","AX","AZ","BA","BB","BD","BE","BF","BG","BH","BI","BJ","BL","BM","BN","BO","BQ","BR","BS","BT","BV","BW","BY","BZ","CA","CC","CD","CF","CG","CH","CI","CK","CL","CM","CN","CO","CR","CU","CV","CW","CX","CY","CZ","DE","DJ","DK","DM","DO","DZ","EC","EE","EG","EH","ER","ES","ET","FI","FJ","FK","FM","FO","FR","GA","GB","GD","GE","GF","GG","GH","GI","GL","GM","GN","GP","GQ","GR","GS","GT","GU","GW","GY","HK","HM","HN","HR","HT","HU","ID","IE","IL","IM","IN","IO","IQ","IR","IS","IT","JE","JM","JO","JP","KE","KG","KH","KI","KM","KN","KP","KR","KW","KY","KZ","LA","LB","LC","LI","LK","LR","LS","LT","LU","LV","LY","MA","MC","MD","ME","MF","MG","MH","MK","ML","MM","MN","MO","MP","MQ","MR","MS","MT","MU","MV","MW","MX","MY","MZ","NA","NC","NE","NF","NG","NI","NL","NO","NP","NR","NU","NZ","OM","PA","PE","PF","PG","PH","PK","PL","PM","PN","PR","PS","PT","PW","PY","QA","RE","RO","RS","RU","RW","SA","SB","SC","SD","SE","SG","SH","SI","SJ","SK","SL","SM","SN","SO","SR","SS","ST","SV","SX","SY","SZ","TC","TD","TF","TG","TH","TJ","TK","TL","TM","TN","TO","TR","TT","TV","TW","TZ","UA","UG","UM","US","UY","UZ","VA","VC","VE","VG","VI","VN","VU","WF","WS","YE","YT","ZA","ZM","ZW"]);var ie=re,oe=/^[A-Za-z]{6}[A-Za-z0-9]{2}([A-Za-z0-9]{3})?$/;var ae=/^[a-f0-9]{32}$/;var ce={md5:32,md4:32,sha1:40,sha256:64,sha384:96,sha512:128,ripemd128:32,ripemd160:40,tiger128:32,tiger160:40,tiger192:48,crc32:8,crc32b:8};var se=/[^A-Z0-9+\/=]/i,ue=/^[A-Z0-9_\-]*$/i,le={urlSafe:!1};function de(t,e){u(t),e=$(e,le);var n=t.length;return e.urlSafe?ue.test(t):n%4==0&&!se.test(t)&&(-1===(e=t.indexOf("="))||e===n-1||e===n-2&&"="===t[n-1])}var fe={allow_primitives:!1};var Ae={ignore_whitespace:!1};var $e={1:/^[0-9A-F]{8}-[0-9A-F]{4}-1[0-9A-F]{3}-[0-9A-F]{4}-[0-9A-F]{12}$/i,2:/^[0-9A-F]{8}-[0-9A-F]{4}-2[0-9A-F]{3}-[0-9A-F]{4}-[0-9A-F]{12}$/i,3:/^[0-9A-F]{8}-[0-9A-F]{4}-3[0-9A-F]{3}-[0-9A-F]{4}-[0-9A-F]{12}$/i,4:/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,5:/^[0-9A-F]{8}-[0-9A-F]{4}-5[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,7:/^[0-9A-F]{8}-[0-9A-F]{4}-7[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,all:/^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/i};function pe(t){u(t);for(var e,n,r=t.replace(/[- ]+/g,""),i=0,o=r.length-1;0<=o;o--)e=r.substring(o,o+1),e=parseInt(e,10),i+=n&&10<=(e*=2)?e%10+1:e,n=!n;return!(i%10!=0||!r)}var v={amex:/^3[47][0-9]{13}$/,dinersclub:/^3(?:0[0-5]|[68][0-9])[0-9]{11}$/,discover:/^6(?:011|5[0-9][0-9])[0-9]{12,15}$/,jcb:/^(?:2131|1800|35\d{3})\d{11}$/,mastercard:/^5[1-5][0-9]{2}|(222[1-9]|22[3-9][0-9]|2[3-6][0-9]{2}|27[01][0-9]|2720)[0-9]{12}$/,unionpay:/^(6[27][0-9]{14}|^(81[0-9]{14,17}))$/,visa:/^(?:4[0-9]{12})(?:[0-9]{3,6})?$/},he=function(){var t,e=[];for(t in v)v.hasOwnProperty(t)&&e.push(v[t]);return e}();var L={PL:function(t){u(t);var r={1:1,2:3,3:7,4:9,5:1,6:3,7:7,8:9,9:1,10:3,11:0};if(null!=t&&11===t.length&&Ot(t,{allow_leading_zeroes:!0})){var e=t.split("").slice(0,-1).reduce(function(t,e,n){return t+Number(e)*r[n+1]},0)%10,t=Number(t.charAt(t.length-1));if(0==e&&0===t||t===10-e)return!0}return!1},ES:function(t){u(t);var e,n={X:0,Y:1,Z:2},t=t.trim().toUpperCase();return!!/^[0-9X-Z][0-9]{7}[TRWAGMYFPDXBNJZSQVHLCKE]$/.test(t)&&(e=t.slice(0,-1).replace(/[X,Y,Z]/g,function(t){return n[t]}),t.endsWith(["T","R","W","A","G","M","Y","F","P","D","X","B","N","J","Z","S","Q","V","H","L","C","K","E"][e%23]))},FI:function(t){return u(t),11===t.length&&!!t.match(/^\d{6}[\-A\+]\d{3}[0-9ABCDEFHJKLMNPRSTUVWXY]{1}$/)&&"0123456789ABCDEFHJKLMNPRSTUVWXY"[(1e3*parseInt(t.slice(0,6),10)+parseInt(t.slice(7,10),10))%31]===t.slice(10,11)},IN:function(t){var n,r=[[0,1,2,3,4,5,6,7,8,9],[1,2,3,4,0,6,7,8,9,5],[2,3,4,0,1,7,8,9,5,6],[3,4,0,1,2,8,9,5,6,7],[4,0,1,2,3,9,5,6,7,8],[5,9,8,7,6,0,4,3,2,1],[6,5,9,8,7,1,0,4,3,2],[7,6,5,9,8,2,1,0,4,3],[8,7,6,5,9,3,2,1,0,4],[9,8,7,6,5,4,3,2,1,0]],i=[[0,1,2,3,4,5,6,7,8,9],[1,5,7,6,2,8,3,0,9,4],[5,8,0,3,7,9,6,1,4,2],[8,9,1,6,0,4,3,5,2,7],[9,4,5,3,1,2,6,8,7,0],[4,2,8,6,5,7,3,9,0,1],[2,7,9,3,8,0,6,4,1,5],[7,0,4,6,9,1,3,2,5,8]],t=t.trim();return!!/^[1-9]\d{3}\s?\d{4}\s?\d{4}$/.test(t)&&(n=0,t.replace(/\s/g,"").split("").map(Number).reverse().forEach(function(t,e){n=r[n][i[e%8][t]]}),0===n)},IR:function(t){if(!t.match(/^\d{10}$/))return!1;if(t="0000".concat(t).slice(t.length-6),0===parseInt(t.slice(3,9),10))return!1;for(var e=parseInt(t.slice(9,10),10),n=0,r=0;r<9;r++)n+=parseInt(t.slice(r,r+1),10)*(10-r);return(n%=11)<2&&e===n||2<=n&&e===11-n},IT:function(t){return 9===t.length&&"CA00000AA"!==t&&-1<t.search(/C[A-Z]\d{5}[A-Z]{2}/i)},NO:function(t){var e,n,t=t.trim();return!isNaN(Number(t))&&11===t.length&&"00000000000"!==t&&(e=(11-(3*(t=t.split("").map(Number))[0]+7*t[1]+6*t[2]+ +t[3]+8*t[4]+9*t[5]+4*t[6]+5*t[7]+2*t[8])%11)%11,n=(11-(5*t[0]+4*t[1]+3*t[2]+2*t[3]+7*t[4]+6*t[5]+5*t[6]+4*t[7]+3*t[8]+2*e)%11)%11,e===t[9])&&n===t[10]},TH:function(t){if(!t.match(/^[1-8]\d{12}$/))return!1;for(var e=0,n=0;n<12;n++)e+=parseInt(t[n],10)*(13-n);return t[12]===((11-e%11)%10).toString()},LK:function(t){return!(10!==t.length||!/^[1-9]\d{8}[vx]$/i.test(t))||!(12!==t.length||!/^[1-9]\d{11}$/i.test(t))},"he-IL":function(t){t=t.trim();if(!/^\d{9}$/.test(t))return!1;for(var e,n=t,r=0,i=0;i<n.length;i++)r+=9<(e=Number(n[i])*(i%2+1))?e-9:e;return r%10==0},"ar-LY":function(t){t=t.trim();return!!/^(1|2)\d{11}$/.test(t)},"ar-TN":function(t){t=t.trim();return!!/^\d{8}$/.test(t)},"zh-CN":function(t){var e,n,r=["11","12","13","14","15","21","22","23","31","32","33","34","35","36","37","41","42","43","44","45","46","50","51","52","53","54","61","62","63","64","65","71","81","82","91"],i=["7","9","10","5","8","4","2","1","6","3","7","9","10","5","8","4","2"],o=["1","0","X","9","8","7","6","5","4","3","2"],a=function(t){return r.includes(t)},c=function(t){var e=parseInt(t.substring(0,4),10),n=parseInt(t.substring(4,6),10),t=parseInt(t.substring(6),10),r=new Date(e,n-1,t);return!(r>new Date)&&r.getFullYear()===e&&r.getMonth()===n-1&&r.getDate()===t},s=function(t){return function(t){for(var e=t.substring(0,17),n=0,r=0;r<17;r++)n+=parseInt(e.charAt(r),10)*parseInt(i[r],10);return o[n%11]}(t)===t.charAt(17).toUpperCase()};return t=t,!!/^\d{15}|(\d{17}(\d|x|X))$/.test(t)&&(15===t.length?!!/^[1-9]\d{7}((0[1-9])|(1[0-2]))((0[1-9])|([1-2][0-9])|(3[0-1]))\d{3}$/.test(e=t)&&(n=e.substring(0,2),!!a(n))&&(n="19".concat(e.substring(6,12)),!!c(n)):!!/^[1-9]\d{5}[1-9]\d{3}((0[1-9])|(1[0-2]))((0[1-9])|([1-2][0-9])|(3[0-1]))\d{3}(\d|x|X)$/.test(e=t)&&(n=e.substring(0,2),!!a(n))&&(n=e.substring(6,14),!!c(n))&&s(e))},"zh-HK":function(t){var e=/^[0-9]$/;if(t=(t=t.trim()).toUpperCase(),!/^[A-Z]{1,2}[0-9]{6}((\([0-9A]\))|(\[[0-9A]\])|([0-9A]))$/.test(t))return!1;8===(t=t.replace(/\[|\]|\(|\)/g,"")).length&&(t="3".concat(t));for(var n=0,r=0;r<=7;r++)n+=(e.test(t[r])?t[r]:(t[r].charCodeAt(0)-55)%11)*(9-r);return(0===(n%=11)?"0":1===n?"A":String(11-n))===t[t.length-1]},"zh-TW":function(t){var i={A:10,B:11,C:12,D:13,E:14,F:15,G:16,H:17,I:34,J:18,K:19,L:20,M:21,N:22,O:35,P:23,Q:24,R:25,S:26,T:27,U:28,V:29,W:32,X:30,Y:31,Z:33},t=t.trim().toUpperCase();return!!/^[A-Z][0-9]{9}$/.test(t)&&Array.from(t).reduce(function(t,e,n){var r;return 0===n?(r=i[e])%10*9+Math.floor(r/10):9===n?(10-t%10-Number(e))%10==0:t+Number(e)*(9-n)},0)}};var ge=8,Se=14,Ee=/^(\d{8}|\d{13}|\d{14})$/;function me(n){var t=10-n.slice(0,-1).split("").map(function(t,e){return Number(t)*(t=n.length,e=e,t===ge||t===Se?e%2==0?3:1:e%2==0?1:3)}).reduce(function(t,e){return t+e},0)%10;return t<10?t:0}var Ze=/^[A-Z]{2}[0-9A-Z]{9}[0-9]$/;var Ie=/^(?:[0-9]{9}X|[0-9]{10})$/,Re=/^(?:[0-9]{13})$/,ve=[1,3];function Le(t){for(var e=10,n=0;n<t.length-1;n++)e=(parseInt(t[n],10)+e)%10==0?9:(parseInt(t[n],10)+e)%10*2%11;return(e=1===e?0:11-e)===parseInt(t[10],10)}function Me(t){for(var e,n=0,r=!1,i=t.length-1;0<=i;i--)n+=r?9<(e=2*parseInt(t[i],10))?e.toString().split("").map(function(t){return parseInt(t,10)}).reduce(function(t,e){return t+e},0):e:parseInt(t[i],10),r=!r;return n%10==0}function M(t,e){for(var n=0,r=0;r<t.length;r++)n+=t[r]*(e-r);return n}var Be={andover:["10","12"],atlanta:["60","67"],austin:["50","53"],brookhaven:["01","02","03","04","05","06","11","13","14","16","21","22","23","25","34","51","52","54","55","56","57","58","59","65"],cincinnati:["30","32","35","36","37","38","61"],fresno:["15","24"],internet:["20","26","27","45","46","47"],kansas:["40","44"],memphis:["94","95"],ogden:["80","90"],philadelphia:["33","39","41","42","43","46","48","62","63","64","66","68","71","72","73","74","75","76","77","81","82","83","84","85","86","87","88","91","92","93","98","99"],sba:["31"]};function Ce(t){for(var e=!1,n=!1,r=0;r<3;r++)if(!e&&/[AEIOU]/.test(t[r]))e=!0;else if(!n&&e&&"X"===t[r])n=!0;else if(0<r){if(e&&!n&&!/[AEIOU]/.test(t[r]))return;if(n&&!/X/.test(t[r]))return}return 1}var B={"bg-BG":/^\d{10}$/,"cs-CZ":/^\d{6}\/{0,1}\d{3,4}$/,"de-AT":/^\d{9}$/,"de-DE":/^[1-9]\d{10}$/,"dk-DK":/^\d{6}-{0,1}\d{4}$/,"el-CY":/^[09]\d{7}[A-Z]$/,"el-GR":/^([0-4]|[7-9])\d{8}$/,"en-CA":/^\d{9}$/,"en-GB":/^\d{10}$|^(?!GB|NK|TN|ZZ)(?![DFIQUV])[A-Z](?![DFIQUVO])[A-Z]\d{6}[ABCD ]$/i,"en-IE":/^\d{7}[A-W][A-IW]{0,1}$/i,"en-US":/^\d{2}[- ]{0,1}\d{7}$/,"es-AR":/(20|23|24|27|30|33|34)[0-9]{8}[0-9]/,"es-ES":/^(\d{0,8}|[XYZKLM]\d{7})[A-HJ-NP-TV-Z]$/i,"et-EE":/^[1-6]\d{6}(00[1-9]|0[1-9][0-9]|[1-6][0-9]{2}|70[0-9]|710)\d$/,"fi-FI":/^\d{6}[-+A]\d{3}[0-9A-FHJ-NPR-Y]$/i,"fr-BE":/^\d{11}$/,"fr-FR":/^[0-3]\d{12}$|^[0-3]\d\s\d{2}(\s\d{3}){3}$/,"fr-LU":/^\d{13}$/,"hr-HR":/^\d{11}$/,"hu-HU":/^8\d{9}$/,"it-IT":/^[A-Z]{6}[L-NP-V0-9]{2}[A-EHLMPRST][L-NP-V0-9]{2}[A-ILMZ][L-NP-V0-9]{3}[A-Z]$/i,"lv-LV":/^\d{6}-{0,1}\d{5}$/,"mt-MT":/^\d{3,7}[APMGLHBZ]$|^([1-8])\1\d{7}$/i,"nl-NL":/^\d{9}$/,"pl-PL":/^\d{10,11}$/,"pt-BR":/(?:^\d{11}$)|(?:^\d{14}$)/,"pt-PT":/^\d{9}$/,"ro-RO":/^\d{13}$/,"sk-SK":/^\d{6}\/{0,1}\d{3,4}$/,"sl-SI":/^[1-9]\d{7}$/,"sv-SE":/^(\d{6}[-+]{0,1}\d{4}|(18|19|20)\d{6}[-+]{0,1}\d{4})$/,"uk-UA":/^\d{10}$/},C=(B["lb-LU"]=B["fr-LU"],B["lt-LT"]=B["et-EE"],B["nl-BE"]=B["fr-BE"],B["fr-CA"]=B["en-CA"],{"bg-BG":function(t){var e=t.slice(0,2),n=parseInt(t.slice(2,4),10),e=(40<n?(n-=40,"20"):20<n?(n-=20,"18"):"19").concat(e);if(n<10&&(n="0".concat(n)),!E("".concat(e,"/").concat(n,"/").concat(t.slice(4,6)),"YYYY/MM/DD"))return!1;for(var r=t.split("").map(function(t){return parseInt(t,10)}),i=[2,4,8,5,10,9,7,3,6],o=0,a=0;a<i.length;a++)o+=r[a]*i[a];return(o=o%11==10?0:o%11)===r[9]},"cs-CZ":function(t){t=t.replace(/\W/,"");var e=parseInt(t.slice(0,2),10);if(10===t.length)e=(e<54?"20":"19").concat(e);else{if("000"===t.slice(6))return!1;if(!(e<54))return!1;e="19".concat(e)}3===e.length&&(e=[e.slice(0,2),"0",e.slice(2)].join(""));var n=parseInt(t.slice(2,4),10);if(50<n&&(n-=50),20<n){if(parseInt(e,10)<2004)return!1;n-=20}if(n<10&&(n="0".concat(n)),!E("".concat(e,"/").concat(n,"/").concat(t.slice(4,6)),"YYYY/MM/DD"))return!1;if(10===t.length&&parseInt(t,10)%11!=0){n=parseInt(t.slice(0,9),10)%11;if(!(parseInt(e,10)<1986&&10==n))return!1;if(0!==parseInt(t.slice(9),10))return!1}return!0},"de-AT":Me,"de-DE":function(t){for(var e=t.split("").map(function(t){return parseInt(t,10)}),n=[],r=0;r<e.length-1;r++){n.push("");for(var i=0;i<e.length-1;i++)e[r]===e[i]&&(n[r]+=i)}if(2!==(n=n.filter(function(t){return 1<t.length})).length&&3!==n.length)return!1;if(3===n[0].length){for(var o=n[0].split("").map(function(t){return parseInt(t,10)}),a=0,c=0;c<o.length-1;c++)o[c]+1===o[c+1]&&(a+=1);if(2===a)return!1}return Le(t)},"dk-DK":function(t){t=t.replace(/\W/,"");var e=parseInt(t.slice(4,6),10);switch(t.slice(6,7)){case"0":case"1":case"2":case"3":e="19".concat(e);break;case"4":case"9":e=(e<37?"20":"19").concat(e);break;default:if(e<37)e="20".concat(e);else{if(!(58<e))return!1;e="18".concat(e)}}if(3===e.length&&(e=[e.slice(0,2),"0",e.slice(2)].join("")),!E("".concat(e,"/").concat(t.slice(2,4),"/").concat(t.slice(0,2)),"YYYY/MM/DD"))return!1;for(var n=t.split("").map(function(t){return parseInt(t,10)}),r=0,i=4,o=0;o<9;o++)r+=n[o]*i,1===--i&&(i=7);return 1!=(r%=11)&&(0===r?0===n[9]:n[9]===11-r)},"el-CY":function(t){for(var e=t.slice(0,8).split("").map(function(t){return parseInt(t,10)}),n=0,r=1;r<e.length;r+=2)n+=e[r];for(var i=0;i<e.length;i+=2)e[i]<2?n+=1-e[i]:(n+=2*(e[i]-2)+5,4<e[i]&&(n+=2));return String.fromCharCode(n%26+65)===t.charAt(8)},"el-GR":function(t){for(var e=t.split("").map(function(t){return parseInt(t,10)}),n=0,r=0;r<8;r++)n+=e[r]*Math.pow(2,8-r);return n%11%10===e[8]},"en-CA":function(t){var e=(t=t.split("")).filter(function(t,e){return e%2}).map(function(t){return 2*Number(t)}).join("").split("");return t.filter(function(t,e){return!(e%2)}).concat(e).map(function(t){return Number(t)}).reduce(function(t,e){return t+e})%10==0},"en-IE":function(t){var e=M(t.split("").slice(0,7).map(function(t){return parseInt(t,10)}),8);return 9===t.length&&"W"!==t[8]&&(e+=9*(t[8].charCodeAt(0)-64)),0===(e%=23)?"W"===t[7].toUpperCase():t[7].toUpperCase()===String.fromCharCode(64+e)},"en-US":function(t){return-1!==function(){var t,e=[];for(t in Be)Be.hasOwnProperty(t)&&e.push.apply(e,H(Be[t]));return e}().indexOf(t.slice(0,2))},"es-AR":function(t){for(var e=0,n=t.split(""),t=parseInt(n.pop(),10),r=0;r<n.length;r++)e+=n[9-r]*(2+r%6);var i=11-e%11;return 11===i?i=0:10===i&&(i=9),t===i},"es-ES":function(t){var e=t.toUpperCase().split("");if(isNaN(parseInt(e[0],10))&&1<e.length){var n=0;switch(e[0]){case"Y":n=1;break;case"Z":n=2}e.splice(0,1,n)}else for(;e.length<9;)e.unshift(0);return e=e.join(""),t=parseInt(e.slice(0,8),10)%23,e[8]===["T","R","W","A","G","M","Y","F","P","D","X","B","N","J","Z","S","Q","V","H","L","C","K","E"][t]},"et-EE":function(t){var e=t.slice(1,3);switch(t.slice(0,1)){case"1":case"2":e="18".concat(e);break;case"3":case"4":e="19".concat(e);break;default:e="20".concat(e)}if(!E("".concat(e,"/").concat(t.slice(3,5),"/").concat(t.slice(5,7)),"YYYY/MM/DD"))return!1;for(var n=t.split("").map(function(t){return parseInt(t,10)}),r=0,i=1,o=0;o<10;o++)r+=n[o]*i,10===(i+=1)&&(i=1);if(r%11==10){for(var r=0,i=3,a=0;a<10;a++)r+=n[a]*i,10===(i+=1)&&(i=1);if(r%11==10)return 0===n[10]}return r%11===n[10]},"fi-FI":function(t){var e,n=t.slice(4,6);switch(t.slice(6,7)){case"+":n="18".concat(n);break;case"-":n="19".concat(n);break;default:n="20".concat(n)}return!!E("".concat(n,"/").concat(t.slice(2,4),"/").concat(t.slice(0,2)),"YYYY/MM/DD")&&((e=parseInt(t.slice(0,6)+t.slice(7,10),10)%31)<10?e===parseInt(t.slice(10),10):["A","B","C","D","E","F","H","J","K","L","M","N","P","R","S","T","U","V","W","X","Y"][e-=10]===t.slice(10))},"fr-BE":function(t){var e,n;return!!("00"===t.slice(2,4)&&"00"===t.slice(4,6)||E("".concat(t.slice(0,2),"/").concat(t.slice(2,4),"/").concat(t.slice(4,6)),"YY/MM/DD"))&&(e=97-parseInt(t.slice(0,9),10)%97,n=parseInt(t.slice(9,11),10),e===n||97-parseInt("2".concat(t.slice(0,9)),10)%97===n)},"fr-FR":function(t){return t=t.replace(/\s/g,""),parseInt(t.slice(0,10),10)%511===parseInt(t.slice(10,13),10)},"fr-LU":function(t){if(E("".concat(t.slice(0,4),"/").concat(t.slice(4,6),"/").concat(t.slice(6,8)),"YYYY/MM/DD")&&Me(t.slice(0,12))){for(var t="".concat(t.slice(0,11)).concat(t[12]),e=[[0,1,2,3,4,5,6,7,8,9],[1,2,3,4,0,6,7,8,9,5],[2,3,4,0,1,7,8,9,5,6],[3,4,0,1,2,8,9,5,6,7],[4,0,1,2,3,9,5,6,7,8],[5,9,8,7,6,0,4,3,2,1],[6,5,9,8,7,1,0,4,3,2],[7,6,5,9,8,2,1,0,4,3],[8,7,6,5,9,3,2,1,0,4],[9,8,7,6,5,4,3,2,1,0]],n=[[0,1,2,3,4,5,6,7,8,9],[1,5,7,6,2,8,3,0,9,4],[5,8,0,3,7,9,6,1,4,2],[8,9,1,6,0,4,3,5,2,7],[9,4,5,3,1,2,6,8,7,0],[4,2,8,6,5,7,3,9,0,1],[2,7,9,3,8,0,6,4,1,5],[7,0,4,6,9,1,3,2,5,8]],r=t.split("").reverse().join(""),i=0,o=0;o<r.length;o++)i=e[i][n[o%8][parseInt(r[o],10)]];return 0===i}return!1},"hr-HR":Le,"hu-HU":function(t){for(var e=t.split("").map(function(t){return parseInt(t,10)}),n=8,r=1;r<9;r++)n+=e[r]*(r+1);return n%11===e[9]},"it-IT":function(t){var e=t.toUpperCase().split("");if(!Ce(e.slice(0,3)))return!1;if(!Ce(e.slice(3,6)))return!1;for(var n={L:"0",M:"1",N:"2",P:"3",Q:"4",R:"5",S:"6",T:"7",U:"8",V:"9"},r=0,i=[6,7,9,10,12,13,14];r<i.length;r++){var o=i[r];e[o]in n&&e.splice(o,1,n[e[o]])}var t={A:"01",B:"02",C:"03",D:"04",E:"05",H:"06",L:"07",M:"08",P:"09",R:"10",S:"11",T:"12"}[e[8]],a=parseInt(e[9]+e[10],10);if(40<a&&(a-=40),a<10&&(a="0".concat(a)),!E("".concat(e[6]).concat(e[7],"/").concat(t,"/").concat(a),"YY/MM/DD"))return!1;for(var c=0,s=1;s<e.length-1;s+=2){var u=parseInt(e[s],10);c+=u=isNaN(u)?e[s].charCodeAt(0)-65:u}for(var l={A:1,B:0,C:5,D:7,E:9,F:13,G:15,H:17,I:19,J:21,K:2,L:4,M:18,N:20,O:11,P:3,Q:6,R:8,S:12,T:14,U:16,V:10,W:22,X:25,Y:24,Z:23,0:1,1:0},d=0;d<e.length-1;d+=2){var f,A=0;e[d]in l?A=l[e[d]]:(A=2*(f=parseInt(e[d],10))+1,4<f&&(A+=2)),c+=A}return String.fromCharCode(65+c%26)===e[15]},"lv-LV":function(t){var e=(t=t.replace(/\W/,"")).slice(0,2);if("32"===e)return!0;if("00"!==t.slice(2,4)){var n=t.slice(4,6);switch(t[6]){case"0":n="18".concat(n);break;case"1":n="19".concat(n);break;default:n="20".concat(n)}if(!E("".concat(n,"/").concat(t.slice(2,4),"/").concat(e),"YYYY/MM/DD"))return!1}for(var r=1101,i=[1,6,3,7,9,10,5,8,4,2],o=0;o<t.length-1;o++)r-=parseInt(t[o],10)*i[o];return parseInt(t[10],10)===r%11},"mt-MT":function(t){if(9!==t.length){for(var e=t.toUpperCase().split("");e.length<8;)e.unshift(0);switch(t[7]){case"A":case"P":if(0===parseInt(e[6],10))return!1;break;default:var n=parseInt(e.join("").slice(0,5),10);if(32e3<n)return!1;if(n===parseInt(e.join("").slice(5,7),10))return!1}}return!0},"nl-NL":function(t){return M(t.split("").slice(0,8).map(function(t){return parseInt(t,10)}),9)%11===parseInt(t[8],10)},"pl-PL":function(t){if(10===t.length){for(var e=[6,5,7,2,3,4,5,6,7],n=0,r=0;r<e.length;r++)n+=parseInt(t[r],10)*e[r];return 10===(n%=11)?!1:n===parseInt(t[9],10)}var i=t.slice(0,2),o=parseInt(t.slice(2,4),10);if(80<o?(i="18".concat(i),o-=80):60<o?(i="22".concat(i),o-=60):40<o?(i="21".concat(i),o-=40):20<o?(i="20".concat(i),o-=20):i="19".concat(i),o<10&&(o="0".concat(o)),!E("".concat(i,"/").concat(o,"/").concat(t.slice(4,6)),"YYYY/MM/DD"))return!1;for(var a=0,c=1,s=0;s<t.length-1;s++)a+=parseInt(t[s],10)*c%10,10<(c+=2)?c=1:5===c&&(c+=2);return(a=10-a%10)===parseInt(t[10],10)},"pt-BR":function(t){if(11===t.length){var e=0;if("11111111111"===t||"22222222222"===t||"33333333333"===t||"44444444444"===t||"55555555555"===t||"66666666666"===t||"77777777777"===t||"88888888888"===t||"99999999999"===t||"00000000000"===t)return!1;for(var n=1;n<=9;n++)e+=parseInt(t.substring(n-1,n),10)*(11-n);if((a=10===(a=10*e%11)?0:a)!==parseInt(t.substring(9,10),10))return!1;e=0;for(var r=1;r<=10;r++)e+=parseInt(t.substring(r-1,r),10)*(12-r);return(a=10===(a=10*e%11)?0:a)!==parseInt(t.substring(10,11),10)?!1:!0}if("00000000000000"===t||"11111111111111"===t||"22222222222222"===t||"33333333333333"===t||"44444444444444"===t||"55555555555555"===t||"66666666666666"===t||"77777777777777"===t||"88888888888888"===t||"99999999999999"===t)return!1;for(var i=t.length-2,o=t.substring(0,i),a=t.substring(i),c=0,s=i-7,u=i;1<=u;u--)c+=o.charAt(i-u)*s,--s<2&&(s=9);if((c%11<2?0:11-c%11)!==parseInt(a.charAt(0),10))return!1;for(var o=t.substring(0,i+=1),c=0,s=i-7,l=i;1<=l;l--)c+=o.charAt(i-l)*s,--s<2&&(s=9);return(c%11<2?0:11-c%11)===parseInt(a.charAt(1),10)},"pt-PT":function(t){var e=11-M(t.split("").slice(0,8).map(function(t){return parseInt(t,10)}),9)%11;return 9<e?0===parseInt(t[8],10):e===parseInt(t[8],10)},"ro-RO":function(t){if("9000"===t.slice(0,4))return!0;var e=t.slice(1,3);switch(t[0]){case"1":case"2":e="19".concat(e);break;case"3":case"4":e="18".concat(e);break;case"5":case"6":e="20".concat(e)}var n="".concat(e,"/").concat(t.slice(3,5),"/").concat(t.slice(5,7));if(8===n.length){if(!E(n,"YY/MM/DD"))return!1}else if(!E(n,"YYYY/MM/DD"))return!1;for(var r=t.split("").map(function(t){return parseInt(t,10)}),i=[2,7,9,1,4,6,3,5,8,2,7,9],o=0,a=0;a<i.length;a++)o+=r[a]*i[a];return o%11==10?1===r[12]:r[12]===o%11},"sk-SK":function(t){if(9===t.length){if("000"===(t=t.replace(/\W/,"")).slice(6))return!1;if(53<(e=parseInt(t.slice(0,2),10)))return!1;var e=(e<10?"190":"19").concat(e),n=parseInt(t.slice(2,4),10);if(50<n&&(n-=50),n<10&&(n="0".concat(n)),!E("".concat(e,"/").concat(n,"/").concat(t.slice(4,6)),"YYYY/MM/DD"))return!1}return!0},"sl-SI":function(t){var e=11-M(t.split("").slice(0,7).map(function(t){return parseInt(t,10)}),8)%11;return 10==e?0===parseInt(t[7],10):e===parseInt(t[7],10)},"sv-SE":function(t){var e=t.slice(0),n="",r=(e=11<t.length?e.slice(2):e).slice(2,4),e=parseInt(e.slice(4,6),10);if(11<t.length)n=t.slice(0,4);else if(n=t.slice(0,2),11===t.length&&e<60){var i=(new Date).getFullYear().toString(),o=parseInt(i.slice(0,2),10),i=parseInt(i,10);if("-"===t[6])n=(parseInt("".concat(o).concat(n),10)>i?"".concat(o-1):"".concat(o)).concat(n);else if(n="".concat(o-1).concat(n),i-parseInt(n,10)<100)return!1}if(60<e&&(e-=60),e<10&&(e="0".concat(e)),8===(o="".concat(n,"/").concat(r,"/").concat(e)).length){if(!E(o,"YY/MM/DD"))return!1}else if(!E(o,"YYYY/MM/DD"))return!1;return Me(t.replace(/\W/,""))},"uk-UA":function(t){for(var e=t.split("").map(function(t){return parseInt(t,10)}),n=[-1,5,7,9,4,6,10,5,7],r=0,i=0;i<n.length;i++)r+=e[i]*n[i];return r%11==10?0===e[9]:e[9]===r%11}}),Z=(C["lb-LU"]=C["fr-LU"],C["lt-LT"]=C["et-EE"],C["nl-BE"]=C["fr-BE"],C["fr-CA"]=C["en-CA"],/[-\\\/!@#$%\^&\*\(\)\+\=\[\]]+/g),N={"de-AT":Z,"de-DE":/[\/\\]/g,"fr-BE":Z};N["nl-BE"]=N["fr-BE"];var F={"am-AM":/^(\+?374|0)(33|4[134]|55|77|88|9[13-689])\d{6}$/,"ar-AE":/^((\+?971)|0)?5[024568]\d{7}$/,"ar-BH":/^(\+?973)?(3|6)\d{7}$/,"ar-DZ":/^(\+?213|0)(5|6|7)\d{8}$/,"ar-LB":/^(\+?961)?((3|81)\d{6}|7\d{7})$/,"ar-EG":/^((\+?20)|0)?1[0125]\d{8}$/,"ar-IQ":/^(\+?964|0)?7[0-9]\d{8}$/,"ar-JO":/^(\+?962|0)?7[789]\d{7}$/,"ar-KW":/^(\+?965)([569]\d{7}|41\d{6})$/,"ar-LY":/^((\+?218)|0)?(9[1-6]\d{7}|[1-8]\d{7,9})$/,"ar-MA":/^(?:(?:\+|00)212|0)[5-7]\d{8}$/,"ar-OM":/^((\+|00)968)?(9[1-9])\d{6}$/,"ar-PS":/^(\+?970|0)5[6|9](\d{7})$/,"ar-SA":/^(!?(\+?966)|0)?5\d{8}$/,"ar-SD":/^((\+?249)|0)?(9[012369]|1[012])\d{7}$/,"ar-SY":/^(!?(\+?963)|0)?9\d{8}$/,"ar-TN":/^(\+?216)?[2459]\d{7}$/,"az-AZ":/^(\+994|0)(10|5[015]|7[07]|99)\d{7}$/,"bs-BA":/^((((\+|00)3876)|06))((([0-3]|[5-6])\d{6})|(4\d{7}))$/,"be-BY":/^(\+?375)?(24|25|29|33|44)\d{7}$/,"bg-BG":/^(\+?359|0)?8[789]\d{7}$/,"bn-BD":/^(\+?880|0)1[13456789][0-9]{8}$/,"ca-AD":/^(\+376)?[346]\d{5}$/,"cs-CZ":/^(\+?420)? ?[1-9][0-9]{2} ?[0-9]{3} ?[0-9]{3}$/,"da-DK":/^(\+?45)?\s?\d{2}\s?\d{2}\s?\d{2}\s?\d{2}$/,"de-DE":/^((\+49|0)1)(5[0-25-9]\d|6([23]|0\d?)|7([0-57-9]|6\d))\d{7,9}$/,"de-AT":/^(\+43|0)\d{1,4}\d{3,12}$/,"de-CH":/^(\+41|0)([1-9])\d{1,9}$/,"de-LU":/^(\+352)?((6\d1)\d{6})$/,"dv-MV":/^(\+?960)?(7[2-9]|9[1-9])\d{5}$/,"el-GR":/^(\+?30|0)?6(8[5-9]|9(?![26])[0-9])\d{7}$/,"el-CY":/^(\+?357?)?(9(9|6)\d{6})$/,"en-AI":/^(\+?1|0)264(?:2(35|92)|4(?:6[1-2]|76|97)|5(?:3[6-9]|8[1-4])|7(?:2(4|9)|72))\d{4}$/,"en-AU":/^(\+?61|0)4\d{8}$/,"en-AG":/^(?:\+1|1)268(?:464|7(?:1[3-9]|[28]\d|3[0246]|64|7[0-689]))\d{4}$/,"en-BM":/^(\+?1)?441(((3|7)\d{6}$)|(5[0-3][0-9]\d{4}$)|(59\d{5}$))/,"en-BS":/^(\+?1[-\s]?|0)?\(?242\)?[-\s]?\d{3}[-\s]?\d{4}$/,"en-GB":/^(\+?44|0)7\d{9}$/,"en-GG":/^(\+?44|0)1481\d{6}$/,"en-GH":/^(\+233|0)(20|50|24|54|27|57|26|56|23|28|55|59)\d{7}$/,"en-GY":/^(\+592|0)6\d{6}$/,"en-HK":/^(\+?852[-\s]?)?[456789]\d{3}[-\s]?\d{4}$/,"en-MO":/^(\+?853[-\s]?)?[6]\d{3}[-\s]?\d{4}$/,"en-IE":/^(\+?353|0)8[356789]\d{7}$/,"en-IN":/^(\+?91|0)?[6789]\d{9}$/,"en-JM":/^(\+?876)?\d{7}$/,"en-KE":/^(\+?254|0)(7|1)\d{8}$/,"fr-CF":/^(\+?236| ?)(70|75|77|72|21|22)\d{6}$/,"en-SS":/^(\+?211|0)(9[1257])\d{7}$/,"en-KI":/^((\+686|686)?)?( )?((6|7)(2|3|8)[0-9]{6})$/,"en-KN":/^(?:\+1|1)869(?:46\d|48[89]|55[6-8]|66\d|76[02-7])\d{4}$/,"en-LS":/^(\+?266)(22|28|57|58|59|27|52)\d{6}$/,"en-MT":/^(\+?356|0)?(99|79|77|21|27|22|25)[0-9]{6}$/,"en-MU":/^(\+?230|0)?\d{8}$/,"en-MW":/^(\+?265|0)(((77|88|31|99|98|21)\d{7})|(((111)|1)\d{6})|(32000\d{4}))$/,"en-NA":/^(\+?264|0)(6|8)\d{7}$/,"en-NG":/^(\+?234|0)?[789]\d{9}$/,"en-NZ":/^(\+?64|0)[28]\d{7,9}$/,"en-PG":/^(\+?675|0)?(7\d|8[18])\d{6}$/,"en-PK":/^((00|\+)?92|0)3[0-6]\d{8}$/,"en-PH":/^(09|\+639)\d{9}$/,"en-RW":/^(\+?250|0)?[7]\d{8}$/,"en-SG":/^(\+65)?[3689]\d{7}$/,"en-SL":/^(\+?232|0)\d{8}$/,"en-TZ":/^(\+?255|0)?[67]\d{8}$/,"en-UG":/^(\+?256|0)?[7]\d{8}$/,"en-US":/^((\+1|1)?( |-)?)?(\([2-9][0-9]{2}\)|[2-9][0-9]{2})( |-)?([2-9][0-9]{2}( |-)?[0-9]{4})$/,"en-ZA":/^(\+?27|0)\d{9}$/,"en-ZM":/^(\+?26)?09[567]\d{7}$/,"en-ZW":/^(\+263)[0-9]{9}$/,"en-BW":/^(\+?267)?(7[1-8]{1})\d{6}$/,"es-AR":/^\+?549(11|[2368]\d)\d{8}$/,"es-BO":/^(\+?591)?(6|7)\d{7}$/,"es-CO":/^(\+?57)?3(0(0|1|2|4|5)|1\d|2[0-4]|5(0|1))\d{7}$/,"es-CL":/^(\+?56|0)[2-9]\d{1}\d{7}$/,"es-CR":/^(\+506)?[2-8]\d{7}$/,"es-CU":/^(\+53|0053)?5\d{7}$/,"es-DO":/^(\+?1)?8[024]9\d{7}$/,"es-HN":/^(\+?504)?[9|8|3|2]\d{7}$/,"es-EC":/^(\+?593|0)([2-7]|9[2-9])\d{7}$/,"es-ES":/^(\+?34)?[6|7]\d{8}$/,"es-PE":/^(\+?51)?9\d{8}$/,"es-MX":/^(\+?52)?(1|01)?\d{10,11}$/,"es-NI":/^(\+?505)\d{7,8}$/,"es-PA":/^(\+?507)\d{7,8}$/,"es-PY":/^(\+?595|0)9[9876]\d{7}$/,"es-SV":/^(\+?503)?[67]\d{7}$/,"es-UY":/^(\+598|0)9[1-9][\d]{6}$/,"es-VE":/^(\+?58)?(2|4)\d{9}$/,"et-EE":/^(\+?372)?\s?(5|8[1-4])\s?([0-9]\s?){6,7}$/,"fa-IR":/^(\+?98[\-\s]?|0)9[0-39]\d[\-\s]?\d{3}[\-\s]?\d{4}$/,"fi-FI":/^(\+?358|0)\s?(4[0-6]|50)\s?(\d\s?){4,8}$/,"fj-FJ":/^(\+?679)?\s?\d{3}\s?\d{4}$/,"fo-FO":/^(\+?298)?\s?\d{2}\s?\d{2}\s?\d{2}$/,"fr-BF":/^(\+226|0)[67]\d{7}$/,"fr-BJ":/^(\+229)\d{8}$/,"fr-CD":/^(\+?243|0)?(8|9)\d{8}$/,"fr-CM":/^(\+?237)6[0-9]{8}$/,"fr-FR":/^(\+?33|0)[67]\d{8}$/,"fr-GF":/^(\+?594|0|00594)[67]\d{8}$/,"fr-GP":/^(\+?590|0|00590)[67]\d{8}$/,"fr-MQ":/^(\+?596|0|00596)[67]\d{8}$/,"fr-PF":/^(\+?689)?8[789]\d{6}$/,"fr-RE":/^(\+?262|0|00262)[67]\d{8}$/,"fr-WF":/^(\+681)?\d{6}$/,"he-IL":/^(\+972|0)([23489]|5[012345689]|77)[1-9]\d{6}$/,"hu-HU":/^(\+?36|06)(20|30|31|50|70)\d{7}$/,"id-ID":/^(\+?62|0)8(1[123456789]|2[1238]|3[1238]|5[12356789]|7[78]|9[56789]|8[123456789])([\s?|\d]{5,11})$/,"ir-IR":/^(\+98|0)?9\d{9}$/,"it-IT":/^(\+?39)?\s?3\d{2} ?\d{6,7}$/,"it-SM":/^((\+378)|(0549)|(\+390549)|(\+3780549))?6\d{5,9}$/,"ja-JP":/^(\+81[ \-]?(\(0\))?|0)[6789]0[ \-]?\d{4}[ \-]?\d{4}$/,"ka-GE":/^(\+?995)?(79\d{7}|5\d{8})$/,"kk-KZ":/^(\+?7|8)?7\d{9}$/,"kl-GL":/^(\+?299)?\s?\d{2}\s?\d{2}\s?\d{2}$/,"ko-KR":/^((\+?82)[ \-]?)?0?1([0|1|6|7|8|9]{1})[ \-]?\d{3,4}[ \-]?\d{4}$/,"ky-KG":/^(\+?7\s?\+?7|0)\s?\d{2}\s?\d{3}\s?\d{4}$/,"lt-LT":/^(\+370|8)\d{8}$/,"lv-LV":/^(\+?371)2\d{7}$/,"mg-MG":/^((\+?261|0)(2|3)\d)?\d{7}$/,"mn-MN":/^(\+|00|011)?976(77|81|88|91|94|95|96|99)\d{6}$/,"my-MM":/^(\+?959|09|9)(2[5-7]|3[1-2]|4[0-5]|6[6-9]|7[5-9]|9[6-9])[0-9]{7}$/,"ms-MY":/^(\+?60|0)1(([0145](-|\s)?\d{7,8})|([236-9](-|\s)?\d{7}))$/,"mz-MZ":/^(\+?258)?8[234567]\d{7}$/,"nb-NO":/^(\+?47)?[49]\d{7}$/,"ne-NP":/^(\+?977)?9[78]\d{8}$/,"nl-BE":/^(\+?32|0)4\d{8}$/,"nl-NL":/^(((\+|00)?31\(0\))|((\+|00)?31)|0)6{1}\d{8}$/,"nl-AW":/^(\+)?297(56|59|64|73|74|99)\d{5}$/,"nn-NO":/^(\+?47)?[49]\d{7}$/,"pl-PL":/^(\+?48)? ?([5-8]\d|45) ?\d{3} ?\d{2} ?\d{2}$/,"pt-BR":/^((\+?55\ ?[1-9]{2}\ ?)|(\+?55\ ?\([1-9]{2}\)\ ?)|(0[1-9]{2}\ ?)|(\([1-9]{2}\)\ ?)|([1-9]{2}\ ?))((\d{4}\-?\d{4})|(9[1-9]{1}\d{3}\-?\d{4}))$/,"pt-PT":/^(\+?351)?9[1236]\d{7}$/,"pt-AO":/^(\+244)\d{9}$/,"ro-MD":/^(\+?373|0)((6(0|1|2|6|7|8|9))|(7(6|7|8|9)))\d{6}$/,"ro-RO":/^(\+?40|0)\s?7\d{2}(\/|\s|\.|-)?\d{3}(\s|\.|-)?\d{3}$/,"ru-RU":/^(\+?7|8)?9\d{9}$/,"si-LK":/^(?:0|94|\+94)?(7(0|1|2|4|5|6|7|8)( |-)?)\d{7}$/,"sl-SI":/^(\+386\s?|0)(\d{1}\s?\d{3}\s?\d{2}\s?\d{2}|\d{2}\s?\d{3}\s?\d{3})$/,"sk-SK":/^(\+?421)? ?[1-9][0-9]{2} ?[0-9]{3} ?[0-9]{3}$/,"so-SO":/^(\+?252|0)((6[0-9])\d{7}|(7[1-9])\d{7})$/,"sq-AL":/^(\+355|0)6[789]\d{6}$/,"sr-RS":/^(\+3816|06)[- \d]{5,9}$/,"sv-SE":/^(\+?46|0)[\s\-]?7[\s\-]?[02369]([\s\-]?\d){7}$/,"tg-TJ":/^(\+?992)?[5][5]\d{7}$/,"th-TH":/^(\+66|66|0)\d{9}$/,"tr-TR":/^(\+?90|0)?5\d{9}$/,"tk-TM":/^(\+993|993|8)\d{8}$/,"uk-UA":/^(\+?38|8)?0\d{9}$/,"uz-UZ":/^(\+?998)?(6[125-79]|7[1-69]|88|9\d)\d{7}$/,"vi-VN":/^((\+?84)|0)((3([2-9]))|(5([25689]))|(7([0|6-9]))|(8([1-9]))|(9([0-9])))([0-9]{7})$/,"zh-CN":/^((\+|00)86)?(1[3-9]|9[28])\d{9}$/,"zh-TW":/^(\+?886\-?|0)?9\d{8}$/,"dz-BT":/^(\+?975|0)?(17|16|77|02)\d{6}$/,"ar-YE":/^(((\+|00)9677|0?7)[0137]\d{7}|((\+|00)967|0)[1-7]\d{6})$/,"ar-EH":/^(\+?212|0)[\s\-]?(5288|5289)[\s\-]?\d{5}$/,"fa-AF":/^(\+93|0)?(2{1}[0-8]{1}|[3-5]{1}[0-4]{1})(\d{7})$/};F["en-CA"]=F["en-US"],F["fr-CA"]=F["en-CA"],F["fr-BE"]=F["nl-BE"],F["zh-HK"]=F["en-HK"],F["zh-MO"]=F["en-MO"],F["ga-IE"]=F["en-IE"],F["fr-CH"]=F["de-CH"],F["it-CH"]=F["fr-CH"];var m=Object.keys(F),Ne=/^(0x)[0-9a-f]{40}$/i;var Fe={symbol:"$",require_symbol:!1,allow_space_after_symbol:!1,symbol_after_digits:!1,allow_negatives:!0,parens_for_negatives:!1,negative_sign_before_digits:!1,negative_sign_after_digits:!1,allow_negative_sign_placeholder:!1,thousands_separator:",",decimal_separator:".",allow_decimal:!0,require_decimal:!1,digits_after_decimal:[2],allow_space_after_digits:!1};var De=/^(bc1)[a-z0-9]{25,39}$/,Te=/^(1|3)[A-HJ-NP-Za-km-z1-9]{25,39}$/;var Ge=/^[A-Z]{3}(U[0-9]{7})|([J,Z][0-9]{6,7})$/,Oe=/^[0-9]$/;function Pe(t){if(u(t),t=t.toUpperCase(),!Ge.test(t))return!1;if(11!==t.length)return!0;for(var e,n=0,r=0;r<t.length-1;r++)Oe.test(t[r])?n+=t[r]*Math.pow(2,r):n+=((e=t.charCodeAt(r)-55)<11?e:11<=e&&e<=20?12+e%11:21<=e&&e<=30?23+e%21:34+e%31)*Math.pow(2,r);var i=n%11;return Number(t[t.length-1])===i}var Z=Pe,He=new Set(["aa","ab","ae","af","ak","am","an","ar","as","av","ay","az","az","ba","be","bg","bh","bi","bm","bn","bo","br","bs","ca","ce","ch","co","cr","cs","cu","cv","cy","da","de","dv","dz","ee","el","en","eo","es","et","eu","fa","ff","fi","fj","fo","fr","fy","ga","gd","gl","gn","gu","gv","ha","he","hi","ho","hr","ht","hu","hy","hz","ia","id","ie","ig","ii","ik","io","is","it","iu","ja","jv","ka","kg","ki","kj","kk","kl","km","kn","ko","kr","ks","ku","kv","kw","ky","la","lb","lg","li","ln","lo","lt","lu","lv","mg","mh","mi","mk","ml","mn","mr","ms","mt","my","na","nb","nd","ne","ng","nl","nn","no","nr","nv","ny","oc","oj","om","or","os","pa","pi","pl","ps","pt","qu","rm","rn","ro","ru","rw","sa","sc","sd","se","sg","si","sk","sl","sm","sn","so","sq","sr","ss","st","su","sv","sw","ta","te","tg","th","ti","tk","tl","tn","to","tr","ts","tt","tw","ty","ug","uk","ur","uz","ve","vi","vo","wa","wo","xh","yi","yo","za","zh","zu"]);var Ue=/^([\+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-3])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T\s]((([01]\d|2[0-3])((:?)[0-5]\d)?|24:?00)([\.,]\d+(?!:))?)?(\17[0-5]\d([\.,]\d+)?)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/,_e=/^([\+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-3])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T]((([01]\d|2[0-3])((:?)[0-5]\d)?|24:?00)([\.,]\d+(?!:))?)?(\17[0-5]\d([\.,]\d+)?)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/;var D=/([01][0-9]|2[0-3])/,T=/[0-5][0-9]/,G=new RegExp("[-+]".concat(D.source,":").concat(T.source)),G=new RegExp("([zZ]|".concat(G.source,")")),D=new RegExp("".concat(D.source,":").concat(T.source,":").concat(/([0-5][0-9]|60)/.source).concat(/(\.[0-9]+)?/.source)),T=new RegExp("".concat(/[0-9]{4}/.source,"-").concat(/(0[1-9]|1[0-2])/.source,"-").concat(/([12]\d|0[1-9]|3[01])/.source)),D=new RegExp("".concat(D.source).concat(G.source)),be=new RegExp("^".concat(T.source,"[ tT]").concat(D.source,"$"));var we=new Set(["AFG","ALA","ALB","DZA","ASM","AND","AGO","AIA","ATA","ATG","ARG","ARM","ABW","AUS","AUT","AZE","BHS","BHR","BGD","BRB","BLR","BEL","BLZ","BEN","BMU","BTN","BOL","BES","BIH","BWA","BVT","BRA","IOT","BRN","BGR","BFA","BDI","KHM","CMR","CAN","CPV","CYM","CAF","TCD","CHL","CHN","CXR","CCK","COL","COM","COG","COD","COK","CRI","CIV","HRV","CUB","CUW","CYP","CZE","DNK","DJI","DMA","DOM","ECU","EGY","SLV","GNQ","ERI","EST","ETH","FLK","FRO","FJI","FIN","FRA","GUF","PYF","ATF","GAB","GMB","GEO","DEU","GHA","GIB","GRC","GRL","GRD","GLP","GUM","GTM","GGY","GIN","GNB","GUY","HTI","HMD","VAT","HND","HKG","HUN","ISL","IND","IDN","IRN","IRQ","IRL","IMN","ISR","ITA","JAM","JPN","JEY","JOR","KAZ","KEN","KIR","PRK","KOR","KWT","KGZ","LAO","LVA","LBN","LSO","LBR","LBY","LIE","LTU","LUX","MAC","MKD","MDG","MWI","MYS","MDV","MLI","MLT","MHL","MTQ","MRT","MUS","MYT","MEX","FSM","MDA","MCO","MNG","MNE","MSR","MAR","MOZ","MMR","NAM","NRU","NPL","NLD","NCL","NZL","NIC","NER","NGA","NIU","NFK","MNP","NOR","OMN","PAK","PLW","PSE","PAN","PNG","PRY","PER","PHL","PCN","POL","PRT","PRI","QAT","REU","ROU","RUS","RWA","BLM","SHN","KNA","LCA","MAF","SPM","VCT","WSM","SMR","STP","SAU","SEN","SRB","SYC","SLE","SGP","SXM","SVK","SVN","SLB","SOM","ZAF","SGS","SSD","ESP","LKA","SDN","SUR","SJM","SWZ","SWE","CHE","SYR","TWN","TJK","TZA","THA","TLS","TGO","TKL","TON","TTO","TUN","TUR","TKM","TCA","TUV","UGA","UKR","ARE","GBR","USA","UMI","URY","UZB","VUT","VEN","VNM","VGB","VIR","WLF","ESH","YEM","ZMB","ZWE"]);var Ke=new Set(["AED","AFN","ALL","AMD","ANG","AOA","ARS","AUD","AWG","AZN","BAM","BBD","BDT","BGN","BHD","BIF","BMD","BND","BOB","BOV","BRL","BSD","BTN","BWP","BYN","BZD","CAD","CDF","CHE","CHF","CHW","CLF","CLP","CNY","COP","COU","CRC","CUC","CUP","CVE","CZK","DJF","DKK","DOP","DZD","EGP","ERN","ETB","EUR","FJD","FKP","GBP","GEL","GHS","GIP","GMD","GNF","GTQ","GYD","HKD","HNL","HRK","HTG","HUF","IDR","ILS","INR","IQD","IRR","ISK","JMD","JOD","JPY","KES","KGS","KHR","KMF","KPW","KRW","KWD","KYD","KZT","LAK","LBP","LKR","LRD","LSL","LYD","MAD","MDL","MGA","MKD","MMK","MNT","MOP","MRU","MUR","MVR","MWK","MXN","MXV","MYR","MZN","NAD","NGN","NIO","NOK","NPR","NZD","OMR","PAB","PEN","PGK","PHP","PKR","PLN","PYG","QAR","RON","RSD","RUB","RWF","SAR","SBD","SCR","SDG","SEK","SGD","SHP","SLE","SLL","SOS","SRD","SSP","STN","SVC","SYP","SZL","THB","TJS","TMT","TND","TOP","TRY","TTD","TWD","TZS","UAH","UGX","USD","USN","UYI","UYU","UYW","UZS","VES","VND","VUV","WST","XAF","XAG","XAU","XBA","XBB","XBC","XBD","XCD","XDR","XOF","XPD","XPF","XPT","XSU","XTS","XUA","XXX","YER","ZAR","ZMW","ZWL"]);var ye=/^[A-Z2-7]+=*$/,We=/^[A-HJKMNP-TV-Z0-9]+$/,Ye={crockford:!1};var xe=/^[A-HJ-NP-Za-km-z1-9]*$/;var ke=/^[a-z]+\/[a-z0-9\-\+\._]+$/i,Ve=/^[a-z\-]+=[a-z0-9\-]+$/i,ze=/^[a-z0-9!\$&'\(\)\*\+,;=\-\._~:@\/\?%\s]*$/i;var Xe=/(?:^magnet:\?|[^?&]&)xt(?:\.1)?=urn:(?:(?:aich|bitprint|btih|ed2k|ed2khash|kzhash|md5|sha1|tree:tiger):[a-z0-9]{32}(?:[a-z0-9]{8})?|btmh:1220[a-z0-9]{64})(?:$|&)/i;function Je(t,e){if(u(t),e)return e=new RegExp("[".concat(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"]+$"),"g"),t.replace(e,"");for(var n=t.length-1;/\s/.test(t.charAt(n));)--n;return t.slice(0,n+1)}function je(t,e){u(t);e=e?new RegExp("^[".concat(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"]+"),"g"):/^\s+/g;return t.replace(e,"")}function qe(t,e){return Je(je(t,e),e)}var Qe=/^(application|audio|font|image|message|model|multipart|text|video)\/[a-zA-Z0-9\.\-\+_]{1,100}$/i,tn=/^text\/[a-zA-Z0-9\.\-\+]{1,100};\s?charset=("[a-zA-Z0-9\.\-\+\s]{0,70}"|[a-zA-Z0-9\.\-\+]{0,70})(\s?\([a-zA-Z0-9\.\-\+\s]{1,20}\))?$/i,en=/^multipart\/[a-zA-Z0-9\.\-\+]{1,100}(;\s?(boundary|charset)=("[a-zA-Z0-9\.\-\+\s]{0,70}"|[a-zA-Z0-9\.\-\+]{0,70})(\s?\([a-zA-Z0-9\.\-\+\s]{1,20}\))?){0,2}$/i;var nn=/^\(?[+-]?(90(\.0+)?|[1-8]?\d(\.\d+)?)$/,rn=/^\s?[+-]?(180(\.0+)?|1[0-7]\d(\.\d+)?|\d{1,2}(\.\d+)?)\)?$/,on=/^(([1-8]?\d)\D+([1-5]?\d|60)\D+([1-5]?\d|60)(\.\d+)?|90\D+0\D+0)\D+[NSns]?$/i,an=/^\s*([1-7]?\d{1,2}\D+([1-5]?\d|60)\D+([1-5]?\d|60)(\.\d+)?|180\D+0\D+0)\D+[EWew]?$/i,cn={checkDMS:!1};var G=/^\d{3}$/,T=/^\d{4}$/,D=/^\d{5}$/,sn=/^\d{6}$/,O={AD:/^AD\d{3}$/,AT:T,AU:T,AZ:/^AZ\d{4}$/,BA:/^([7-8]\d{4}$)/,BE:T,BG:T,BR:/^\d{5}-\d{3}$/,BY:/^2[1-4]\d{4}$/,CA:/^[ABCEGHJKLMNPRSTVXY]\d[ABCEGHJ-NPRSTV-Z][\s\-]?\d[ABCEGHJ-NPRSTV-Z]\d$/i,CH:T,CN:/^(0[1-7]|1[012356]|2[0-7]|3[0-6]|4[0-7]|5[1-7]|6[1-7]|7[1-5]|8[1345]|9[09])\d{4}$/,CZ:/^\d{3}\s?\d{2}$/,DE:D,DK:T,DO:D,DZ:D,EE:D,ES:/^(5[0-2]{1}|[0-4]{1}\d{1})\d{3}$/,FI:D,FR:/^\d{2}\s?\d{3}$/,GB:/^(gir\s?0aa|[a-z]{1,2}\d[\da-z]?\s?(\d[a-z]{2})?)$/i,GR:/^\d{3}\s?\d{2}$/,HR:/^([1-5]\d{4}$)/,HT:/^HT\d{4}$/,HU:T,ID:D,IE:/^(?!.*(?:o))[A-Za-z]\d[\dw]\s\w{4}$/i,IL:/^(\d{5}|\d{7})$/,IN:/^((?!10|29|35|54|55|65|66|86|87|88|89)[1-9][0-9]{5})$/,IR:/^(?!(\d)\1{3})[13-9]{4}[1346-9][013-9]{5}$/,IS:G,IT:D,JP:/^\d{3}\-\d{4}$/,KE:D,KR:/^(\d{5}|\d{6})$/,LI:/^(948[5-9]|949[0-7])$/,LT:/^LT\-\d{5}$/,LU:T,LV:/^LV\-\d{4}$/,LK:D,MG:G,MX:D,MT:/^[A-Za-z]{3}\s{0,1}\d{4}$/,MY:D,NL:/^[1-9]\d{3}\s?(?!sa|sd|ss)[a-z]{2}$/i,NO:T,NP:/^(10|21|22|32|33|34|44|45|56|57)\d{3}$|^(977)$/i,NZ:T,PL:/^\d{2}\-\d{3}$/,PR:/^00[679]\d{2}([ -]\d{4})?$/,PT:/^\d{4}\-\d{3}?$/,RO:sn,RU:sn,SA:D,SE:/^[1-9]\d{2}\s?\d{2}$/,SG:sn,SI:T,SK:/^\d{3}\s?\d{2}$/,TH:D,TN:T,TW:/^\d{3}(\d{2})?$/,UA:D,US:/^\d{5}(-\d{4})?$/,ZA:T,ZM:D},G=Object.keys(O);function un(t,e){return u(t),t.replace(new RegExp("[".concat(e,"]+"),"g"),"")}var ln={all_lowercase:!0,gmail_lowercase:!0,gmail_remove_dots:!0,gmail_remove_subaddress:!0,gmail_convert_googlemaildotcom:!0,outlookdotcom_lowercase:!0,outlookdotcom_remove_subaddress:!0,yahoo_lowercase:!0,yahoo_remove_subaddress:!0,yandex_lowercase:!0,icloud_lowercase:!0,icloud_remove_subaddress:!0},dn=["icloud.com","me.com"],fn=["hotmail.at","hotmail.be","hotmail.ca","hotmail.cl","hotmail.co.il","hotmail.co.nz","hotmail.co.th","hotmail.co.uk","hotmail.com","hotmail.com.ar","hotmail.com.au","hotmail.com.br","hotmail.com.gr","hotmail.com.mx","hotmail.com.pe","hotmail.com.tr","hotmail.com.vn","hotmail.cz","hotmail.de","hotmail.dk","hotmail.es","hotmail.fr","hotmail.hu","hotmail.id","hotmail.ie","hotmail.in","hotmail.it","hotmail.jp","hotmail.kr","hotmail.lv","hotmail.my","hotmail.ph","hotmail.pt","hotmail.sa","hotmail.sg","hotmail.sk","live.be","live.co.uk","live.com","live.com.ar","live.com.mx","live.de","live.es","live.eu","live.fr","live.it","live.nl","msn.com","outlook.at","outlook.be","outlook.cl","outlook.co.il","outlook.co.nz","outlook.co.th","outlook.com","outlook.com.ar","outlook.com.au","outlook.com.br","outlook.com.gr","outlook.com.pe","outlook.com.tr","outlook.com.vn","outlook.cz","outlook.de","outlook.dk","outlook.es","outlook.fr","outlook.hu","outlook.id","outlook.ie","outlook.in","outlook.it","outlook.jp","outlook.kr","outlook.lv","outlook.my","outlook.ph","outlook.pt","outlook.sa","outlook.sg","outlook.sk","passport.com"],An=["rocketmail.com","yahoo.ca","yahoo.co.uk","yahoo.com","yahoo.de","yahoo.fr","yahoo.in","yahoo.it","ymail.com"],$n=["yandex.ru","yandex.ua","yandex.kz","yandex.com","yandex.by","ya.ru"];function pn(t){return 1<t.length?t:""}var hn=/^[^\s-_](?!.*?[-_]{2,})[a-z0-9-\\][^\s]*[^-_\s]$/;var P={"cs-CZ":function(t){return/^(([ABCDEFHIJKLMNPRSTUVXYZ]|[0-9])-?){5,8}$/.test(t)},"de-DE":function(t){return/^((A|AA|AB|AC|AE|AH|AK|AM|AN|AÖ|AP|AS|AT|AU|AW|AZ|B|BA|BB|BC|BE|BF|BH|BI|BK|BL|BM|BN|BO|BÖ|BS|BT|BZ|C|CA|CB|CE|CO|CR|CW|D|DA|DD|DE|DH|DI|DL|DM|DN|DO|DU|DW|DZ|E|EA|EB|ED|EE|EF|EG|EH|EI|EL|EM|EN|ER|ES|EU|EW|F|FB|FD|FF|FG|FI|FL|FN|FO|FR|FS|FT|FÜ|FW|FZ|G|GA|GC|GD|GE|GF|GG|GI|GK|GL|GM|GN|GÖ|GP|GR|GS|GT|GÜ|GV|GW|GZ|H|HA|HB|HC|HD|HE|HF|HG|HH|HI|HK|HL|HM|HN|HO|HP|HR|HS|HU|HV|HX|HY|HZ|IK|IL|IN|IZ|J|JE|JL|K|KA|KB|KC|KE|KF|KG|KH|KI|KK|KL|KM|KN|KO|KR|KS|KT|KU|KW|KY|L|LA|LB|LC|LD|LF|LG|LH|LI|LL|LM|LN|LÖ|LP|LR|LU|M|MA|MB|MC|MD|ME|MG|MH|MI|MK|ML|MM|MN|MO|MQ|MR|MS|MÜ|MW|MY|MZ|N|NB|ND|NE|NF|NH|NI|NK|NM|NÖ|NP|NR|NT|NU|NW|NY|NZ|OA|OB|OC|OD|OE|OF|OG|OH|OK|OL|OP|OS|OZ|P|PA|PB|PE|PF|PI|PL|PM|PN|PR|PS|PW|PZ|R|RA|RC|RD|RE|RG|RH|RI|RL|RM|RN|RO|RP|RS|RT|RU|RV|RW|RZ|S|SB|SC|SE|SG|SI|SK|SL|SM|SN|SO|SP|SR|ST|SU|SW|SY|SZ|TE|TF|TG|TO|TP|TR|TS|TT|TÜ|ÜB|UE|UH|UL|UM|UN|V|VB|VG|VK|VR|VS|W|WA|WB|WE|WF|WI|WK|WL|WM|WN|WO|WR|WS|WT|WÜ|WW|WZ|Z|ZE|ZI|ZP|ZR|ZW|ZZ)[- ]?[A-Z]{1,2}[- ]?\d{1,4}|(ABG|ABI|AIB|AIC|ALF|ALZ|ANA|ANG|ANK|APD|ARN|ART|ASL|ASZ|AUR|AZE|BAD|BAR|BBG|BCH|BED|BER|BGD|BGL|BID|BIN|BIR|BIT|BIW|BKS|BLB|BLK|BNA|BOG|BOH|BOR|BOT|BRA|BRB|BRG|BRK|BRL|BRV|BSB|BSK|BTF|BÜD|BUL|BÜR|BÜS|BÜZ|CAS|CHA|CLP|CLZ|COC|COE|CUX|DAH|DAN|DAU|DBR|DEG|DEL|DGF|DIL|DIN|DIZ|DKB|DLG|DON|DUD|DÜW|EBE|EBN|EBS|ECK|EIC|EIL|EIN|EIS|EMD|EMS|ERB|ERH|ERK|ERZ|ESB|ESW|FDB|FDS|FEU|FFB|FKB|FLÖ|FOR|FRG|FRI|FRW|FTL|FÜS|GAN|GAP|GDB|GEL|GEO|GER|GHA|GHC|GLA|GMN|GNT|GOA|GOH|GRA|GRH|GRI|GRM|GRZ|GTH|GUB|GUN|GVM|HAB|HAL|HAM|HAS|HBN|HBS|HCH|HDH|HDL|HEB|HEF|HEI|HER|HET|HGN|HGW|HHM|HIG|HIP|HMÜ|HOG|HOH|HOL|HOM|HOR|HÖS|HOT|HRO|HSK|HST|HVL|HWI|IGB|ILL|JÜL|KEH|KEL|KEM|KIB|KLE|KLZ|KÖN|KÖT|KÖZ|KRU|KÜN|KUS|KYF|LAN|LAU|LBS|LBZ|LDK|LDS|LEO|LER|LEV|LIB|LIF|LIP|LÖB|LOS|LRO|LSZ|LÜN|LUP|LWL|MAB|MAI|MAK|MAL|MED|MEG|MEI|MEK|MEL|MER|MET|MGH|MGN|MHL|MIL|MKK|MOD|MOL|MON|MOS|MSE|MSH|MSP|MST|MTK|MTL|MÜB|MÜR|MYK|MZG|NAB|NAI|NAU|NDH|NEA|NEB|NEC|NEN|NES|NEW|NMB|NMS|NOH|NOL|NOM|NOR|NVP|NWM|OAL|OBB|OBG|OCH|OHA|ÖHR|OHV|OHZ|OPR|OSL|OVI|OVL|OVP|PAF|PAN|PAR|PCH|PEG|PIR|PLÖ|PRÜ|QFT|QLB|RDG|REG|REH|REI|RID|RIE|ROD|ROF|ROK|ROL|ROS|ROT|ROW|RSL|RÜD|RÜG|SAB|SAD|SAN|SAW|SBG|SBK|SCZ|SDH|SDL|SDT|SEB|SEE|SEF|SEL|SFB|SFT|SGH|SHA|SHG|SHK|SHL|SIG|SIM|SLE|SLF|SLK|SLN|SLS|SLÜ|SLZ|SMÜ|SOB|SOG|SOK|SÖM|SON|SPB|SPN|SRB|SRO|STA|STB|STD|STE|STL|SUL|SÜW|SWA|SZB|TBB|TDO|TET|TIR|TÖL|TUT|UEM|UER|UFF|USI|VAI|VEC|VER|VIB|VIE|VIT|VOH|WAF|WAK|WAN|WAR|WAT|WBS|WDA|WEL|WEN|WER|WES|WHV|WIL|WIS|WIT|WIZ|WLG|WMS|WND|WOB|WOH|WOL|WOR|WOS|WRN|WSF|WST|WSW|WTL|WTM|WUG|WÜM|WUN|WUR|WZL|ZEL|ZIG)[- ]?(([A-Z][- ]?\d{1,4})|([A-Z]{2}[- ]?\d{1,3})))[- ]?(E|H)?$/.test(t)},"de-LI":function(t){return/^FL[- ]?\d{1,5}[UZ]?$/.test(t)},"en-IN":function(t){return/^[A-Z]{2}[ -]?[0-9]{1,2}(?:[ -]?[A-Z])(?:[ -]?[A-Z]*)?[ -]?[0-9]{4}$/.test(t)},"es-AR":function(t){return/^(([A-Z]{2} ?[0-9]{3} ?[A-Z]{2})|([A-Z]{3} ?[0-9]{3}))$/.test(t)},"fi-FI":function(t){return/^(?=.{4,7})(([A-Z]{1,3}|[0-9]{1,3})[\s-]?([A-Z]{1,3}|[0-9]{1,5}))$/.test(t)},"hu-HU":function(t){return/^((((?!AAA)(([A-NPRSTVZWXY]{1})([A-PR-Z]{1})([A-HJ-NPR-Z]))|(A[ABC]I)|A[ABC]O|A[A-W]Q|BPI|BPO|UCO|UDO|XAO)-(?!000)\d{3})|(M\d{6})|((CK|DT|CD|HC|H[ABEFIKLMNPRSTVX]|MA|OT|R[A-Z]) \d{2}-\d{2})|(CD \d{3}-\d{3})|(C-(C|X) \d{4})|(X-(A|B|C) \d{4})|(([EPVZ]-\d{5}))|(S A[A-Z]{2} \d{2})|(SP \d{2}-\d{2}))$/.test(t)},"pt-BR":function(t){return/^[A-Z]{3}[ -]?[0-9][A-Z][0-9]{2}|[A-Z]{3}[ -]?[0-9]{4}$/.test(t)},"pt-PT":function(t){return/^([A-Z]{2}|[0-9]{2})[ -·]?([A-Z]{2}|[0-9]{2})[ -·]?([A-Z]{2}|[0-9]{2})$/.test(t)},"sq-AL":function(t){return/^[A-Z]{2}[- ]?((\d{3}[- ]?(([A-Z]{2})|T))|(R[- ]?\d{3}))$/.test(t)},"sv-SE":function(t){return/^[A-HJ-PR-UW-Z]{3} ?[\d]{2}[A-HJ-PR-UW-Z1-9]$|(^[A-ZÅÄÖ ]{2,7}$)/.test(t.trim())},"en-PK":function(t){return/(^[A-Z]{2}((\s|-){0,1})[0-9]{3,4}((\s|-)[0-9]{2}){0,1}$)|(^[A-Z]{3}((\s|-){0,1})[0-9]{3,4}((\s|-)[0-9]{2}){0,1}$)|(^[A-Z]{4}((\s|-){0,1})[0-9]{3,4}((\s|-)[0-9]{2}){0,1}$)|(^[A-Z]((\s|-){0,1})[0-9]{4}((\s|-)[0-9]{2}){0,1}$)/.test(t.trim())}};var gn=/^[A-Z]$/,Sn=/^[a-z]$/,En=/^[0-9]$/,mn=/^[-#!$@£%^&*()_+|~=`{}\[\]:";'<>?,.\/\\ ]$/,Zn={minLength:8,minLowercase:1,minUppercase:1,minNumbers:1,minSymbols:1,returnScore:!1,pointsPerUnique:1,pointsPerRepeat:.5,pointsForContainingLower:10,pointsForContainingUpper:10,pointsForContainingNumber:10,pointsForContainingSymbol:10};function In(t){e={},Array.from(t).forEach(function(t){e[t]?e[t]+=1:e[t]=1});var e,n=e,r={length:t.length,uniqueChars:Object.keys(n).length,uppercaseCount:0,lowercaseCount:0,numberCount:0,symbolCount:0};return Object.keys(n).forEach(function(t){gn.test(t)?r.uppercaseCount+=n[t]:Sn.test(t)?r.lowercaseCount+=n[t]:En.test(t)?r.numberCount+=n[t]:mn.test(t)&&(r.symbolCount+=n[t])}),r}var Rn={AT:function(t){return/^(AT)?U\d{8}$/.test(t)},BE:function(t){return/^(BE)?\d{10}$/.test(t)},BG:function(t){return/^(BG)?\d{9,10}$/.test(t)},HR:function(t){return/^(HR)?\d{11}$/.test(t)},CY:function(t){return/^(CY)?\w{9}$/.test(t)},CZ:function(t){return/^(CZ)?\d{8,10}$/.test(t)},DK:function(t){return/^(DK)?\d{8}$/.test(t)},EE:function(t){return/^(EE)?\d{9}$/.test(t)},FI:function(t){return/^(FI)?\d{8}$/.test(t)},FR:function(t){return/^(FR)?\w{2}\d{9}$/.test(t)},DE:function(t){return/^(DE)?\d{9}$/.test(t)},EL:function(t){return/^(EL)?\d{9}$/.test(t)},HU:function(t){return/^(HU)?\d{8}$/.test(t)},IE:function(t){return/^(IE)?\d{7}\w{1}(W)?$/.test(t)},IT:function(t){return/^(IT)?\d{11}$/.test(t)},LV:function(t){return/^(LV)?\d{11}$/.test(t)},LT:function(t){return/^(LT)?\d{9,12}$/.test(t)},LU:function(t){return/^(LU)?\d{8}$/.test(t)},MT:function(t){return/^(MT)?\d{8}$/.test(t)},NL:function(t){return/^(NL)?\d{9}B\d{2}$/.test(t)},PL:function(t){return/^(PL)?(\d{10}|(\d{3}-\d{3}-\d{2}-\d{2})|(\d{3}-\d{2}-\d{2}-\d{3}))$/.test(t)},PT:function(t){var e,t=t.match(/^(PT)?(\d{9})$/);return!!t&&(9<(e=11-M((t=t[2]).split("").slice(0,8).map(function(t){return parseInt(t,10)}),9)%11)?0===parseInt(t[8],10):e===parseInt(t[8],10))},RO:function(t){return/^(RO)?\d{2,10}$/.test(t)},SK:function(t){return/^(SK)?\d{10}$/.test(t)},SI:function(t){return/^(SI)?\d{8}$/.test(t)},ES:function(t){return/^(ES)?\w\d{7}[A-Z]$/.test(t)},SE:function(t){return/^(SE)?\d{12}$/.test(t)},AL:function(t){return/^(AL)?\w{9}[A-Z]$/.test(t)},MK:function(t){return/^(MK)?\d{13}$/.test(t)},AU:function(t){if(!t.match(/^(AU)?(\d{11})$/))return!1;for(var e=[10,1,3,5,7,9,11,13,15,17,19],n=(t=t.replace(/^AU/,""),(parseInt(t.slice(0,1),10)-1).toString()+t.slice(1)),r=0,i=0;i<11;i++)r+=e[i]*n.charAt(i);return 0!==r&&r%89==0},BY:function(t){return/^(УНП )?\d{9}$/.test(t)},CA:function(t){return/^(CA)?\d{9}$/.test(t)},IS:function(t){return/^(IS)?\d{5,6}$/.test(t)},IN:function(t){return/^(IN)?\d{15}$/.test(t)},ID:function(t){return/^(ID)?(\d{15}|(\d{2}.\d{3}.\d{3}.\d{1}-\d{3}.\d{3}))$/.test(t)},IL:function(t){return/^(IL)?\d{9}$/.test(t)},KZ:function(t){return/^(KZ)?\d{12}$/.test(t)},NZ:function(t){return/^(NZ)?\d{9}$/.test(t)},NG:function(t){return/^(NG)?(\d{12}|(\d{8}-\d{4}))$/.test(t)},NO:function(t){return/^(NO)?\d{9}MVA$/.test(t)},PH:function(t){return/^(PH)?(\d{12}|\d{3} \d{3} \d{3} \d{3})$/.test(t)},RU:function(t){return/^(RU)?(\d{10}|\d{12})$/.test(t)},SM:function(t){return/^(SM)?\d{5}$/.test(t)},SA:function(t){return/^(SA)?\d{15}$/.test(t)},RS:function(t){return/^(RS)?\d{9}$/.test(t)},CH:function(t){var e,r;return/^(CHE[- ]?)?(\d{9}|(\d{3}\.\d{3}\.\d{3})|(\d{3} \d{3} \d{3})) ?(TVA|MWST|IVA)?$/.test(t)&&(t=t.match(/\d/g).map(function(t){return+t}),e=t.pop(),r=[5,4,3,2,7,6,5,4],e===(11-t.reduce(function(t,e,n){return t+e*r[n]},0)%11)%11)},TR:function(t){return/^(TR)?\d{10}$/.test(t)},UA:function(t){return/^(UA)?\d{12}$/.test(t)},GB:function(t){return/^GB((\d{3} \d{4} ([0-8][0-9]|9[0-6]))|(\d{9} \d{3})|(((GD[0-4])|(HA[5-9]))[0-9]{2}))$/.test(t)},UZ:function(t){return/^(UZ)?\d{9}$/.test(t)},AR:function(t){return/^(AR)?\d{11}$/.test(t)},BO:function(t){return/^(BO)?\d{7}$/.test(t)},BR:function(t){return/^(BR)?((\d{2}.\d{3}.\d{3}\/\d{4}-\d{2})|(\d{3}.\d{3}.\d{3}-\d{2}))$/.test(t)},CL:function(t){return/^(CL)?\d{8}-\d{1}$/.test(t)},CO:function(t){return/^(CO)?\d{10}$/.test(t)},CR:function(t){return/^(CR)?\d{9,12}$/.test(t)},EC:function(t){return/^(EC)?\d{13}$/.test(t)},SV:function(t){return/^(SV)?\d{4}-\d{6}-\d{3}-\d{1}$/.test(t)},GT:function(t){return/^(GT)?\d{7}-\d{1}$/.test(t)},HN:function(t){return/^(HN)?$/.test(t)},MX:function(t){return/^(MX)?\w{3,4}\d{6}\w{3}$/.test(t)},NI:function(t){return/^(NI)?\d{3}-\d{6}-\d{4}\w{1}$/.test(t)},PA:function(t){return/^(PA)?$/.test(t)},PY:function(t){return/^(PY)?\d{6,8}-\d{1}$/.test(t)},PE:function(t){return/^(PE)?\d{11}$/.test(t)},DO:function(t){return/^(DO)?(\d{11}|(\d{3}-\d{7}-\d{1})|[1,4,5]{1}\d{8}|([1,4,5]{1})-\d{2}-\d{5}-\d{1})$/.test(t)},UY:function(t){return/^(UY)?\d{12}$/.test(t)},VE:function(t){return/^(VE)?[J,G,V,E]{1}-(\d{9}|(\d{8}-\d{1}))$/.test(t)}};return{version:"13.12.0",toDate:n,toFloat:j,toInt:function(t,e){return u(t),parseInt(t,e||10)},toBoolean:function(t,e){return u(t),e?"1"===t||/^true$/i.test(t):"0"!==t&&!/^false$/i.test(t)&&""!==t},equals:function(t,e){return u(t),t===e},contains:function(t,e,n){return u(t),(n=$(n,Q)).ignoreCase?t.toLowerCase().split(q(e).toLowerCase()).length>n.minOccurrences:t.split(q(e)).length>n.minOccurrences},matches:function(t,e,n){return u(t),"[object RegExp]"!==Object.prototype.toString.call(e)&&(e=new RegExp(e,n)),!!t.match(e)},isEmail:dt,isURL:function(t,e){if(u(t),!t||/[\s<>]/.test(t))return!1;if(0===t.indexOf("mailto:"))return!1;if((e=$(e,ft)).validate_length&&2083<=t.length)return!1;if(!e.allow_fragments&&t.includes("#"))return!1;if(!e.allow_query_components&&(t.includes("?")||t.includes("&")))return!1;var n,r=t.split("#");if(1<(r=(t=(r=(t=r.shift()).split("?")).shift()).split("://")).length){if(i=r.shift().toLowerCase(),e.require_valid_protocol&&-1===e.protocols.indexOf(i))return!1}else{if(e.require_protocol)return!1;if("//"===t.slice(0,2)){if(!e.allow_protocol_relative_urls)return!1;r[0]=t.slice(2)}}if(""===(t=r.join("://")))return!1;if(""===(t=(r=t.split("/")).shift())&&!e.require_host)return!0;if(1<(r=t.split("@")).length){if(e.disallow_auth)return!1;if(""===r[0])return!1;if(0<=(i=r.shift()).indexOf(":")&&2<i.split(":").length)return!1;var t=d(i.split(":"),2),i=t[0],t=t[1];if(""===i&&""===t)return!1}var o,i=null,t=null,a=(o=r.join("@")).match(At);if(a?(n="",t=a[1],i=a[2]||null):(n=(r=o.split(":")).shift(),r.length&&(i=r.join(":"))),null!==i&&0<i.length){if(a=parseInt(i,10),!/^[0-9]+$/.test(i)||a<=0||65535<a)return!1}else if(e.require_port)return!1;return e.host_whitelist?$t(n,e.host_whitelist):""===n&&!e.require_host||!!(S(n)||et(n,e)||t&&S(t,6))&&(n=n||t,!e.host_blacklist||!$t(n,e.host_blacklist))},isMACAddress:function t(e,n){return u(e),null!=n&&n.eui&&(n.eui=String(n.eui)),null!=n&&n.no_colons||null!=n&&n.no_separators?"48"===n.eui?ht.test(e):"64"!==n.eui&&ht.test(e)||Et.test(e):"48"===(null==n?void 0:n.eui)?pt.test(e)||gt.test(e):"64"===(null==n?void 0:n.eui)?St.test(e)||mt.test(e):t(e,{eui:"48"})||t(e,{eui:"64"})},isIP:S,isIPRange:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"",n=(u(t),t.split("/"));if(2!==n.length)return!1;if(!Zt.test(n[1]))return!1;if(1<n[1].length&&n[1].startsWith("0"))return!1;if(!S(n[0],e))return!1;var r=null;switch(String(e)){case"4":r=32;break;case"6":r=128;break;default:r=S(n[0],"6")?128:32}return n[1]<=r&&0<=n[1]},isFQDN:et,isBoolean:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:Lt;return u(t),e.loose?Bt.includes(t.toLowerCase()):Mt.includes(t)},isIBAN:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};return u(t),ne(t,e)&&1===((e=(e=t).replace(/[^A-Z0-9]+/gi,"").toUpperCase()).slice(4)+e.slice(0,4)).replace(/[A-Z]/g,function(t){return t.charCodeAt(0)-55}).match(/\d{1,7}/g).reduce(function(t,e){return Number(t+e)%97},"")},isBIC:function(t){u(t);var e=t.slice(4,6).toUpperCase();return!(!ie.has(e)&&"XK"!==e)&&oe.test(t)},isAbaRouting:function(t){if(u(t),!Nt.test(t))return!1;for(var e=0,n=0;n<t.length;n++)e+=n%3==0?3*t[n]:n%3==1?7*t[n]:+t[n];return e%10==0},isAlpha:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"en-US",n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};if(u(t),n=n.ignore)if(n instanceof RegExp)t=t.replace(n,"");else{if("string"!=typeof n)throw new Error("ignore should be instance of a String or RegExp");t=t.replace(new RegExp("[".concat(n.replace(/[-[\]{}()*+?.,\\^$|#\\s]/g,"\\$&"),"]"),"g"),"")}if(e in i)return i[e].test(t);throw new Error("Invalid locale '".concat(e,"'"))},isAlphaLocales:g,isAlphanumeric:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"en-US",n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};if(u(t),n=n.ignore)if(n instanceof RegExp)t=t.replace(n,"");else{if("string"!=typeof n)throw new Error("ignore should be instance of a String or RegExp");t=t.replace(new RegExp("[".concat(n.replace(/[-[\]{}()*+?.,\\^$|#\\s]/g,"\\$&"),"]"),"g"),"")}if(e in a)return a[e].test(t);throw new Error("Invalid locale '".concat(e,"'"))},isAlphanumericLocales:I,isNumeric:function(t,e){return u(t),(e&&e.no_symbols?Ft:new RegExp("^[+-]?([0-9]*[".concat((e||{}).locale?s[e.locale]:".","])?[0-9]+$"))).test(t)},isPassportNumber:function(t,e){return u(t),t=t.replace(/\s/g,"").toUpperCase(),e.toUpperCase()in Dt&&Dt[e].test(t)},isPort:function(t){return Ot(t,{allow_leading_zeroes:!1,min:0,max:65535})},isLowercase:function(t){return u(t),t===t.toLowerCase()},isUppercase:function(t){return u(t),t===t.toUpperCase()},isAscii:function(t){return u(t),Ut.test(t)},isFullWidth:function(t){return u(t),_t.test(t)},isHalfWidth:function(t){return u(t),bt.test(t)},isVariableWidth:function(t){return u(t),_t.test(t)&&bt.test(t)},isMultibyte:function(t){return u(t),wt.test(t)},isSemVer:function(t){return u(t),Kt.test(t)},isSurrogatePair:function(t){return u(t),yt.test(t)},isInt:Ot,isIMEI:function(t,e){u(t);var n=Pt;if(!(n=(e=e||{}).allow_hyphens?Ht:n).test(t))return!1;t=t.replace(/-/g,"");for(var r=0,i=2,o=0;o<14;o++){var a=t.substring(14-o-1,14-o),a=parseInt(a,10)*i;r+=10<=a?a%10+1:a,1===i?i+=1:--i}return(10-r%10)%10===parseInt(t.substring(14,15),10)},isFloat:X,isFloatLocales:J,isDecimal:function(t,e){if(u(t),(e=$(e,Wt)).locale in s)return n=Yt,r=t.replace(/ /g,""),!n.some(function(t){return r===t})&&(n=e,new RegExp("^[-+]?([0-9]+)?(\\".concat(s[n.locale],"[0-9]{").concat(n.decimal_digits,"})").concat(n.force_decimal?"":"?","$")).test(t));var n,r;throw new Error("Invalid locale '".concat(e.locale,"'"))},isHexadecimal:kt,isOctal:function(t){return u(t),Vt.test(t)},isDivisibleBy:function(t,e){return u(t),j(t)%parseInt(e,10)==0},isHexColor:function(t){return u(t),zt.test(t)},isRgbColor:function(t){var e=!(1<arguments.length&&void 0!==arguments[1])||arguments[1];return u(t),e?Xt.test(t)||Jt.test(t)||jt.test(t)||qt.test(t):Xt.test(t)||Jt.test(t)},isHSL:function(t){return u(t),(-1!==(t=t.replace(/\s+/g," ").replace(/\s?(hsla?\(|\)|,)\s?/gi,"$1")).indexOf(",")?Qt:te).test(t)},isISRC:function(t){return u(t),ee.test(t)},isMD5:function(t){return u(t),ae.test(t)},isHash:function(t,e){return u(t),new RegExp("^[a-fA-F0-9]{".concat(ce[e],"}$")).test(t)},isJWT:function(t){return u(t),3===(t=t.split(".")).length&&t.reduce(function(t,e){return t&&de(e,{urlSafe:!0})},!0)},isJSON:function(t,e){u(t);try{e=$(e,fe);var n=[],r=(e.allow_primitives&&(n=[null,!1,!0]),JSON.parse(t));return n.includes(r)||!!r&&"object"===o(r)}catch(t){}return!1},isEmpty:function(t,e){return u(t),0===((e=$(e,Ae)).ignore_whitespace?t.trim():t).length},isLength:function(t,e){u(t),e="object"===o(e)?(n=e.min||0,e.max):(n=e||0,arguments[2]);var n,r=t.match(/(\uFE0F|\uFE0E)/g)||[],i=t.match(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g)||[],t=t.length-r.length-i.length;return n<=t&&(void 0===e||t<=e)},isLocale:function(t){return u(t),Ct.test(t)},isByteLength:p,isUUID:function(t,e){return u(t),!!(e=$e[[void 0,null].includes(e)?"all":e])&&e.test(t)},isMongoId:function(t){return u(t),kt(t)&&24===t.length},isAfter:function(t,e){return e=n((null==e?void 0:e.comparisonDate)||e||Date().toString()),!!((t=n(t))&&e&&e<t)},isBefore:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:String(new Date),e=(u(t),n(e)),t=n(t);return!!(t&&e&&t<e)},isIn:function(t,e){if(u(t),"[object Array]"!==Object.prototype.toString.call(e))return"object"===o(e)?e.hasOwnProperty(t):!(!e||"function"!=typeof e.indexOf)&&0<=e.indexOf(t);var n,r=[];for(n in e)!{}.hasOwnProperty.call(e,n)||(r[n]=q(e[n]));return 0<=r.indexOf(t)},isLuhnNumber:pe,isCreditCard:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},e=(u(t),e.provider),n=t.replace(/[- ]+/g,"");if(e&&e.toLowerCase()in v){if(!v[e.toLowerCase()].test(n))return!1}else{if(e&&!(e.toLowerCase()in v))throw new Error("".concat(e," is not a valid credit card provider."));if(!he.some(function(t){return t.test(n)}))return!1}return pe(t)},isIdentityCard:function(t,e){if(u(t),e in L)return L[e](t);if("any"!==e)throw new Error("Invalid locale '".concat(e,"'"));for(var n in L)if(L.hasOwnProperty(n))if((0,L[n])(t))return!0;return!1},isEAN:function(t){u(t);var e=Number(t.slice(-1));return Ee.test(t)&&e===me(t)},isISIN:function(t){if(u(t),!Ze.test(t))return!1;for(var e=!0,n=0,r=t.length-2;0<=r;r--)if("A"<=t[r]&&t[r]<="Z")for(var i=t[r].charCodeAt(0)-55,o=0,a=[i%10,Math.trunc(i/10)];o<a.length;o++){var c=a[o];n+=e?5<=c?1+2*(c-5):2*c:c,e=!e}else{i=t[r].charCodeAt(0)-"0".charCodeAt(0);n+=e?5<=i?1+2*(i-5):2*i:i,e=!e}var s=10*Math.trunc((n+9)/10)-n;return+t[t.length-1]==s},isISBN:function t(e,n){u(e);var r=String((null==n?void 0:n.version)||n);if(!(null!=n&&n.version||n))return t(e,{version:10})||t(e,{version:13});var i=e.replace(/[\s-]+/g,""),o=0;if("10"===r){if(!Ie.test(i))return!1;for(var a=0;a<r-1;a++)o+=(a+1)*i.charAt(a);if("X"===i.charAt(9)?o+=100:o+=10*i.charAt(9),o%11==0)return!0}else if("13"===r){if(!Re.test(i))return!1;for(var c=0;c<12;c++)o+=ve[c%2]*i.charAt(c);if(i.charAt(12)-(10-o%10)%10==0)return!0}return!1},isISSN:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=(u(t),"^\\d{4}-?\\d{3}[\\dX]$"),n=e.require_hyphen?n.replace("?",""):n;if(!(n=e.case_sensitive?new RegExp(n):new RegExp(n,"i")).test(t))return!1;for(var r=t.replace("-","").toUpperCase(),i=0,o=0;o<r.length;o++){var a=r[o];i+=("X"===a?10:+a)*(8-o)}return i%11==0},isMobilePhone:function(e,t,n){if(u(e),!n||!n.strictMode||e.startsWith("+")){if(Array.isArray(t))return t.some(function(t){if(F.hasOwnProperty(t)&&F[t].test(e))return!0;return!1});if(t in F)return F[t].test(e);if(t&&"any"!==t)throw new Error("Invalid locale '".concat(t,"'"));for(var r in F)if(F.hasOwnProperty(r))if(F[r].test(e))return!0}return!1},isMobilePhoneLocales:m,isPostalCode:function(t,e){if(u(t),e in O)return O[e].test(t);if("any"!==e)throw new Error("Invalid locale '".concat(e,"'"));for(var n in O)if(O.hasOwnProperty(n))if(O[n].test(t))return!0;return!1},isPostalCodeLocales:G,isEthereumAddress:function(t){return u(t),Ne.test(t)},isCurrency:function(t,e){return u(t),e=e=$(e,Fe),n="\\d{".concat(e.digits_after_decimal[0],"}"),e.digits_after_decimal.forEach(function(t,e){0!==e&&(n="".concat(n,"|\\d{").concat(t,"}"))}),r="(".concat(e.symbol.replace(/\W/,function(t){return"\\".concat(t)}),")").concat(e.require_symbol?"":"?"),i=["0","[1-9]\\d*","[1-9]\\d{0,2}(\\".concat(e.thousands_separator,"\\d{3})*")],i="(".concat(i.join("|"),")?"),o="(\\".concat(e.decimal_separator,"(").concat(n,"))").concat(e.require_decimal?"":"?"),i+=e.allow_decimal||e.require_decimal?o:"",e.allow_negatives&&!e.parens_for_negatives&&(e.negative_sign_after_digits?i+="-?":e.negative_sign_before_digits&&(i="-?"+i)),e.allow_negative_sign_placeholder?i="( (?!\\-))?".concat(i):e.allow_space_after_symbol?i=" ?".concat(i):e.allow_space_after_digits&&(i+="( (?!$))?"),e.symbol_after_digits?i+=r:i=r+i,e.allow_negatives&&(e.parens_for_negatives?i="(\\(".concat(i,"\\)|").concat(i,")"):e.negative_sign_before_digits||e.negative_sign_after_digits||(i="-?"+i)),new RegExp("^(?!-? )(?=.*\\d)".concat(i,"$")).test(t);var n,r,i,o},isBtcAddress:function(t){return u(t),De.test(t)||Te.test(t)},isISO6346:Pe,isFreightContainerID:Z,isISO6391:function(t){return u(t),He.has(t)},isISO8601:function(t){var e,n,r,i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},o=(u(t),(i.strictSeparator?_e:Ue).test(t));return o&&i.strict?(t=(i=t).match(/^(\d{4})-?(\d{3})([ T]{1}\.*|$)/))?(e=Number(t[1]),t=Number(t[2]),e%4==0&&e%100!=0||e%400==0?t<=366:t<=365):(t=(e=i.match(/(\d{4})-?(\d{0,2})-?(\d*)/).map(Number))[1],i=e[2],e=e[3],r=i&&"0".concat(i).slice(-2),n=e&&"0".concat(e).slice(-2),r=new Date("".concat(t,"-").concat(r||"01","-").concat(n||"01")),!i||!e||r.getUTCFullYear()===t&&r.getUTCMonth()+1===i&&r.getUTCDate()===e):o},isRFC3339:function(t){return u(t),be.test(t)},isISO31661Alpha2:function(t){return u(t),re.has(t.toUpperCase())},isISO31661Alpha3:function(t){return u(t),we.has(t.toUpperCase())},isISO4217:function(t){return u(t),Ke.has(t.toUpperCase())},isBase32:function(t,e){return u(t),(e=$(e,Ye)).crockford?We.test(t):!(t.length%8!=0||!ye.test(t))},isBase58:function(t){return u(t),!!xe.test(t)},isBase64:de,isDataURI:function(t){u(t);var e=t.split(",");if(e.length<2)return!1;var n=e.shift().trim().split(";");if("data:"!==(t=n.shift()).slice(0,5))return!1;if(""!==(t=t.slice(5))&&!ke.test(t))return!1;for(var r=0;r<n.length;r++)if((r!==n.length-1||"base64"!==n[r].toLowerCase())&&!Ve.test(n[r]))return!1;for(var i=0;i<e.length;i++)if(!ze.test(e[i]))return!1;return!0},isMagnetURI:function(t){return u(t),0===t.indexOf("magnet:?")&&Xe.test(t)},isMailtoURI:function(t,e){var n;return u(t),0===t.indexOf("mailto:")&&(n=(t=d(t.replace("mailto:","").split("?"),2))[0],t=void 0===(t=t[1])?"":t,!n&&!t||!!(t=function(t){var e=new Set(["subject","body","cc","bcc"]),n={cc:"",bcc:""},r=!1;if(4<(t=t.split("&")).length)return!1;var i,o=U(t);try{for(o.s();!(i=o.n()).done;){var a=d(i.value.split("="),2),c=a[0],s=a[1];if(c&&!e.has(c)){r=!0;break}!s||"cc"!==c&&"bcc"!==c||(n[c]=s),c&&e.delete(c)}}catch(t){o.e(t)}finally{o.f()}return!r&&n}(t))&&"".concat(n,",").concat(t.cc,",").concat(t.bcc).split(",").every(function(t){return!(t=qe(t," "))||dt(t,e)}))},isMimeType:function(t){return u(t),Qe.test(t)||tn.test(t)||en.test(t)},isLatLong:function(t,e){return u(t),e=$(e,cn),!!t.includes(",")&&!((t=t.split(","))[0].startsWith("(")&&!t[1].endsWith(")")||t[1].endsWith(")")&&!t[0].startsWith("("))&&(e.checkDMS?on.test(t[0])&&an.test(t[1]):nn.test(t[0])&&rn.test(t[1]))},ltrim:je,rtrim:Je,trim:qe,escape:function(t){return u(t),t.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\//g,"&#x2F;").replace(/\\/g,"&#x5C;").replace(/`/g,"&#96;")},unescape:function(t){return u(t),t.replace(/&quot;/g,'"').replace(/&#x27;/g,"'").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&#x2F;/g,"/").replace(/&#x5C;/g,"\\").replace(/&#96;/g,"`").replace(/&amp;/g,"&")},stripLow:function(t,e){return u(t),un(t,e?"\\x00-\\x09\\x0B\\x0C\\x0E-\\x1F\\x7F":"\\x00-\\x1F\\x7F")},whitelist:function(t,e){return u(t),t.replace(new RegExp("[^".concat(e,"]+"),"g"),"")},blacklist:un,isWhitelisted:function(t,e){u(t);for(var n=t.length-1;0<=n;n--)if(-1===e.indexOf(t[n]))return!1;return!0},normalizeEmail:function(t,e){e=$(e,ln);var n=(t=t.split("@")).pop();if((t=[t.join("@"),n])[1]=t[1].toLowerCase(),"gmail.com"===t[1]||"googlemail.com"===t[1]){if(e.gmail_remove_subaddress&&(t[0]=t[0].split("+")[0]),e.gmail_remove_dots&&(t[0]=t[0].replace(/\.+/g,pn)),!t[0].length)return!1;(e.all_lowercase||e.gmail_lowercase)&&(t[0]=t[0].toLowerCase()),t[1]=e.gmail_convert_googlemaildotcom?"gmail.com":t[1]}else if(0<=dn.indexOf(t[1])){if(e.icloud_remove_subaddress&&(t[0]=t[0].split("+")[0]),!t[0].length)return!1;(e.all_lowercase||e.icloud_lowercase)&&(t[0]=t[0].toLowerCase())}else if(0<=fn.indexOf(t[1])){if(e.outlookdotcom_remove_subaddress&&(t[0]=t[0].split("+")[0]),!t[0].length)return!1;(e.all_lowercase||e.outlookdotcom_lowercase)&&(t[0]=t[0].toLowerCase())}else if(0<=An.indexOf(t[1])){if(e.yahoo_remove_subaddress&&(n=t[0].split("-"),t[0]=1<n.length?n.slice(0,-1).join("-"):n[0]),!t[0].length)return!1;(e.all_lowercase||e.yahoo_lowercase)&&(t[0]=t[0].toLowerCase())}else 0<=$n.indexOf(t[1])?((e.all_lowercase||e.yandex_lowercase)&&(t[0]=t[0].toLowerCase()),t[1]="yandex.ru"):e.all_lowercase&&(t[0]=t[0].toLowerCase());return t.join("@")},toString:toString,isSlug:function(t){return u(t),hn.test(t)},isStrongPassword:function(t){var e,n,r,i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null,t=(u(t),In(t));return(i=$(i||{},Zn)).returnScore?(n=i,r=0,r=(r+=(e=t).uniqueChars*n.pointsPerUnique)+(e.length-e.uniqueChars)*n.pointsPerRepeat,0<e.lowercaseCount&&(r+=n.pointsForContainingLower),0<e.uppercaseCount&&(r+=n.pointsForContainingUpper),0<e.numberCount&&(r+=n.pointsForContainingNumber),0<e.symbolCount&&(r+=n.pointsForContainingSymbol),r):t.length>=i.minLength&&t.lowercaseCount>=i.minLowercase&&t.uppercaseCount>=i.minUppercase&&t.numberCount>=i.minNumbers&&t.symbolCount>=i.minSymbols},isTaxID:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"en-US",t=(u(t),t.slice(0));if(e in B)return e in N&&(t=t.replace(N[e],"")),!!B[e].test(t)&&(!(e in C)||C[e](t));throw new Error("Invalid locale '".concat(e,"'"))},isDate:E,isTime:function(t,e){return e=$(e,Rt),"string"==typeof t&&vt[e.hourFormat][e.mode].test(t)},isLicensePlate:function(t,e){if(u(t),e in P)return P[e](t);if("any"!==e)throw new Error("Invalid locale '".concat(e,"'"));for(var n in P)if((0,P[n])(t))return!0;return!1},isVAT:function(t,e){if(u(t),u(e),e in Rn)return Rn[e](t);throw new Error("Invalid country code: '".concat(e,"'"))},ibanLocales:h}});