import { DataTypes, Model, Optional } from 'sequelize'
import { sequelize } from '../config/database'

/**
 * 文章媒体关联模型的属性接口定义
 */
export interface ArticleMediaAttributes {
  id: number
  articleId: number
  mediaId: number
  type: 'cover' | 'content' | 'gallery'  // 封面图、内容图、图库
  sort: number  // 排序
  createdAt: Date
}

/**
 * 文章媒体关联创建时的属性接口定义
 */
export interface ArticleMediaCreationAttributes extends Optional<ArticleMediaAttributes, 'id' | 'sort' | 'createdAt'> {}

/**
 * 文章媒体关联模型类
 * 用于管理文章与媒体文件的关联关系
 */
export class ArticleMedia extends Model<ArticleMediaAttributes, ArticleMediaCreationAttributes> implements ArticleMediaAttributes {
  public id!: number
  public articleId!: number
  public mediaId!: number
  public type!: 'cover' | 'content' | 'gallery'
  public sort!: number
  public createdAt!: Date

  /**
   * 根据文章ID获取媒体文件
   */
  public static async findByArticleId(articleId: number, type?: string) {
    const whereClause: any = { articleId }
    if (type) {
      whereClause.type = type
    }

    return this.findAll({
      where: whereClause,
      order: [['sort', 'ASC'], ['createdAt', 'ASC']]
    })
  }

  /**
   * 根据媒体ID获取关联的文章
   */
  public static async findByMediaId(mediaId: number) {
    return this.findAll({
      where: { mediaId },
      order: [['createdAt', 'DESC']]
    })
  }
}

/**
 * 初始化文章媒体关联模型
 */
ArticleMedia.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    articleId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'article_id',
      references: {
        model: 'articles',
        key: 'id'
      }
    },
    mediaId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'media_id',
      references: {
        model: 'media',
        key: 'id'
      }
    },
    type: {
      type: DataTypes.ENUM('cover', 'content', 'gallery'),
      allowNull: false,
      defaultValue: 'content'
    },
    sort: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    }
  },
  {
    sequelize,
    modelName: 'ArticleMedia',
    tableName: 'article_media',
    timestamps: true,
    updatedAt: false,
    underscored: true,
    indexes: [
      {
        fields: ['article_id']
      },
      {
        fields: ['media_id']
      },
      {
        fields: ['article_id', 'type']
      },
      {
        unique: true,
        fields: ['article_id', 'media_id']
      }
    ]
  }
)
