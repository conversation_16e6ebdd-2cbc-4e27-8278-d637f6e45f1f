"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateSettingsData = exports.getDefaultSettings = exports.getUserProfile = exports.resetSettings = exports.updateSettings = exports.getSettings = void 0;
const Settings_1 = require("../models/Settings");
const User_1 = require("../models/User");
const errorHandler_1 = require("../middleware/errorHandler");
const joi_1 = __importDefault(require("joi"));
const updateSettingsSchema = joi_1.default.object({
    displayName: joi_1.default.string().min(1).max(100).optional(),
    avatar: joi_1.default.string().uri().optional().allow(''),
    bio: joi_1.default.string().max(500).optional().allow(''),
    website: joi_1.default.string().uri().optional().allow(''),
    location: joi_1.default.string().min(1).max(100).optional().allow(''),
    theme: joi_1.default.string().valid('light', 'dark', 'auto').optional(),
    language: joi_1.default.string().min(2).max(10).optional(),
    timezone: joi_1.default.string().min(1).max(50).optional(),
    itemsPerPage: joi_1.default.number().integer().min(5).max(100).optional(),
    emailNotifications: joi_1.default.boolean().optional(),
    commentNotifications: joi_1.default.boolean().optional(),
    systemNotifications: joi_1.default.boolean().optional(),
    profileVisibility: joi_1.default.string().valid('public', 'private').optional(),
    defaultPostVisibility: joi_1.default.string().valid('public', 'private').optional(),
    showEmail: joi_1.default.boolean().optional(),
    twoFactorEnabled: joi_1.default.boolean().optional()
});
const getSettings = async (req, res, next) => {
    try {
        if (!req.user) {
            throw (0, errorHandler_1.createError)(401, 'Authentication required', 'UNAUTHORIZED');
        }
        let settings = await Settings_1.Settings.findByUserId(req.user.id);
        if (!settings) {
            const defaultSettings = Settings_1.Settings.getDefaultSettings();
            settings = await Settings_1.Settings.upsertByUserId(req.user.id, defaultSettings);
        }
        res.json({
            success: true,
            message: '获取设置成功',
            data: settings
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getSettings = getSettings;
const updateSettings = async (req, res, next) => {
    try {
        if (!req.user) {
            throw (0, errorHandler_1.createError)(401, 'Authentication required', 'UNAUTHORIZED');
        }
        const { error, value } = updateSettingsSchema.validate(req.body);
        if (error) {
            throw (0, errorHandler_1.createError)(400, error.details[0]?.message || 'Validation error', 'VALIDATION_ERROR');
        }
        const validation = Settings_1.Settings.validateSettings(value);
        if (!validation.valid) {
            throw (0, errorHandler_1.createError)(400, validation.errors.join(', '), 'VALIDATION_ERROR');
        }
        const settings = await Settings_1.Settings.upsertByUserId(req.user.id, value);
        res.json({
            success: true,
            message: '设置更新成功',
            data: settings
        });
    }
    catch (error) {
        next(error);
    }
};
exports.updateSettings = updateSettings;
const resetSettings = async (req, res, next) => {
    try {
        if (!req.user) {
            throw (0, errorHandler_1.createError)(401, 'Authentication required', 'UNAUTHORIZED');
        }
        const defaultSettings = Settings_1.Settings.getDefaultSettings();
        const settings = await Settings_1.Settings.upsertByUserId(req.user.id, defaultSettings);
        res.json({
            success: true,
            message: '设置已重置为默认值',
            data: settings
        });
    }
    catch (error) {
        next(error);
    }
};
exports.resetSettings = resetSettings;
const getUserProfile = async (req, res, next) => {
    try {
        if (!req.user) {
            throw (0, errorHandler_1.createError)(401, 'Authentication required', 'UNAUTHORIZED');
        }
        const user = await User_1.User.findByPk(req.user.id, {
            include: [
                {
                    model: Settings_1.Settings,
                    as: 'settings'
                }
            ]
        });
        if (!user) {
            throw (0, errorHandler_1.createError)(404, 'User not found', 'USER_NOT_FOUND');
        }
        let userSettings = await Settings_1.Settings.findOne({ where: { userId: req.user.id } });
        if (!userSettings) {
            const defaultSettings = Settings_1.Settings.getDefaultSettings();
            userSettings = await Settings_1.Settings.upsertByUserId(req.user.id, defaultSettings);
        }
        res.json({
            success: true,
            message: '获取用户设置成功',
            data: userSettings
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getUserProfile = getUserProfile;
const getDefaultSettings = async (req, res, next) => {
    try {
        const defaultSettings = Settings_1.Settings.getDefaultSettings();
        res.json({
            success: true,
            message: '获取默认设置成功',
            data: defaultSettings
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getDefaultSettings = getDefaultSettings;
const validateSettingsData = async (req, res, next) => {
    try {
        const { error, value } = updateSettingsSchema.validate(req.body);
        if (error) {
            res.json({
                success: false,
                message: 'Validation failed',
                errors: error.details.map(detail => detail.message)
            });
            return;
        }
        const validation = Settings_1.Settings.validateSettings(value);
        res.json({
            success: validation.valid,
            message: validation.valid ? 'Validation passed' : 'Validation failed',
            errors: validation.errors
        });
    }
    catch (error) {
        next(error);
    }
};
exports.validateSettingsData = validateSettingsData;
//# sourceMappingURL=settings.js.map