{"version": 3, "file": "UserRole.d.ts", "sourceRoot": "", "sources": ["../../src/models/UserRole.ts"], "names": [], "mappings": "AAAA,OAAO,EAAa,KAAK,EAAE,QAAQ,EAAE,MAAM,WAAW,CAAA;AAMtD,MAAM,WAAW,kBAAkB;IACjC,EAAE,EAAE,MAAM,CAAA;IACV,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,EAAE,MAAM,CAAA;IACd,UAAU,CAAC,EAAE,MAAM,CAAA;IACnB,UAAU,EAAE,IAAI,CAAA;IAChB,SAAS,EAAE,IAAI,CAAA;IACf,SAAS,EAAE,IAAI,CAAA;CAChB;AAKD,MAAM,WAAW,0BAA2B,SAAQ,QAAQ,CAAC,kBAAkB,EAAE,IAAI,GAAG,YAAY,GAAG,WAAW,GAAG,WAAW,CAAC;CAAI;AAMrI,qBAAa,QAAS,SAAQ,KAAK,CAAC,kBAAkB,EAAE,0BAA0B,CAAE,YAAW,kBAAkB;IACxG,EAAE,EAAG,MAAM,CAAA;IACX,MAAM,EAAG,MAAM,CAAA;IACf,MAAM,EAAG,MAAM,CAAA;IACf,UAAU,CAAC,EAAE,MAAM,CAAA;IACnB,UAAU,EAAG,IAAI,CAAA;IACjB,SAAS,EAAG,IAAI,CAAA;IAChB,SAAS,EAAG,IAAI,CAAA;IAGvB,SAAgB,IAAI,CAAC,EAAE,GAAG,CAAA;IAC1B,SAAgB,IAAI,CAAC,EAAE,GAAG,CAAA;IAC1B,SAAgB,QAAQ,CAAC,EAAE,GAAG,CAAA;WAOV,YAAY,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;WAYjD,YAAY,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;WAajD,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;WAWzD,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;WAqBjE,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC;WA0BlF,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;WAW3D,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;WA0BlF,YAAY,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;WAmB5C,kBAAkB,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;WA8ClD,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;WAmCvE,gBAAgB,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAQ9D,MAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC;CAIjD"}