/**
 * 用户设置种子数据
 * 
 * 为所有用户创建默认的个人设置
 * 序号：010 (对应迁移文件 013-create-settings.ts)
 */

import { QueryInterface, QueryTypes } from 'sequelize'

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  console.log('⚙️ 开始创建用户设置种子数据...')

  // 检查是否已存在设置数据
  const existingSettings = await queryInterface.select(null, 'settings', {})
  if (existingSettings.length > 0) {
    console.log('用户设置数据已存在，跳过创建...')
    return
  }

  // 获取所有用户
  const users = await queryInterface.sequelize.query(
    'SELECT id, username FROM users ORDER BY id',
    { type: QueryTypes.SELECT }
  ) as Array<{ id: number; username: string }>

  if (users.length === 0) {
    console.log('⚠️ 没有找到用户数据，跳过设置种子数据创建')
    return
  }

  const now = new Date()

  // 为每个用户创建默认设置
  const settingsData = users.map(user => ({
    user_id: user.id,
    display_name: user.username,
    theme: 'auto', // 自动主题
    language: 'zh-CN', // 中文
    timezone: 'Asia/Shanghai', // 上海时区
    items_per_page: 10, // 每页显示10项
    email_notifications: true, // 启用邮件通知
    comment_notifications: true, // 启用评论通知
    system_notifications: true, // 启用系统通知
    profile_visibility: 'public', // 公开个人资料
    default_post_visibility: 'public', // 默认说说可见性为公开
    show_email: false, // 不显示邮箱
    two_factor_enabled: false, // 未启用双因子认证
    created_at: now,
    updated_at: now
  }))

  // 插入设置数据
  await queryInterface.bulkInsert('settings', settingsData)

  console.log(`✅ 成功为 ${users.length} 个用户创建了默认设置`)
}

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  console.log('🗑️ 删除用户设置种子数据...')
  await queryInterface.bulkDelete('settings', {}, {})
  console.log('✅ 用户设置种子数据删除完成')
}
