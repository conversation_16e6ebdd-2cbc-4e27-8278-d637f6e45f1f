import crypto from 'crypto'
import jwt from 'jsonwebtoken'

/**
 * 验证码类型枚举
 */
export enum VerificationType {
  EMAIL_VERIFICATION = 'email_verification',
  PASSWORD_RESET = 'password_reset',
  TWO_FACTOR_AUTH = 'two_factor_auth'
}

/**
 * 验证码数据接口
 */
interface VerificationData {
  userId?: number
  email?: string
  type: VerificationType
  expiresAt: Date
}

/**
 * 验证结果接口
 */
interface VerificationResult {
  valid: boolean
  data?: VerificationData
  error?: string
}

/**
 * 验证码工具类
 * 提供验证码生成、验证、管理功能
 */
export class VerificationService {
  /**
   * 生成验证token
   */
  private static generateVerificationToken(payload: VerificationData): string {
    const secret = process.env.JWT_SECRET
    if (!secret) {
      throw new Error('JWT_SECRET is not defined')
    }

    return jwt.sign(payload, secret, {
      expiresIn: '24h'
    })
  }

  /**
   * 验证token
   */
  private static verifyVerificationToken(token: string): VerificationData {
    const secret = process.env.JWT_SECRET
    if (!secret) {
      throw new Error('JWT_SECRET is not defined')
    }

    return jwt.verify(token, secret) as VerificationData
  }
  /**
   * 生成邮箱验证token
   */
  static generateEmailVerificationToken(email: string, userId?: number): string {
    const payload: VerificationData = {
      email,
      userId: userId!,
      type: VerificationType.EMAIL_VERIFICATION,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时后过期
    }

    return this.generateVerificationToken(payload)
  }

  /**
   * 生成密码重置token
   */
  static generatePasswordResetToken(email: string, userId: number): string {
    const payload: VerificationData = {
      email,
      userId,
      type: VerificationType.PASSWORD_RESET,
      expiresAt: new Date(Date.now() + 60 * 60 * 1000) // 1小时后过期
    }

    return this.generateVerificationToken(payload)
  }

  /**
   * 生成两步验证码
   */
  static generateTwoFactorCode(): string {
    return crypto.randomInt(100000, 999999).toString()
  }

  /**
   * 生成安全的随机token
   */
  static generateSecureToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex')
  }

  /**
   * 验证token
   */
  static verifyToken(token: string): VerificationResult {
    try {
      const payload = this.verifyVerificationToken(token)

      // 检查是否过期
      if (new Date() > new Date(payload.expiresAt)) {
        return {
          valid: false,
          error: 'Token has expired'
        }
      }

      return {
        valid: true,
        data: payload
      }
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : 'Invalid token'
      }
    }
  }

  /**
   * 验证邮箱验证token
   */
  static verifyEmailVerificationToken(token: string): VerificationResult {
    const result = this.verifyToken(token)

    if (!result.valid || !result.data) {
      return result
    }

    if (result.data.type !== VerificationType.EMAIL_VERIFICATION) {
      return {
        valid: false,
        error: 'Invalid token type'
      }
    }

    return result
  }

  /**
   * 验证密码重置token
   */
  static verifyPasswordResetToken(token: string): VerificationResult {
    const result = this.verifyToken(token)

    if (!result.valid || !result.data) {
      return result
    }

    if (result.data.type !== VerificationType.PASSWORD_RESET) {
      return {
        valid: false,
        error: 'Invalid token type'
      }
    }

    return result
  }

  /**
   * 生成验证码哈希
   */
  static hashVerificationCode(code: string): string {
    return crypto.createHash('sha256').update(code).digest('hex')
  }

  /**
   * 验证验证码
   */
  static verifyCode(code: string, hashedCode: string): boolean {
    const inputHash = this.hashVerificationCode(code)
    return crypto.timingSafeEqual(Buffer.from(inputHash), Buffer.from(hashedCode))
  }

  /**
   * 检查token是否即将过期（15分钟内）
   */
  static isTokenExpiringSoon(token: string): boolean {
    try {
      const payload = this.verifyVerificationToken(token)
      const expiresAt = new Date(payload.expiresAt)
      const now = new Date()
      const fifteenMinutesFromNow = new Date(now.getTime() + 15 * 60 * 1000)

      return expiresAt <= fifteenMinutesFromNow
    } catch {
      return true // 如果无法解析，认为即将过期
    }
  }

  /**
   * 生成带有时间戳的唯一标识符
   */
  static generateUniqueId(): string {
    const timestamp = Date.now().toString(36)
    const randomPart = crypto.randomBytes(8).toString('hex')
    return `${timestamp}-${randomPart}`
  }

  /**
   * 验证邮箱格式
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  /**
   * 验证密码强度
   */
  static validatePasswordStrength(password: string): {
    valid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    if (password.length < 8) {
      errors.push('密码长度至少8位')
    }

    if (password.length > 128) {
      errors.push('密码长度不能超过128位')
    }

    if (!/[a-z]/.test(password)) {
      errors.push('密码必须包含小写字母')
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('密码必须包含大写字母')
    }

    if (!/\d/.test(password)) {
      errors.push('密码必须包含数字')
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('密码必须包含特殊字符')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 验证用户名格式
   */
  static validateUsername(username: string): {
    valid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    if (username.length < 3) {
      errors.push('用户名长度至少3位')
    }

    if (username.length > 50) {
      errors.push('用户名长度不能超过50位')
    }

    if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
      errors.push('用户名只能包含字母、数字、下划线和连字符')
    }

    if (/^[0-9]/.test(username)) {
      errors.push('用户名不能以数字开头')
    }

    // 检查保留用户名
    const reservedNames = [
      'admin', 'administrator', 'root', 'system', 'api', 'www', 'mail',
      'ftp', 'blog', 'test', 'demo', 'guest', 'null', 'undefined'
    ]

    if (reservedNames.includes(username.toLowerCase())) {
      errors.push('该用户名为系统保留，请选择其他用户名')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 生成安全的临时密码
   */
  static generateTemporaryPassword(length: number = 12): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz'
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    const numbers = '0123456789'
    const symbols = '!@#$%^&*'

    const allChars = lowercase + uppercase + numbers + symbols

    let password = ''

    // 确保包含每种类型的字符
    password += lowercase[crypto.randomInt(lowercase.length)]
    password += uppercase[crypto.randomInt(uppercase.length)]
    password += numbers[crypto.randomInt(numbers.length)]
    password += symbols[crypto.randomInt(symbols.length)]

    // 填充剩余长度
    for (let i = 4; i < length; i++) {
      password += allChars[crypto.randomInt(allChars.length)]
    }

    // 打乱字符顺序
    return password.split('').sort(() => crypto.randomInt(3) - 1).join('')
  }
}
