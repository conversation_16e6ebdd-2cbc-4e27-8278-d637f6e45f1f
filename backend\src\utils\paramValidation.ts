/**
 * 参数验证工具类
 * 提供安全的参数解析和验证功能
 */

import { Request } from 'express'

/**
 * 安全地解析字符串参数为整数
 * @param value - 要解析的值
 * @param paramName - 参数名称（用于错误信息）
 * @returns 解析后的整数
 * @throws Error 如果参数无效
 */
export function parseIntParam(value: string | undefined, paramName: string): number {
  if (!value) {
    throw new Error(`Missing required parameter: ${paramName}`)
  }
  
  const parsed = parseInt(value, 10)
  if (isNaN(parsed)) {
    throw new Error(`Invalid ${paramName}: must be a valid integer`)
  }
  
  return parsed
}

/**
 * 安全地解析可选的字符串参数为整数
 * @param value - 要解析的值
 * @param defaultValue - 默认值
 * @returns 解析后的整数或默认值
 */
export function parseOptionalIntParam(value: string | undefined, defaultValue: number): number {
  if (!value) {
    return defaultValue
  }
  
  const parsed = parseInt(value, 10)
  if (isNaN(parsed)) {
    return defaultValue
  }
  
  return parsed
}

/**
 * 从请求中安全地获取ID参数
 * @param req - Express请求对象
 * @param paramName - 参数名称（默认为'id'）
 * @returns 解析后的ID
 * @throws Error 如果ID无效
 */
export function getIdParam(req: Request, paramName: string = 'id'): number {
  return parseIntParam(req.params[paramName], paramName)
}

/**
 * 从请求中安全地获取用户ID参数
 * @param req - Express请求对象
 * @returns 解析后的用户ID
 * @throws Error 如果用户ID无效
 */
export function getUserIdParam(req: Request): number {
  return parseIntParam(req.params.userId, 'userId')
}

/**
 * 从请求中安全地获取角色ID参数
 * @param req - Express请求对象
 * @returns 解析后的角色ID
 * @throws Error 如果角色ID无效
 */
export function getRoleIdParam(req: Request): number {
  return parseIntParam(req.params.roleId, 'roleId')
}

/**
 * 验证字符串参数是否存在且非空
 * @param value - 要验证的值
 * @param paramName - 参数名称
 * @returns 验证后的字符串
 * @throws Error 如果参数无效
 */
export function validateStringParam(value: string | undefined, paramName: string): string {
  if (!value || value.trim() === '') {
    throw new Error(`Missing or empty required parameter: ${paramName}`)
  }
  return value.trim()
}

/**
 * 从请求中安全地获取字符串参数
 * @param req - Express请求对象
 * @param paramName - 参数名称
 * @returns 验证后的字符串
 * @throws Error 如果参数无效
 */
export function getStringParam(req: Request, paramName: string): string {
  return validateStringParam(req.params[paramName], paramName)
}

/**
 * 从查询参数中安全地获取分页参数
 * @param req - Express请求对象
 * @returns 分页参数对象
 */
export function getPaginationParams(req: Request): { page: number; limit: number; offset: number } {
  const page = parseOptionalIntParam(req.query.page as string, 1)
  const limit = parseOptionalIntParam(req.query.limit as string, 10)
  
  // 确保分页参数在合理范围内
  const validPage = Math.max(1, page)
  const validLimit = Math.min(Math.max(1, limit), 100) // 最大100条记录
  const offset = (validPage - 1) * validLimit
  
  return {
    page: validPage,
    limit: validLimit,
    offset
  }
}

/**
 * 从查询参数中安全地获取搜索参数
 * @param req - Express请求对象
 * @returns 搜索参数
 */
export function getSearchParam(req: Request): string | undefined {
  const search = req.query.search as string
  return search && search.trim() !== '' ? search.trim() : undefined
}
