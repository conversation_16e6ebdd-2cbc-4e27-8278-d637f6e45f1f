{"version": 3, "file": "verification.d.ts", "sourceRoot": "", "sources": ["../../src/utils/verification.ts"], "names": [], "mappings": "AAMA,oBAAY,gBAAgB;IAC1B,kBAAkB,uBAAuB;IACzC,cAAc,mBAAmB;IACjC,eAAe,oBAAoB;CACpC;AAKD,UAAU,gBAAgB;IACxB,MAAM,CAAC,EAAE,MAAM,CAAA;IACf,KAAK,CAAC,EAAE,MAAM,CAAA;IACd,IAAI,EAAE,gBAAgB,CAAA;IACtB,SAAS,EAAE,IAAI,CAAA;CAChB;AAKD,UAAU,kBAAkB;IAC1B,KAAK,EAAE,OAAO,CAAA;IACd,IAAI,CAAC,EAAE,gBAAgB,CAAA;IACvB,KAAK,CAAC,EAAE,MAAM,CAAA;CACf;AAMD,qBAAa,mBAAmB;IAI9B,OAAO,CAAC,MAAM,CAAC,yBAAyB;IAcxC,OAAO,CAAC,MAAM,CAAC,uBAAuB;IAWtC,MAAM,CAAC,8BAA8B,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,MAAM;IAc7E,MAAM,CAAC,0BAA0B,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,MAAM;IAcxE,MAAM,CAAC,qBAAqB,IAAI,MAAM;IAOtC,MAAM,CAAC,mBAAmB,CAAC,MAAM,GAAE,MAAW,GAAG,MAAM;IAOvD,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,GAAG,kBAAkB;IA2BrD,MAAM,CAAC,4BAA4B,CAAC,KAAK,EAAE,MAAM,GAAG,kBAAkB;IAoBtE,MAAM,CAAC,wBAAwB,CAAC,KAAK,EAAE,MAAM,GAAG,kBAAkB;IAoBlE,MAAM,CAAC,oBAAoB,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;IAOjD,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,GAAG,OAAO;IAQ5D,MAAM,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO;IAgBlD,MAAM,CAAC,gBAAgB,IAAI,MAAM;IASjC,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO;IAQ3C,MAAM,CAAC,wBAAwB,CAAC,QAAQ,EAAE,MAAM,GAAG;QACjD,KAAK,EAAE,OAAO,CAAA;QACd,MAAM,EAAE,MAAM,EAAE,CAAA;KACjB;IAoCD,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG;QACzC,KAAK,EAAE,OAAO,CAAA;QACd,MAAM,EAAE,MAAM,EAAE,CAAA;KACjB;IAsCD,MAAM,CAAC,yBAAyB,CAAC,MAAM,GAAE,MAAW,GAAG,MAAM;CAwB9D"}