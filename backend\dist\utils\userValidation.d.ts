interface ValidationResult {
    valid: boolean;
    errors: string[];
}
interface AvailabilityResult {
    available: boolean;
    message?: string;
}
export declare class UserValidationService {
    static validateRegistrationData(data: {
        username: string;
        email: string;
        password: string;
        confirmPassword?: string;
    }): Promise<ValidationResult>;
    static checkUsernameAvailability(username: string): Promise<AvailabilityResult>;
    static checkEmailAvailability(email: string): Promise<AvailabilityResult>;
    static validateLoginData(data: {
        username: string;
        password: string;
    }): ValidationResult;
    static validatePasswordResetData(data: {
        password: string;
        confirmPassword: string;
    }): ValidationResult;
    static validateUserUpdateData(userId: number, data: {
        username?: string;
        email?: string;
        password?: string;
        confirmPassword?: string;
    }): Promise<ValidationResult>;
    static validateEmailVerificationRequest(email: string): ValidationResult;
    static validatePasswordResetRequest(email: string): ValidationResult;
    static canPerformSensitiveOperation(userId: number): Promise<{
        allowed: boolean;
        reason?: string;
    }>;
    static generateDisplayNameSuggestions(username: string): string[];
    static containsProfanity(username: string): boolean;
}
export {};
//# sourceMappingURL=userValidation.d.ts.map