"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.migrationInfo = exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('settings', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        user_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            unique: true,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        display_name: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: true,
            comment: '显示名称'
        },
        avatar: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            comment: '头像URL'
        },
        bio: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            comment: '个人简介'
        },
        website: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: true,
            comment: '个人网站'
        },
        location: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: true,
            comment: '所在地'
        },
        theme: {
            type: sequelize_1.DataTypes.ENUM('light', 'dark', 'auto'),
            allowNull: false,
            defaultValue: 'auto',
            comment: '主题偏好'
        },
        language: {
            type: sequelize_1.DataTypes.STRING(10),
            allowNull: false,
            defaultValue: 'zh-CN',
            comment: '语言设置'
        },
        timezone: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            defaultValue: 'Asia/Shanghai',
            comment: '时区设置'
        },
        items_per_page: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 10,
            comment: '每页显示数量'
        },
        email_notifications: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true,
            comment: '邮件通知'
        },
        comment_notifications: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true,
            comment: '评论通知'
        },
        system_notifications: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true,
            comment: '系统通知'
        },
        profile_visibility: {
            type: sequelize_1.DataTypes.ENUM('public', 'private'),
            allowNull: false,
            defaultValue: 'public',
            comment: '个人资料可见性'
        },
        default_post_visibility: {
            type: sequelize_1.DataTypes.ENUM('public', 'private'),
            allowNull: false,
            defaultValue: 'public',
            comment: '默认文章可见性'
        },
        show_email: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment: '显示邮箱地址'
        },
        two_factor_enabled: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment: '两步验证'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    }, {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci'
    });
    try {
        await queryInterface.addIndex('settings', ['user_id'], {
            unique: true,
            name: 'settings_user_id_unique'
        });
    }
    catch {
        console.log('Index settings_user_id_unique already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('settings', ['theme'], {
            name: 'settings_theme_index'
        });
    }
    catch {
        console.log('Index settings_theme_index already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('settings', ['profile_visibility'], {
            name: 'settings_profile_visibility_index'
        });
    }
    catch {
        console.log('Index settings_profile_visibility_index already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('settings', ['created_at'], {
            name: 'settings_created_at_index'
        });
    }
    catch {
        console.log('Index settings_created_at_index already exists, skipping...');
    }
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('settings');
};
exports.down = down;
exports.migrationInfo = {
    name: '20241229_create_settings_table',
    description: '创建用户设置表',
    version: '1.0.0',
    author: 'System',
    createdAt: new Date('2024-12-29')
};
//# sourceMappingURL=013-create-settings.js.map