"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const user_1 = require("../controllers/user");
const auth_1 = require("../middleware/auth");
const permission_1 = require("../middleware/permission");
const paramValidation_1 = require("../utils/paramValidation");
const router = (0, express_1.Router)();
router.get('/', auth_1.authenticateToken, (0, permission_1.requirePermission)('user.list'), user_1.getUsers);
router.get('/stats', auth_1.authenticateToken, (0, permission_1.requirePermission)('user.stats'), user_1.getUserStats);
router.get('/:id', auth_1.authenticateToken, (0, permission_1.requirePermission)('user.read'), user_1.getUserById);
router.post('/', auth_1.authenticateToken, (0, permission_1.requirePermission)('user.create'), user_1.createUser);
router.put('/:id', auth_1.authenticateToken, (0, permission_1.requirePermission)('user.update'), user_1.updateUser);
router.delete('/:id', auth_1.authenticateToken, (0, permission_1.requirePermission)('user.delete'), user_1.deleteUser);
router.get('/:id/roles', auth_1.authenticateToken, (0, permission_1.requirePermission)('user.read'), async (req, res, next) => {
    try {
        const userId = (0, paramValidation_1.getUserIdParam)(req);
        req.url = `/users/${userId}/roles`;
        return next('route');
    }
    catch (error) {
        return next(error);
    }
});
router.post('/:id/roles', auth_1.authenticateToken, (0, permission_1.requirePermission)('role.assign'), async (req, res, next) => {
    try {
        const userId = parseInt(req.params.id);
        if (isNaN(userId)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid user ID'
            });
        }
        req.url = `/users/${userId}/roles/assign`;
        next('route');
    }
    catch (error) {
        next(error);
    }
});
router.post('/bulk/create', auth_1.authenticateToken, (0, permission_1.requireRole)('admin'), async (req, res, next) => {
    try {
        res.status(501).json({
            success: false,
            message: 'Bulk user creation not implemented yet'
        });
    }
    catch (error) {
        next(error);
    }
});
router.post('/bulk/delete', auth_1.authenticateToken, (0, permission_1.requireRole)('admin'), async (req, res, next) => {
    try {
        res.status(501).json({
            success: false,
            message: 'Bulk user deletion not implemented yet'
        });
    }
    catch (error) {
        next(error);
    }
});
router.post('/bulk/assign-roles', auth_1.authenticateToken, (0, permission_1.requireRole)('admin'), async (req, res, next) => {
    try {
        res.status(501).json({
            success: false,
            message: 'Bulk role assignment not implemented yet'
        });
    }
    catch (error) {
        next(error);
    }
});
router.get('/search', auth_1.authenticateToken, (0, permission_1.requirePermission)('user.list'), async (req, res, next) => {
    try {
        const { q, limit = 10 } = req.query;
        if (!q || typeof q !== 'string') {
            return res.status(400).json({
                success: false,
                message: 'Search query is required'
            });
        }
        req.query = { search: q, limit, page: '1' };
        (0, user_1.getUsers)(req, res, next);
    }
    catch (error) {
        next(error);
    }
});
router.get('/by-role/:roleId', auth_1.authenticateToken, (0, permission_1.requirePermission)('user.list'), async (req, res, next) => {
    try {
        const roleId = parseInt(req.params.roleId);
        if (isNaN(roleId)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid role ID'
            });
        }
        req.url = `/roles/${roleId}/users`;
        next('route');
    }
    catch (error) {
        next(error);
    }
});
exports.default = router;
//# sourceMappingURL=user.js.map