"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    console.log('🔔 开始创建通知系统表结构...');
    await queryInterface.createTable('notifications', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        type: {
            type: sequelize_1.DataTypes.ENUM('interaction', 'content', 'system', 'marketing'),
            allowNull: false,
            comment: '通知类型：互动、内容、系统、营销'
        },
        title: {
            type: sequelize_1.DataTypes.STRING(200),
            allowNull: false,
            comment: '通知标题'
        },
        content: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            comment: '通知内容详情'
        },
        priority: {
            type: sequelize_1.DataTypes.ENUM('high', 'medium', 'low'),
            allowNull: false,
            defaultValue: 'medium',
            comment: '通知优先级'
        },
        recipient_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            comment: '接收者用户ID',
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        sender_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            comment: '发送者用户ID（系统通知可为空）',
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'SET NULL'
        },
        related_type: {
            type: sequelize_1.DataTypes.ENUM('article', 'post', 'comment', 'user', 'system'),
            allowNull: true,
            comment: '关联对象类型'
        },
        related_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            comment: '关联对象ID'
        },
        action_url: {
            type: sequelize_1.DataTypes.STRING(500),
            allowNull: true,
            comment: '操作链接URL'
        },
        is_read: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment: '是否已读'
        },
        read_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: true,
            comment: '阅读时间'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '创建时间'
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '更新时间'
        }
    });
    await queryInterface.createTable('notification_preferences', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        user_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            comment: '用户ID',
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        notification_type: {
            type: sequelize_1.DataTypes.ENUM('interaction', 'content', 'system', 'marketing'),
            allowNull: false,
            comment: '通知类型'
        },
        channel: {
            type: sequelize_1.DataTypes.ENUM('in_app', 'email', 'push'),
            allowNull: false,
            defaultValue: 'in_app',
            comment: '通知渠道：站内、邮件、推送'
        },
        is_enabled: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true,
            comment: '是否启用该类型通知'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '创建时间'
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '更新时间'
        }
    });
    console.log('📊 创建通知表索引...');
    try {
        await queryInterface.addIndex('notifications', ['recipient_id', 'created_at'], {
            name: 'idx_recipient_created'
        });
    }
    catch {
        console.log('Index idx_recipient_created already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('notifications', ['recipient_id', 'is_read'], {
            name: 'idx_recipient_unread'
        });
    }
    catch {
        console.log('Index idx_recipient_unread already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('notifications', ['type', 'priority'], {
            name: 'idx_type_priority'
        });
    }
    catch {
        console.log('Index idx_type_priority already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('notifications', ['related_type', 'related_id'], {
            name: 'idx_related'
        });
    }
    catch {
        console.log('Index idx_related already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('notifications', ['created_at'], {
            name: 'idx_created_at'
        });
    }
    catch {
        console.log('Index idx_created_at already exists, skipping...');
    }
    console.log('⚙️ 创建通知偏好设置表索引...');
    try {
        await queryInterface.addIndex('notification_preferences', ['user_id', 'notification_type', 'channel'], {
            name: 'unique_user_type_channel',
            unique: true
        });
    }
    catch {
        console.log('Index unique_user_type_channel already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('notification_preferences', ['user_id', 'is_enabled'], {
            name: 'idx_user_enabled'
        });
    }
    catch {
        console.log('Index idx_user_enabled already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('notification_preferences', ['notification_type', 'channel', 'is_enabled'], {
            name: 'idx_type_channel_enabled'
        });
    }
    catch {
        console.log('Index idx_type_channel_enabled already exists, skipping...');
    }
    console.log('✅ 通知系统表结构创建完成');
    console.log('📋 已创建表：');
    console.log('   - notifications (通知表)');
    console.log('   - notification_preferences (通知偏好设置表)');
    console.log('🔍 已创建索引：');
    console.log('   - 通知表：5个索引');
    console.log('   - 偏好设置表：3个索引');
};
exports.up = up;
const down = async (queryInterface) => {
    console.log('🗑️ 开始删除通知系统表结构...');
    await queryInterface.dropTable('notification_preferences');
    console.log('✅ 已删除 notification_preferences 表');
    await queryInterface.dropTable('notifications');
    console.log('✅ 已删除 notifications 表');
    console.log('🔔 通知系统表结构删除完成');
};
exports.down = down;
//# sourceMappingURL=009-create-notifications.js.map