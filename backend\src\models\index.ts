import { User } from './User'
import { Article } from './Article'
import { Tag } from './Tag'
import { ArticleTag } from './ArticleTag'
import { ArticleMedia } from './ArticleMedia'
import { ArticleView } from './ArticleView'
import { ArticleLike } from './ArticleLike'
import { Category } from './Category'
import { Comment } from './Comment'
import { Post } from './Post'
import { PostLike } from './PostLike'
import { Media } from './Media'
import { Notification } from './Notification'
import { NotificationPreference } from './NotificationPreference'
import { Settings } from './Settings'
import { Role } from './Role'
import { Permission } from './Permission'
import { UserRole } from './UserRole'
import { RolePermission } from './RolePermission'
import { AuditLog } from './AuditLog'



// 建立用户与文章的一对多关系：一个用户可以拥有多个文章
User.hasMany(Article, {
  foreignKey: 'authorId',
  as: 'articles'
})

// 建立文章与用户的一对一关系：每篇文章属于一个作者
Article.belongsTo(User, {
  foreignKey: 'authorId',
  as: 'author'
})

// 建立分类与文章的一对多关系：一个分类可以有多篇文章
Category.hasMany(Article, {
  foreignKey: 'categoryId',
  as: 'articles'
})

// 建立文章与分类的一对一关系：每篇文章属于一个分类
Article.belongsTo(Category, {
  foreignKey: 'categoryId',
  as: 'category'
})

// 建立分类的自关联关系：支持层次化分类
Category.hasMany(Category, {
  foreignKey: 'parentId',
  as: 'children'
})

Category.belongsTo(Category, {
  foreignKey: 'parentId',
  as: 'parent'
})


  // 建立文章与标签的多对多关系：通过ArticleTag中间表关联
  ; (Article as any).belongsToMany(Tag, {
    through: ArticleTag,
    foreignKey: 'articleId',
    otherKey: 'tagId',
    as: 'tags'
  })

  // 建立标签与文章的多对多关系：通过ArticleTag中间表关联
  ; (Tag as any).belongsToMany(Article, {
    through: ArticleTag,
    foreignKey: 'tagId',
    otherKey: 'articleId',
    as: 'articles'
  })

  // ==================== 文章媒体关联关系 ====================

  // 建立文章与媒体的多对多关系：通过ArticleMedia中间表关联
  ; (Article as any).belongsToMany(Media, {
    through: ArticleMedia,
    foreignKey: 'articleId',
    otherKey: 'mediaId',
    as: 'media'
  })

  // 建立媒体与文章的多对多关系：通过ArticleMedia中间表关联
  ; (Media as any).belongsToMany(Article, {
    through: ArticleMedia,
    foreignKey: 'mediaId',
    otherKey: 'articleId',
    as: 'articles'
  })

// ==================== 文章阅读记录关系 ====================

// 建立文章与阅读记录的一对多关系
Article.hasMany(ArticleView, {
  foreignKey: 'articleId',
  as: 'views'
})

// 建立阅读记录与文章的多对一关系
ArticleView.belongsTo(Article, {
  foreignKey: 'articleId',
  as: 'article'
})

// 建立用户与阅读记录的一对多关系
User.hasMany(ArticleView, {
  foreignKey: 'userId',
  as: 'articleViews'
})

// 建立阅读记录与用户的多对一关系
ArticleView.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
})

// ==================== 文章点赞关系 ====================

// 建立文章与点赞的一对多关系
Article.hasMany(ArticleLike, {
  foreignKey: 'articleId',
  as: 'likes'
})

// 建立点赞与文章的多对一关系
ArticleLike.belongsTo(Article, {
  foreignKey: 'articleId',
  as: 'article'
})

// 建立用户与文章点赞的一对多关系
User.hasMany(ArticleLike, {
  foreignKey: 'userId',
  as: 'articleLikes'
})

// 建立文章点赞与用户的多对一关系
ArticleLike.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
})


// 建立用户与评论的一对多关系：一个用户可以发表多个评论
User.hasMany(Comment, {
  foreignKey: 'authorId',
  as: 'comments'
})

// 建立评论与用户的多对一关系：每个评论属于一个作者
Comment.belongsTo(User, {
  foreignKey: 'authorId',
  as: 'author'
})

// 建立文章与评论的一对多关系：一篇文章可以有多个评论
Article.hasMany(Comment, {
  foreignKey: 'articleId',
  as: 'comments'
})

// 建立评论与文章的多对一关系：每个评论属于一篇文章
Comment.belongsTo(Article, {
  foreignKey: 'articleId',
  as: 'article'
})

// 建立评论的自关联关系：支持回复功能
Comment.hasMany(Comment, {
  foreignKey: 'parentId',
  as: 'replies'
})

Comment.belongsTo(Comment, {
  foreignKey: 'parentId',
  as: 'parent'
})

// ==================== 说说功能相关关系 ====================

// 建立用户与说说的一对多关系：一个用户可以发布多个说说
User.hasMany(Post, {
  foreignKey: 'authorId',
  as: 'posts'
})

// 建立说说与用户的多对一关系：每个说说属于一个作者
Post.belongsTo(User, {
  foreignKey: 'authorId',
  as: 'author'
})

// 建立说说与评论的一对多关系：一个说说可以有多个评论
Post.hasMany(Comment, {
  foreignKey: 'postId',
  as: 'comments'
})

// 建立评论与说说的多对一关系：每个评论可能属于一个说说
Comment.belongsTo(Post, {
  foreignKey: 'postId',
  as: 'post'
})

// 建立说说与点赞的一对多关系：一个说说可以有多个点赞
Post.hasMany(PostLike, {
  foreignKey: 'postId',
  as: 'likes'
})

// 建立点赞与说说的多对一关系：每个点赞属于一个说说
PostLike.belongsTo(Post, {
  foreignKey: 'postId',
  as: 'post'
})

// 建立用户与点赞的一对多关系：一个用户可以点赞多个说说
User.hasMany(PostLike, {
  foreignKey: 'userId',
  as: 'postLikes'
})

// 建立点赞与用户的多对一关系：每个点赞属于一个用户
PostLike.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
})

// 建立用户与媒体的一对多关系：一个用户可以上传多个媒体文件
User.hasMany(Media, {
  foreignKey: 'uploaderId',
  as: 'media'
})

// 建立媒体与用户的一对一关系：每个媒体文件属于一个上传者
Media.belongsTo(User, {
  foreignKey: 'uploaderId',
  as: 'uploader'
})

// 建立用户与通知的一对多关系：一个用户可以接收多个通知
User.hasMany(Notification, {
  foreignKey: 'recipientId',
  as: 'receivedNotifications'
})

// 建立用户与通知的一对多关系：一个用户可以发送多个通知
User.hasMany(Notification, {
  foreignKey: 'senderId',
  as: 'sentNotifications'
})

// 建立通知与接收者的多对一关系：每个通知属于一个接收者
Notification.belongsTo(User, {
  foreignKey: 'recipientId',
  as: 'recipient'
})

// 建立通知与发送者的多对一关系：每个通知可能有一个发送者
Notification.belongsTo(User, {
  foreignKey: 'senderId',
  as: 'sender'
})

// 建立用户与通知偏好的一对多关系：一个用户可以有多个通知偏好设置
User.hasMany(NotificationPreference, {
  foreignKey: 'userId',
  as: 'notificationPreferences'
})

// 建立通知偏好与用户的多对一关系：每个通知偏好属于一个用户
NotificationPreference.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
})

// 建立用户与设置的一对一关系：一个用户有一个设置
User.hasOne(Settings, {
  foreignKey: 'userId',
  as: 'settings'
})

// 建立设置与用户的一对一关系：每个设置属于一个用户
Settings.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
})

// ==================== 角色权限系统关系 ====================

// 建立用户与角色的多对多关系：通过UserRole中间表关联
User.belongsToMany(Role, {
  through: UserRole,
  foreignKey: 'userId',
  otherKey: 'roleId',
  as: 'roles'
})

// 建立角色与用户的多对多关系：通过UserRole中间表关联
Role.belongsToMany(User, {
  through: UserRole,
  foreignKey: 'roleId',
  otherKey: 'userId',
  as: 'users'
})

// 建立角色与权限的多对多关系：通过RolePermission中间表关联
Role.belongsToMany(Permission, {
  through: RolePermission,
  foreignKey: 'roleId',
  otherKey: 'permissionId',
  as: 'permissions'
})

// 建立权限与角色的多对多关系：通过RolePermission中间表关联
Permission.belongsToMany(Role, {
  through: RolePermission,
  foreignKey: 'permissionId',
  otherKey: 'roleId',
  as: 'roles'
})

// 建立UserRole与User的关系
UserRole.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
})

// 建立UserRole与Role的关系
UserRole.belongsTo(Role, {
  foreignKey: 'roleId',
  as: 'role'
})

// 建立UserRole与分配者的关系
UserRole.belongsTo(User, {
  foreignKey: 'assignedBy',
  as: 'assigner'
})

// 建立RolePermission与Role的关系
RolePermission.belongsTo(Role, {
  foreignKey: 'roleId',
  as: 'role'
})

// 建立RolePermission与Permission的关系
RolePermission.belongsTo(Permission, {
  foreignKey: 'permissionId',
  as: 'permission'
})

// 建立RolePermission与分配者的关系
RolePermission.belongsTo(User, {
  foreignKey: 'assignedBy',
  as: 'assigner'
})

// ==================== 操作日志关系 ====================

// 建立用户与操作日志的一对多关系：一个用户可以有多条操作日志
User.hasMany(AuditLog, {
  foreignKey: 'userId',
  as: 'auditLogs'
})

// 建立操作日志与用户的多对一关系：每条日志属于一个用户（可选）
AuditLog.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
})

export {
  User,
  Article,
  Tag,
  ArticleTag,
  ArticleMedia,
  ArticleView,
  ArticleLike,
  Category,
  Comment,
  Post,
  PostLike,
  Media,
  Notification,
  NotificationPreference,
  Settings,
  Role,
  Permission,
  UserRole,
  RolePermission,
  AuditLog
}


/**
 * 同步所有数据模型到数据库
 * @param force 是否强制同步（会删除已存在的表结构）
 * @returns Promise<void>
 */
export const syncModels = async (force: boolean = false): Promise<void> => {
  try {
    // 按顺序同步所有模型
    await User.sync({ force })
    await Settings.sync({ force })  // Settings依赖User
    await Role.sync({ force })
    await Permission.sync({ force })
    await UserRole.sync({ force })  // UserRole依赖User和Role
    await RolePermission.sync({ force })  // RolePermission依赖Role和Permission
    await Category.sync({ force })
    await Tag.sync({ force })
    await Article.sync({ force })
    await ArticleTag.sync({ force })
    await ArticleMedia.sync({ force })
    await ArticleView.sync({ force })
    await ArticleLike.sync({ force })
    await Post.sync({ force })
    await PostLike.sync({ force })
    await Media.sync({ force })
    await Notification.sync({ force })
    await NotificationPreference.sync({ force })
    await Comment.sync({ force })  // Comment放在最后，因为它依赖Post
    await AuditLog.sync({ force })  // AuditLog依赖User
    console.log('All models synchronized successfully')
  } catch (error) {
    console.error('Error synchronizing models:', error)
    throw error
  }
}