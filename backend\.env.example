
# 端口配置
PORT=8080
# 运行环境配置
NODE_ENV=development
# 前端URL配置
FRONTEND_URL=http://localhost:3000


# 数据库主机地址
DB_HOST=localhost
# 数据库端口
DB_PORT=3306
# 数据库名称
DB_NAME=person-blog
# 数据库用户名
DB_USER=person-blog
# 数据库密码
DB_PASSWORD=123456


# JWT密钥，生产环境必须更改
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
# JWT过期时间
JWT_EXPIRES_IN=7d


# API接口URL
API_URL=http://localhost:8080


# 邮件服务配置
# SMTP服务器地址
SMTP_HOST=smtp.gmail.com
# SMTP服务器端口
SMTP_PORT=587
# 是否使用安全连接
SMTP_SECURE=false
# SMTP用户名（邮箱地址）
SMTP_USER=<EMAIL>
# SMTP密码（应用专用密码）
SMTP_PASS=your-app-password
# 发件人邮箱地址
SMTP_FROM=<EMAIL>

# 应用名称
APP_NAME=Personal Blog