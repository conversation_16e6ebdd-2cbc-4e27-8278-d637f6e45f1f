import { Request, Response, NextFunction } from 'express'
import Joi from 'joi'
import { createError } from './errorHandler'

/**
 * 创建一个用于验证请求体（req.body）的中间件函数
 * @param schema Joi 验证模式对象，用于定义数据结构和验证规则
 * @returns 返回一个 Express 中间件函数，用于验证请求体数据
 */
export const validate = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    // 使用提供的 schema 验证 req.body 数据，abortEarly: false 表示收集所有错误而非遇到第一个错误就停止
    const { error } = schema.validate(req.body, { abortEarly: false })

    if (error) {
      // 将所有验证错误信息拼接为一个字符串
      const errorMessage = error.details.map(detail => detail.message).join(', ')
      // 抛出自定义错误，状态码为 400，错误类型为 VALIDATION_ERROR
      throw createError(400, errorMessage, 'VALIDATION_ERROR')
    }

    next()
  }
}

/**
 * 创建一个用于验证请求体（req.body）的中间件函数
 * @param schema Joi 验证模式对象，用于定义请求体的数据结构和验证规则
 * @returns 返回一个 Express 中间件函数，用于验证请求体数据
 */
export const validateRequest = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    // 使用提供的 schema 验证 req.body 数据
    const { error } = schema.validate(req.body, { abortEarly: false })

    if (error) {
      // 将所有验证错误信息拼接为一个字符串
      const errorMessage = error.details.map(detail => detail.message).join(', ')
      // 抛出自定义错误，状态码为 400，错误类型为 VALIDATION_ERROR
      throw createError(400, errorMessage, 'VALIDATION_ERROR')
    }

    next()
  }
}

/**
 * 创建一个用于验证查询参数（req.query）的中间件函数
 * @param schema Joi 验证模式对象，用于定义查询参数的数据结构和验证规则
 * @returns 返回一个 Express 中间件函数，用于验证查询参数数据
 */
export const validateQuery = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    // 使用提供的 schema 验证 req.query 数据
    const { error } = schema.validate(req.query, { abortEarly: false })

    if (error) {
      // 将所有验证错误信息拼接为一个字符串
      const errorMessage = error.details.map(detail => detail.message).join(', ')
      // 抛出自定义错误，状态码为 400，错误类型为 VALIDATION_ERROR
      throw createError(400, errorMessage, 'VALIDATION_ERROR')
    }

    next()
  }
}

/**
 * 创建一个用于验证路径参数（req.params）的中间件函数
 * @param schema Joi 验证模式对象，用于定义路径参数的数据结构和验证规则
 * @returns 返回一个 Express 中间件函数，用于验证路径参数数据
 */
export const validateParams = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    // 使用提供的 schema 验证 req.params 数据
    const { error } = schema.validate(req.params, { abortEarly: false })

    if (error) {
      // 将所有验证错误信息拼接为一个字符串
      const errorMessage = error.details.map(detail => detail.message).join(', ')
      // 抛出自定义错误，状态码为 400，错误类型为 VALIDATION_ERROR
      throw createError(400, errorMessage, 'VALIDATION_ERROR')
    }

    next()
  }
}

// 定义文章创建时的验证 schema
const articleSchema = Joi.object({
  title: Joi.string().min(1).max(200).required(),
  content: Joi.string().min(1).required(),
  excerpt: Joi.string().optional().allow(''),
  status: Joi.string().valid('draft', 'published').default('draft'),
  tags: Joi.array().items(Joi.string().trim().min(1)).default([]),
  categoryId: Joi.number().integer().positive().optional().allow(null)
})

// 定义文章更新时的验证 schema，所有字段为可选
const articleUpdateSchema = Joi.object({
  title: Joi.string().min(1).max(200).optional(),
  content: Joi.string().min(1).optional(),
  excerpt: Joi.string().optional().allow(''),
  status: Joi.string().valid('draft', 'published').optional(),
  tags: Joi.array().items(Joi.string().trim().min(1)).optional(),
  categoryId: Joi.number().integer().positive().optional().allow(null)
})

// 定义分类创建时的验证 schema
const categorySchema = Joi.object({
  name: Joi.string().min(1).max(100).required(),
  slug: Joi.string().min(1).max(100).pattern(/^[a-z0-9-]+$/i).optional(),
  description: Joi.string().optional().allow(''),
  parentId: Joi.number().integer().positive().optional().allow(null),
  sort: Joi.number().integer().min(0).default(0)
})

// 定义分类更新时的验证 schema，所有字段为可选
const categoryUpdateSchema = Joi.object({
  name: Joi.string().min(1).max(100).optional(),
  slug: Joi.string().min(1).max(100).pattern(/^[a-z0-9-]+$/i).optional(),
  description: Joi.string().optional().allow(''),
  parentId: Joi.number().integer().positive().optional().allow(null),
  sort: Joi.number().integer().min(0).optional()
})

// 导出针对文章创建和更新的验证中间件
export const validateArticle = validate(articleSchema)
export const validateArticleUpdate = validate(articleUpdateSchema)

// 导出针对分类创建和更新的验证中间件
export const validateCategory = validate(categorySchema)
export const validateCategoryUpdate = validate(categoryUpdateSchema)

// 定义评论创建时的验证 schema
const commentSchema = Joi.object({
  content: Joi.string().min(1).max(2000).required()
    .custom((value, helpers) => {
      // 基础的XSS防护：检查是否包含script标签
      if (/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(value)) {
        return helpers.error('any.invalid')
      }
      return value
    }, 'XSS Protection'),
  articleId: Joi.number().integer().positive().required(),
  parentId: Joi.number().integer().positive().optional()
})

// 定义评论更新时的验证 schema
const commentUpdateSchema = Joi.object({
  content: Joi.string().min(1).max(2000).required()
    .custom((value, helpers) => {
      // 基础的XSS防护：检查是否包含script标签
      if (/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(value)) {
        return helpers.error('any.invalid')
      }
      return value
    }, 'XSS Protection')
})

// 定义评论状态更新时的验证 schema
const commentStatusSchema = Joi.object({
  status: Joi.string().valid('pending', 'approved', 'rejected').required()
})

// 定义评论查询参数的验证 schema
const commentQuerySchema = Joi.object({
  articleId: Joi.number().integer().positive().optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  status: Joi.string().valid('pending', 'approved', 'rejected', 'all').default('approved')
})

// 导出针对评论的验证中间件
export const validateComment = validate(commentSchema)
export const validateCommentUpdate = validate(commentUpdateSchema)
export const validateCommentStatus = validate(commentStatusSchema)
export const validateCommentQuery = validateQuery(commentQuerySchema)

// ==================== 说说功能验证规则 ====================

// 定义说说创建时的验证 schema
const postCreationSchema = Joi.object({
  content: Joi.string().trim().min(1).max(1000).required().messages({
    'string.empty': '说说内容不能为空',
    'string.min': '说说内容不能为空',
    'string.max': '说说内容不能超过1000个字符',
    'any.required': '说说内容是必填项'
  }),
  images: Joi.array().items(
    Joi.string().uri().messages({
      'string.uri': '图片URL格式不正确'
    })
  ).max(9).optional().messages({
    'array.max': '最多只能上传9张图片'
  }),
  visibility: Joi.string().valid('public', 'private').default('public').messages({
    'any.only': '可见性设置无效，只能是 public 或 private'
  }),
  location: Joi.string().max(200).optional().messages({
    'string.max': '位置信息不能超过200个字符'
  })
})

// 定义说说更新时的验证 schema
const postUpdateSchema = Joi.object({
  content: Joi.string().trim().min(1).max(1000).optional().messages({
    'string.empty': '说说内容不能为空',
    'string.min': '说说内容不能为空',
    'string.max': '说说内容不能超过1000个字符'
  }),
  images: Joi.array().items(
    Joi.string().uri().messages({
      'string.uri': '图片URL格式不正确'
    })
  ).max(9).optional().messages({
    'array.max': '最多只能上传9张图片'
  }),
  visibility: Joi.string().valid('public', 'private').optional().messages({
    'any.only': '可见性设置无效，只能是 public 或 private'
  }),
  location: Joi.string().max(200).optional().messages({
    'string.max': '位置信息不能超过200个字符'
  })
})

// 导出针对说说功能的验证中间件
export const validatePostCreation = validate(postCreationSchema)
export const validatePostUpdate = validate(postUpdateSchema)