import { QueryInterface, DataTypes } from 'sequelize'

/**
 * 创建RBAC（基于角色的访问控制）系统相关数据表的迁移文件
 * 包括：roles（角色表）、permissions（权限表）、user_roles（用户角色关联表）、role_permissions（角色权限关联表）
 * 迁移序号：014 (重命名自 20241230_create_rbac_tables.ts)
 */

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // 创建角色表
  await queryInterface.createTable('roles', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    is_system: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci'
  })

  // 创建权限表
  await queryInterface.createTable('permissions', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    resource: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    action: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci'
  })

  // 创建用户角色关联表
  await queryInterface.createTable('user_roles', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    role_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'roles',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    assigned_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    },
    assigned_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci'
  })

  // 创建角色权限关联表
  await queryInterface.createTable('role_permissions', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    role_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'roles',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    permission_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'permissions',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    assigned_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    },
    assigned_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci'
  })

  // 创建索引
  // 角色表索引
  try {
    await queryInterface.addIndex('roles', ['name'], { unique: true, name: 'roles_name' })
  } catch {
    console.log('Index roles_name already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('roles', ['is_active'], { name: 'roles_is_active' })
  } catch {
    console.log('Index roles_is_active already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('roles', ['is_system'], { name: 'roles_is_system' })
  } catch {
    console.log('Index roles_is_system already exists, skipping...')
  }

  // 权限表索引
  try {
    await queryInterface.addIndex('permissions', ['name'], { unique: true, name: 'permissions_name' })
  } catch {
    console.log('Index permissions_name already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('permissions', ['resource', 'action'], { unique: true, name: 'permissions_resource_action' })
  } catch {
    console.log('Index permissions_resource_action already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('permissions', ['resource'], { name: 'permissions_resource' })
  } catch {
    console.log('Index permissions_resource already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('permissions', ['action'], { name: 'permissions_action' })
  } catch {
    console.log('Index permissions_action already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('permissions', ['is_active'], { name: 'permissions_is_active' })
  } catch {
    console.log('Index permissions_is_active already exists, skipping...')
  }

  // 用户角色关联表索引
  try {
    await queryInterface.addIndex('user_roles', ['user_id', 'role_id'], { unique: true, name: 'user_roles_user_role' })
  } catch {
    console.log('Index user_roles_user_role already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('user_roles', ['user_id'], { name: 'user_roles_user_id' })
  } catch {
    console.log('Index user_roles_user_id already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('user_roles', ['role_id'], { name: 'user_roles_role_id' })
  } catch {
    console.log('Index user_roles_role_id already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('user_roles', ['assigned_by'], { name: 'user_roles_assigned_by' })
  } catch {
    console.log('Index user_roles_assigned_by already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('user_roles', ['assigned_at'], { name: 'user_roles_assigned_at' })
  } catch {
    console.log('Index user_roles_assigned_at already exists, skipping...')
  }

  // 角色权限关联表索引
  try {
    await queryInterface.addIndex('role_permissions', ['role_id', 'permission_id'], { unique: true, name: 'role_permissions_role_permission' })
  } catch {
    console.log('Index role_permissions_role_permission already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('role_permissions', ['role_id'], { name: 'role_permissions_role_id' })
  } catch {
    console.log('Index role_permissions_role_id already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('role_permissions', ['permission_id'], { name: 'role_permissions_permission_id' })
  } catch {
    console.log('Index role_permissions_permission_id already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('role_permissions', ['assigned_by'], { name: 'role_permissions_assigned_by' })
  } catch {
    console.log('Index role_permissions_assigned_by already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('role_permissions', ['assigned_at'], { name: 'role_permissions_assigned_at' })
  } catch {
    console.log('Index role_permissions_assigned_at already exists, skipping...')
  }
}

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  // 删除表（按依赖关系的逆序）
  await queryInterface.dropTable('role_permissions')
  await queryInterface.dropTable('user_roles')
  await queryInterface.dropTable('permissions')
  await queryInterface.dropTable('roles')
}
