{"version": 3, "file": "seedRbac.js", "sourceRoot": "", "sources": ["../../src/scripts/seedRbac.ts"], "names": [], "mappings": ";;;;;;AAEA,uDAAqD;AACrD,iDAA8C;AAC9C,sFAAqG;AACrG,oDAA2B;AAG3B,gBAAM,CAAC,MAAM,EAAE,CAAA;AAMf,MAAM,cAAc;IAIlB,KAAK,CAAC,IAAI;QACR,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAA;QACtD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;QAE3B,IAAI,CAAC;YAEH,MAAM,cAAc,GAAG,yBAAY,CAAC,iBAAiB,EAAE,CAAA;YACvD,OAAO,CAAC,GAAG,CAAC,gBAAgB,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAA;YACtD,OAAO,CAAC,GAAG,CAAC,YAAY,cAAc,CAAC,IAAI,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC,CAAA;YACrE,OAAO,CAAC,GAAG,CAAC,YAAY,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAA;YAClD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YAGf,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;YAChD,MAAM,WAAW,GAAG,MAAM,yBAAY,CAAC,cAAc,EAAE,CAAA;YAEvD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,OAAO,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAA;gBAC9C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACjB,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;YAChD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YAGf,MAAM,cAAc,GAAG,oBAAS,CAAC,iBAAiB,EAAE,CAAA;YAGpD,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAA;YAC7C,MAAM,IAAA,2BAAY,EAAC,cAAc,CAAC,CAAA;YAElC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YACf,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;YAC3B,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAA;YAClE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YACf,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAA;YAC/B,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAA;YACrE,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAA;YAChE,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAA;YAC5D,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YACf,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAA;YACtD,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAA;YAC/C,OAAO,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAA;YAC3E,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAA;YACzD,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAA;QAE5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;YACjB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;YAC7B,OAAO,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAA;YACnD,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAA;YACtC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACjB,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,KAAK;QACT,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAA;QACpD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;QAE3B,IAAI,CAAC;YAEH,MAAM,cAAc,GAAG,yBAAY,CAAC,iBAAiB,EAAE,CAAA;YACvD,OAAO,CAAC,GAAG,CAAC,gBAAgB,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAA;YACtD,OAAO,CAAC,GAAG,CAAC,YAAY,cAAc,CAAC,IAAI,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC,CAAA;YACrE,OAAO,CAAC,GAAG,CAAC,YAAY,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAA;YAClD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YAGf,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;YAChD,MAAM,WAAW,GAAG,MAAM,yBAAY,CAAC,cAAc,EAAE,CAAA;YAEvD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,OAAO,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAA;gBAC9C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACjB,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;YAChD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YAGf,MAAM,cAAc,GAAG,oBAAS,CAAC,iBAAiB,EAAE,CAAA;YAGpD,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAA;YAC5C,MAAM,IAAA,6BAAa,EAAC,cAAc,CAAC,CAAA;YAEnC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YACf,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;YAC3B,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAA;QAElE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;YACjB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;YAC7B,OAAO,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAA;YACjD,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAA;YACtC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACjB,CAAC;IACH,CAAC;IAKD,QAAQ;QACN,OAAO,CAAC,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;CAwBf,CAAC,CAAA;IACA,CAAC;CACF;AAKD,KAAK,UAAU,IAAI;IACjB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IAGlC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QACnD,MAAM,MAAM,GAAG,IAAI,cAAc,EAAE,CAAA;QACnC,MAAM,CAAC,QAAQ,EAAE,CAAA;QACjB,OAAM;IACR,CAAC;IAGD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;IAGtC,MAAM,MAAM,GAAG,IAAI,cAAc,EAAE,CAAA;IACnC,IAAI,OAAO,EAAE,CAAC;QACZ,MAAM,MAAM,CAAC,KAAK,EAAE,CAAA;IACtB,CAAC;SAAM,CAAC;QACN,MAAM,MAAM,CAAC,IAAI,EAAE,CAAA;IACrB,CAAC;AACH,CAAC;AAGD,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;IACnD,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAAA;IACpE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACjB,CAAC,CAAC,CAAA;AAEF,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE;IACxC,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAA;IAC3C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACjB,CAAC,CAAC,CAAA;AAGF,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACrB,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAA;QAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACjB,CAAC,CAAC,CAAA;AACJ,CAAC"}