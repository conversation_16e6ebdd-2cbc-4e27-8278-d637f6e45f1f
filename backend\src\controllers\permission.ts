import { Request, Response, NextFunction } from 'express'
import { Permission } from '../models/Permission'
import { Role } from '../models/Role'
import { RolePermission } from '../models/RolePermission'
import { createError } from '../middleware/errorHandler'
import { getStringParam } from '../utils/paramValidation'

/**
 * 权限管理控制器
 * 提供权限的查询和管理功能
 */

/**
 * 获取权限列表
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const getPermissions = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { page = 1, limit = 50, search, resource, action, isActive } = req.query

    const pageNum = parseInt(page as string)
    const limitNum = parseInt(limit as string)
    const offset = (pageNum - 1) * limitNum

    // 构建查询条件
    const whereClause: any = {}

    if (search) {
      const { Op } = require('sequelize')
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ]
    }

    if (resource) {
      whereClause.resource = resource
    }

    if (action) {
      whereClause.action = action
    }

    if (isActive !== undefined) {
      whereClause.isActive = isActive === 'true'
    }

    // 查询权限列表
    const { rows: permissions, count: total } = await Permission.findAndCountAll({
      where: whereClause,
      limit: limitNum,
      offset,
      order: [['resource', 'ASC'], ['action', 'ASC']],
      include: [
        {
          model: Role,
          as: 'roles',
          through: { attributes: [] }
        }
      ]
    })

    // 为每个权限添加统计信息
    const permissionsWithStats = await Promise.all(
      permissions.map(async (permission) => {
        const roleCount = await permission.getRoleCount()

        return {
          ...permission.toJSON(),
          roleCount
        }
      })
    )

    res.json({
      success: true,
      data: {
        permissions: permissionsWithStats,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 根据ID获取权限详情
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const getPermissionById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params

    const permission = await Permission.findByPk(id, {
      include: [
        {
          model: Role,
          as: 'roles',
          through: { attributes: ['assignedAt', 'assignedBy'] }
        }
      ]
    })

    if (!permission) {
      throw createError(404, '权限不存在', 'PERMISSION_NOT_FOUND')
    }

    // 获取权限统计信息
    const roleCount = await permission.getRoleCount()

    res.json({
      success: true,
      data: {
        ...permission.toJSON(),
        roleCount
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 根据资源分组获取权限
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const getPermissionsByResource = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { isActive } = req.query

    // 构建查询条件
    const whereClause: any = {}
    if (isActive !== undefined) {
      whereClause.isActive = isActive === 'true'
    }

    const permissions = await Permission.findAll({
      where: whereClause,
      order: [['resource', 'ASC'], ['action', 'ASC']]
    })

    // 按资源分组
    const groupedPermissions: Record<string, any[]> = {}
    permissions.forEach(permission => {
      const resource = permission.resource
      if (!groupedPermissions[resource]) {
        groupedPermissions[resource] = []
      }
      groupedPermissions[resource].push(permission.toJSON())
    })

    res.json({
      success: true,
      data: groupedPermissions
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 获取所有资源列表
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const getResources = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const resources = await Permission.getResources()

    res.json({
      success: true,
      data: resources
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 获取指定资源的所有操作
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const getResourceActions = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { resource } = req.params

    const resourceName = getStringParam(req, 'resource')
    const actions = await Permission.getActionsByResource(resourceName)

    res.json({
      success: true,
      data: actions
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 获取权限的角色列表
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const getPermissionRoles = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params

    const permission = await Permission.findByPk(id)
    if (!permission) {
      throw createError(404, '权限不存在', 'PERMISSION_NOT_FOUND')
    }

    const roles = await permission.getRoles()

    res.json({
      success: true,
      data: roles
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 创建新权限
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const createPermission = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { name, description, resource, action, isActive = true } = req.body

    // 检查权限名称是否已存在
    const existingPermission = await Permission.findByName(name)
    if (existingPermission) {
      throw createError(400, '权限名称已存在', 'PERMISSION_NAME_EXISTS')
    }

    // 检查资源和操作组合是否已存在
    const existingResourceAction = await Permission.findByResourceAndAction(resource, action)
    if (existingResourceAction) {
      throw createError(400, '该资源和操作的权限已存在', 'RESOURCE_ACTION_EXISTS')
    }

    // 创建权限
    const permission = await Permission.create({
      name,
      description,
      resource,
      action,
      isActive
    })

    res.status(201).json({
      success: true,
      data: permission,
      message: '权限创建成功'
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 更新权限
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const updatePermission = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params
    const { name, description, resource, action, isActive } = req.body

    const permission = await Permission.findByPk(id)
    if (!permission) {
      throw createError(404, '权限不存在', 'PERMISSION_NOT_FOUND')
    }

    // 如果修改名称，检查是否与其他权限重复
    if (name && name !== permission.name) {
      const existingPermission = await Permission.findByName(name)
      if (existingPermission) {
        throw createError(400, '权限名称已存在', 'PERMISSION_NAME_EXISTS')
      }
    }

    // 如果修改资源或操作，检查组合是否与其他权限重复
    const newResource = resource || permission.resource
    const newAction = action || permission.action
    if ((resource && resource !== permission.resource) || (action && action !== permission.action)) {
      const existingResourceAction = await Permission.findByResourceAndAction(newResource, newAction)
      if (existingResourceAction && existingResourceAction.id !== permission.id) {
        throw createError(400, '该资源和操作的权限已存在', 'RESOURCE_ACTION_EXISTS')
      }
    }

    // 更新权限
    await permission.update({
      name: name || permission.name,
      description: description !== undefined ? description : permission.description,
      resource: resource || permission.resource,
      action: action || permission.action,
      isActive: isActive !== undefined ? isActive : permission.isActive
    })

    res.json({
      success: true,
      data: permission,
      message: '权限更新成功'
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 删除权限
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const deletePermission = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params

    const permission = await Permission.findByPk(id)
    if (!permission) {
      throw createError(404, '权限不存在', 'PERMISSION_NOT_FOUND')
    }

    // 检查是否有角色使用此权限
    const roleCount = await permission.getRoleCount()
    if (roleCount > 0) {
      throw createError(400, `权限正在被 ${roleCount} 个角色使用，无法删除`, 'PERMISSION_IN_USE')
    }

    // 删除权限
    await permission.destroy()

    res.json({
      success: true,
      message: '权限删除成功'
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 批量创建权限
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const bulkCreatePermissions = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { permissions } = req.body

    if (!Array.isArray(permissions) || permissions.length === 0) {
      throw createError(400, '权限数据不能为空', 'INVALID_PERMISSIONS_DATA')
    }

    // 验证权限数据格式
    for (const perm of permissions) {
      if (!perm.name || !perm.resource || !perm.action) {
        throw createError(400, '权限数据格式不正确，缺少必要字段', 'INVALID_PERMISSION_FORMAT')
      }
    }

    // 批量创建权限
    const createdPermissions = await Permission.bulkCreatePermissions(permissions)

    res.status(201).json({
      success: true,
      data: createdPermissions,
      message: `成功创建 ${createdPermissions.length} 个权限`
    })
  } catch (error) {
    next(error)
  }
}
