#!/usr/bin/env ts-node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const database_1 = require("../config/database");
const connection_1 = require("../database/connection");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
class CleanDbScript {
    async run() {
        console.log('🧹 Starting database cleanup...');
        console.log('='.repeat(50));
        try {
            console.log('🔍 Testing database connection...');
            const isConnected = await connection_1.dbConnection.testConnection();
            if (!isConnected) {
                console.error('❌ Database connection failed!');
                process.exit(1);
            }
            console.log('✅ Database connection successful!');
            console.log('');
            const queryInterface = database_1.sequelize.getQueryInterface();
            await queryInterface.sequelize.query('SET FOREIGN_KEY_CHECKS = 0');
            console.log('🔓 Disabled foreign key checks');
            const tablesToDrop = [
                'audit_logs',
                'comments',
                'post_likes',
                'posts',
                'article_tags',
                'articles',
                'categories',
                'tags',
                'notifications',
                'notification_preferences',
                'media',
                'settings',
                'role_permissions',
                'user_roles',
                'permissions',
                'roles',
                'users',
                'seeders',
                'migrations'
            ];
            for (const tableName of tablesToDrop) {
                try {
                    await queryInterface.dropTable(tableName);
                    console.log(`🗑️  Dropped table: ${tableName}`);
                }
                catch (error) {
                    console.log(`⚠️  Table ${tableName} does not exist, skipping...`);
                }
            }
            await queryInterface.sequelize.query('SET FOREIGN_KEY_CHECKS = 1');
            console.log('🔒 Re-enabled foreign key checks');
            console.log('');
            console.log('='.repeat(50));
            console.log('✅ Database cleanup completed successfully!');
            console.log('💡 You can now run "npm run db:init" to reinitialize the database');
        }
        catch (error) {
            console.error('');
            console.error('='.repeat(50));
            console.error('❌ Database cleanup failed!');
            console.error('Error details:', error);
            process.exit(1);
        }
        finally {
            await connection_1.dbConnection.closeConnection();
        }
    }
}
async function main() {
    const args = process.argv.slice(2);
    if (args.includes('--help') || args.includes('-h')) {
        console.log(`
📚 Database Cleanup Tool

Usage:
  ts-node src/scripts/cleanDb.ts    Clean all database tables

⚠️  WARNING: This will delete ALL data in the database!

Examples:
  ts-node src/scripts/cleanDb.ts
`);
        return;
    }
    console.log('⚠️  WARNING: This will delete ALL data in the database!');
    console.log('Are you sure you want to continue? (This action cannot be undone)');
    console.log('Press Ctrl+C to cancel, or press Enter to continue...');
    const script = new CleanDbScript();
    await script.run();
}
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});
if (require.main === module) {
    main().catch((error) => {
        console.error('Script execution failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=cleanDb.js.map