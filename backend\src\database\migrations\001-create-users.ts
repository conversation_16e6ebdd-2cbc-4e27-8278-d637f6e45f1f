import { QueryInterface, DataTypes } from 'sequelize'

/**
 * 执行数据库迁移升级操作，创建用户表
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 返回空的Promise，表示异步操作完成
 */
export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // 创建用户表，包含用户基本信息、状态字段和时间戳字段
  await queryInterface.createTable('users', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true
    },
    password_hash: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    // 用户状态相关字段
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '用户是否激活'
    },
    email_verified: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '邮箱是否已验证'
    },
    email_verified_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '邮箱验证时间'
    },
    last_login_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '最后登录时间'
    },
    // 密码重置相关字段
    password_reset_token: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '密码重置token'
    },
    password_reset_expires: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '密码重置token过期时间'
    },
    // 邮箱验证相关字段
    email_verification_token: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '邮箱验证token'
    },
    email_verification_expires: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '邮箱验证token过期时间'
    },
    // 时间戳字段
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  })

  // 为用户名和邮箱字段添加索引以提高查询性能
  // 使用 try-catch 来避免重复创建索引的错误
  try {
    await queryInterface.addIndex('users', ['username'])
  } catch {
    console.log('Index users_username already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('users', ['email'])
  } catch {
    console.log('Index users_email already exists, skipping...')
  }
}

/**
 * 执行数据库迁移降级操作，删除用户表
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 返回空的Promise，表示异步操作完成
 */
export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.dropTable('users')
}