"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserStats = exports.deleteUser = exports.updateUser = exports.createUser = exports.getUserById = exports.getUsers = exports.UserController = void 0;
const User_1 = require("../models/User");
const UserRole_1 = require("../models/UserRole");
const Role_1 = require("../models/Role");
const errorHandler_1 = require("../middleware/errorHandler");
const password_1 = require("../utils/password");
const joi_1 = __importDefault(require("joi"));
const sequelize_1 = require("sequelize");
const paramValidation_1 = require("../utils/paramValidation");
const getUsersQuerySchema = joi_1.default.object({
    page: joi_1.default.number().integer().min(1).default(1),
    limit: joi_1.default.number().integer().min(1).max(100).default(20),
    search: joi_1.default.string().max(100).optional(),
    orderBy: joi_1.default.string().valid('id', 'username', 'email', 'createdAt').default('createdAt'),
    orderDirection: joi_1.default.string().valid('ASC', 'DESC').default('DESC'),
    includeRoles: joi_1.default.boolean().default(false)
});
const createUserSchema = joi_1.default.object({
    username: joi_1.default.string().alphanum().min(3).max(50).required(),
    email: joi_1.default.string().email().max(100).required(),
    password: joi_1.default.string().min(6).max(100).required(),
    roleIds: joi_1.default.array().items(joi_1.default.number().integer().min(1)).optional()
});
const updateUserSchema = joi_1.default.object({
    username: joi_1.default.string().alphanum().min(3).max(50).optional(),
    email: joi_1.default.string().email().max(100).optional(),
    password: joi_1.default.string().min(6).max(100).optional(),
    roleIds: joi_1.default.array().items(joi_1.default.number().integer().min(1)).optional()
});
class UserController {
    static async getUsers(req, res, next) {
        try {
            const { error, value } = getUsersQuerySchema.validate(req.query);
            if (error) {
                throw (0, errorHandler_1.createError)(400, error.details[0]?.message || 'Invalid query parameters', 'VALIDATION_ERROR');
            }
            const { page, limit, search, orderBy, orderDirection, includeRoles } = value;
            const offset = (page - 1) * limit;
            const where = {};
            if (search) {
                where[sequelize_1.Op.or] = [
                    { username: { [sequelize_1.Op.iLike]: `%${search}%` } },
                    { email: { [sequelize_1.Op.iLike]: `%${search}%` } }
                ];
            }
            const include = [];
            if (includeRoles) {
                include.push({
                    model: Role_1.Role,
                    as: 'roles',
                    attributes: ['id', 'name', 'description'],
                    through: { attributes: [] }
                });
            }
            const { rows: users, count: total } = await User_1.User.findAndCountAll({
                where,
                include,
                attributes: { exclude: ['passwordHash'] },
                order: [[orderBy, orderDirection]],
                limit,
                offset
            });
            const totalPages = Math.ceil(total / limit);
            res.json({
                success: true,
                data: {
                    users,
                    pagination: {
                        page,
                        limit,
                        total,
                        totalPages
                    }
                },
                message: 'Users retrieved successfully'
            });
        }
        catch (error) {
            next(error);
        }
    }
    static async getUserById(req, res, next) {
        try {
            const userId = (0, paramValidation_1.getUserIdParam)(req);
            if (isNaN(userId)) {
                throw (0, errorHandler_1.createError)(400, 'Invalid user ID', 'INVALID_PARAMETER');
            }
            const user = await User_1.User.findByPk(userId, {
                attributes: { exclude: ['passwordHash'] },
                include: [
                    {
                        model: Role_1.Role,
                        as: 'roles',
                        attributes: ['id', 'name', 'description'],
                        through: { attributes: ['assignedAt', 'assignedBy'] }
                    }
                ]
            });
            if (!user) {
                throw (0, errorHandler_1.createError)(404, 'User not found', 'USER_NOT_FOUND');
            }
            res.json({
                success: true,
                data: user,
                message: 'User retrieved successfully'
            });
        }
        catch (error) {
            next(error);
        }
    }
    static async createUser(req, res, next) {
        try {
            const { error, value } = createUserSchema.validate(req.body);
            if (error) {
                throw (0, errorHandler_1.createError)(400, error.details[0]?.message || 'Invalid parameters', 'VALIDATION_ERROR');
            }
            const { username, email, password, roleIds = [] } = value;
            const existingUserByUsername = await User_1.User.findByUsername(username);
            if (existingUserByUsername) {
                throw (0, errorHandler_1.createError)(400, 'Username already exists', 'USERNAME_EXISTS');
            }
            const existingUserByEmail = await User_1.User.findByEmail(email);
            if (existingUserByEmail) {
                throw (0, errorHandler_1.createError)(400, 'Email already exists', 'EMAIL_EXISTS');
            }
            if (roleIds.length > 0) {
                const validRoles = await Role_1.Role.findAll({
                    where: { id: roleIds, isActive: true }
                });
                if (validRoles.length !== roleIds.length) {
                    throw (0, errorHandler_1.createError)(400, 'Invalid role IDs provided', 'INVALID_ROLE_IDS');
                }
            }
            const passwordHash = await (0, password_1.hashPassword)(password);
            const user = await User_1.User.create({
                username,
                email,
                passwordHash,
                isActive: true,
                emailVerified: false
            });
            if (roleIds.length > 0) {
                const userRoleData = roleIds.map((roleId) => ({
                    userId: user.id,
                    roleId,
                    assignedBy: req.user?.id
                }));
                await UserRole_1.UserRole.bulkCreate(userRoleData);
            }
            const createdUser = await User_1.User.findByPk(user.id, {
                attributes: { exclude: ['passwordHash'] },
                include: [
                    {
                        model: Role_1.Role,
                        as: 'roles',
                        attributes: ['id', 'name', 'description'],
                        through: { attributes: [] }
                    }
                ]
            });
            res.status(201).json({
                success: true,
                data: createdUser,
                message: 'User created successfully'
            });
        }
        catch (error) {
            next(error);
        }
    }
    static async updateUser(req, res, next) {
        try {
            const userId = (0, paramValidation_1.getUserIdParam)(req);
            if (isNaN(userId)) {
                throw (0, errorHandler_1.createError)(400, 'Invalid user ID', 'INVALID_PARAMETER');
            }
            const { error, value } = updateUserSchema.validate(req.body);
            if (error) {
                throw (0, errorHandler_1.createError)(400, error.details[0]?.message || 'Invalid parameters', 'VALIDATION_ERROR');
            }
            const { username, email, password, roleIds } = value;
            const user = await User_1.User.findByPk(userId);
            if (!user) {
                throw (0, errorHandler_1.createError)(404, 'User not found', 'USER_NOT_FOUND');
            }
            if (username && username !== user.username) {
                const existingUser = await User_1.User.findByUsername(username);
                if (existingUser) {
                    throw (0, errorHandler_1.createError)(400, 'Username already exists', 'USERNAME_EXISTS');
                }
            }
            if (email && email !== user.email) {
                const existingUser = await User_1.User.findByEmail(email);
                if (existingUser) {
                    throw (0, errorHandler_1.createError)(400, 'Email already exists', 'EMAIL_EXISTS');
                }
            }
            const updateData = {};
            if (username)
                updateData.username = username;
            if (email)
                updateData.email = email;
            if (password)
                updateData.passwordHash = await (0, password_1.hashPassword)(password);
            if (Object.keys(updateData).length > 0) {
                await user.update(updateData);
            }
            if (roleIds !== undefined) {
                if (roleIds.length > 0) {
                    const validRoles = await Role_1.Role.findAll({
                        where: { id: roleIds, isActive: true }
                    });
                    if (validRoles.length !== roleIds.length) {
                        throw (0, errorHandler_1.createError)(400, 'Invalid role IDs provided', 'INVALID_ROLE_IDS');
                    }
                }
                await UserRole_1.UserRole.destroy({ where: { userId } });
                if (roleIds.length > 0) {
                    const userRoleData = roleIds.map((roleId) => ({
                        userId,
                        roleId,
                        assignedBy: req.user?.id
                    }));
                    await UserRole_1.UserRole.bulkCreate(userRoleData);
                }
            }
            const updatedUser = await User_1.User.findByPk(userId, {
                attributes: { exclude: ['passwordHash'] },
                include: [
                    {
                        model: Role_1.Role,
                        as: 'roles',
                        attributes: ['id', 'name', 'description'],
                        through: { attributes: [] }
                    }
                ]
            });
            res.json({
                success: true,
                data: updatedUser,
                message: 'User updated successfully'
            });
        }
        catch (error) {
            next(error);
        }
    }
    static async deleteUser(req, res, next) {
        try {
            const userId = (0, paramValidation_1.getUserIdParam)(req);
            if (isNaN(userId)) {
                throw (0, errorHandler_1.createError)(400, 'Invalid user ID', 'INVALID_PARAMETER');
            }
            if (req.user && req.user.id === userId) {
                throw (0, errorHandler_1.createError)(400, 'Cannot delete your own account', 'CANNOT_DELETE_SELF');
            }
            const user = await User_1.User.findByPk(userId);
            if (!user) {
                throw (0, errorHandler_1.createError)(404, 'User not found', 'USER_NOT_FOUND');
            }
            await user.destroy();
            res.json({
                success: true,
                message: 'User deleted successfully'
            });
        }
        catch (error) {
            next(error);
        }
    }
    static async getUserStats(req, res, next) {
        try {
            const totalUsers = await User_1.User.count();
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            const recentUsers = await User_1.User.count({
                where: {
                    createdAt: {
                        [sequelize_1.Op.gte]: thirtyDaysAgo
                    }
                }
            });
            const roleStats = await UserRole_1.UserRole.findAll({
                include: [
                    {
                        model: Role_1.Role,
                        as: 'role',
                        attributes: ['name']
                    }
                ],
                attributes: ['roleId'],
                group: ['roleId', 'role.id', 'role.name'],
                raw: true
            });
            res.json({
                success: true,
                data: {
                    totalUsers,
                    recentUsers,
                    roleStats
                },
                message: 'User statistics retrieved successfully'
            });
        }
        catch (error) {
            next(error);
        }
    }
}
exports.UserController = UserController;
exports.getUsers = UserController.getUsers, exports.getUserById = UserController.getUserById, exports.createUser = UserController.createUser, exports.updateUser = UserController.updateUser, exports.deleteUser = UserController.deleteUser, exports.getUserStats = UserController.getUserStats;
exports.default = UserController;
//# sourceMappingURL=user.js.map