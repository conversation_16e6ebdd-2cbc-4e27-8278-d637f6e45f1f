{"name": "personal-blog-backend", "version": "1.0.0", "description": "Personal blog system backend API", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest --passWithNoTests", "test:watch": "jest --watch --passWithNoTests", "migrate": "ts-node src/scripts/migrate.ts", "migrate:prod": "node dist/scripts/migrate.js", "seed:rbac": "ts-node src/scripts/seedRbac.ts", "seed:rbac:clean": "ts-node src/scripts/seedRbac.ts clean", "db:init": "ts-node src/scripts/initDb.ts", "db:init:prod": "node dist/scripts/initDb.js", "db:check": "ts-node src/scripts/checkDb.ts", "db:clean": "ts-node src/scripts/cleanDb.ts", "db:reset": "ts-node src/scripts/reset-database.ts"}, "dependencies": {"@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "axios": "^1.11.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.2.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "nodemailer": "^7.0.5", "sequelize": "^6.35.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.8.10", "@types/supertest": "^2.0.16", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.4", "ts-jest": "^29.4.0", "ts-node": "^10.9.1", "typescript": "^5.2.2"}}