{"version": 3, "file": "Settings.js", "sourceRoot": "", "sources": ["../../src/models/Settings.ts"], "names": [], "mappings": ";;;AAAA,yCAAsD;AACtD,iDAA8C;AAgD9C,MAAa,QAAS,SAAQ,iBAAqD;IAsC1E,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,MAAc;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,CAAA;IAC5C,CAAC;IAQM,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,MAAc,EAAE,YAAyC;QAC1F,MAAM,UAAU,GAAQ;YACtB,MAAM;YACN,GAAG,YAAY;SAChB,CAAA;QAED,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAA;QAChD,OAAO,QAAQ,CAAA;IACjB,CAAC;IAMM,MAAM,CAAC,kBAAkB;QAC9B,OAAO;YACL,KAAK,EAAE,MAAM;YACb,QAAQ,EAAE,OAAO;YACjB,QAAQ,EAAE,eAAe;YACzB,YAAY,EAAE,EAAE;YAChB,kBAAkB,EAAE,IAAI;YACxB,oBAAoB,EAAE,IAAI;YAC1B,mBAAmB,EAAE,IAAI;YACzB,iBAAiB,EAAE,QAAQ;YAC3B,qBAAqB,EAAE,QAAQ;YAC/B,SAAS,EAAE,KAAK;YAChB,gBAAgB,EAAE,KAAK;SACxB,CAAA;IACH,CAAC;IAOM,MAAM,CAAC,gBAAgB,CAAC,YAAyC;QACtE,MAAM,MAAM,GAAa,EAAE,CAAA;QAG3B,IAAI,YAAY,CAAC,KAAK,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;YAClF,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAA;QAC1C,CAAC;QAGD,IAAI,YAAY,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,CAAC,IAAI,YAAY,CAAC,YAAY,GAAG,GAAG,CAAC,EAAE,CAAC;YACpG,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAA;QACnC,CAAC;QAGD,IAAI,YAAY,CAAC,iBAAiB,IAAI,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACtG,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAA;QAC5C,CAAC;QAED,IAAI,YAAY,CAAC,qBAAqB,IAAI,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,qBAAqB,CAAC,EAAE,CAAC;YAC9G,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAA;QAC5C,CAAC;QAGD,IAAI,YAAY,CAAC,OAAO,IAAI,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;YACxD,IAAI,CAAC;gBACH,IAAI,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;YAC/B,CAAC;YAAC,MAAM,CAAC;gBACP,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;YAC3B,CAAC;QACH,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;SACP,CAAA;IACH,CAAC;IAKM,MAAM;QACX,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAA;QAChC,OAAO,MAAM,CAAA;IACf,CAAC;CACF;AA/HD,4BA+HC;AAKD,QAAQ,CAAC,IAAI,CACX;IACE,EAAE,EAAE;QACF,IAAI,EAAE,qBAAS,CAAC,OAAO;QACvB,UAAU,EAAE,IAAI;QAChB,aAAa,EAAE,IAAI;QACnB,SAAS,EAAE,KAAK;KACjB;IACD,MAAM,EAAE;QACN,IAAI,EAAE,qBAAS,CAAC,OAAO;QACvB,SAAS,EAAE,KAAK;QAChB,MAAM,EAAE,IAAI;QACZ,KAAK,EAAE,SAAS;QAChB,UAAU,EAAE;YACV,KAAK,EAAE,OAAO;YACd,GAAG,EAAE,IAAI;SACV;QACD,QAAQ,EAAE,SAAS;QACnB,QAAQ,EAAE,SAAS;KACpB;IAGD,WAAW,EAAE;QACX,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;QAC3B,SAAS,EAAE,IAAI;QACf,KAAK,EAAE,cAAc;QACrB,QAAQ,EAAE;YACR,GAAG,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;SACd;KACF;IACD,MAAM,EAAE;QACN,IAAI,EAAE,qBAAS,CAAC,IAAI;QACpB,SAAS,EAAE,IAAI;QACf,QAAQ,EAAE;YACR,KAAK,EAAE,IAAI;SACZ;KACF;IACD,GAAG,EAAE;QACH,IAAI,EAAE,qBAAS,CAAC,IAAI;QACpB,SAAS,EAAE,IAAI;QACf,QAAQ,EAAE;YACR,GAAG,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;SACd;KACF;IACD,OAAO,EAAE;QACP,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;QAC3B,SAAS,EAAE,IAAI;QACf,QAAQ,EAAE;YACR,KAAK,EAAE,IAAI;SACZ;KACF;IACD,QAAQ,EAAE;QACR,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;QAC3B,SAAS,EAAE,IAAI;QACf,QAAQ,EAAE;YACR,GAAG,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;SACd;KACF;IAGD,KAAK,EAAE;QACL,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;QAC7C,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,MAAM;KACrB;IACD,QAAQ,EAAE;QACR,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,EAAE,CAAC;QAC1B,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,OAAO;QACrB,QAAQ,EAAE;YACR,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;SACb;KACF;IACD,QAAQ,EAAE;QACR,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,EAAE,CAAC;QAC1B,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,eAAe;QAC7B,QAAQ,EAAE;YACR,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;SACb;KACF;IACD,YAAY,EAAE;QACZ,IAAI,EAAE,qBAAS,CAAC,OAAO;QACvB,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,EAAE;QAChB,KAAK,EAAE,gBAAgB;QACvB,QAAQ,EAAE;YACR,GAAG,EAAE,CAAC;YACN,GAAG,EAAE,GAAG;SACT;KACF;IAGD,kBAAkB,EAAE;QAClB,IAAI,EAAE,qBAAS,CAAC,OAAO;QACvB,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,IAAI;QAClB,KAAK,EAAE,qBAAqB;KAC7B;IACD,oBAAoB,EAAE;QACpB,IAAI,EAAE,qBAAS,CAAC,OAAO;QACvB,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,IAAI;QAClB,KAAK,EAAE,uBAAuB;KAC/B;IACD,mBAAmB,EAAE;QACnB,IAAI,EAAE,qBAAS,CAAC,OAAO;QACvB,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,IAAI;QAClB,KAAK,EAAE,sBAAsB;KAC9B;IAGD,iBAAiB,EAAE;QACjB,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC;QACzC,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,QAAQ;QACtB,KAAK,EAAE,oBAAoB;KAC5B;IACD,qBAAqB,EAAE;QACrB,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC;QACzC,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,QAAQ;QACtB,KAAK,EAAE,yBAAyB;KACjC;IACD,SAAS,EAAE;QACT,IAAI,EAAE,qBAAS,CAAC,OAAO;QACvB,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,KAAK;QACnB,KAAK,EAAE,YAAY;KACpB;IAGD,gBAAgB,EAAE;QAChB,IAAI,EAAE,qBAAS,CAAC,OAAO;QACvB,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,KAAK;QACnB,KAAK,EAAE,oBAAoB;KAC5B;IAED,SAAS,EAAE;QACT,IAAI,EAAE,qBAAS,CAAC,IAAI;QACpB,SAAS,EAAE,KAAK;KACjB;IACD,SAAS,EAAE;QACT,IAAI,EAAE,qBAAS,CAAC,IAAI;QACpB,SAAS,EAAE,KAAK;KACjB;CACF,EACD;IACE,SAAS,EAAT,oBAAS;IACT,SAAS,EAAE,UAAU;IACrB,SAAS,EAAE,UAAU;IACrB,UAAU,EAAE,IAAI;IAChB,WAAW,EAAE,IAAI;IACjB,OAAO,EAAE;QACP;YACE,MAAM,EAAE,IAAI;YACZ,MAAM,EAAE,CAAC,SAAS,CAAC;SACpB;KACF;CACF,CACF,CAAA"}