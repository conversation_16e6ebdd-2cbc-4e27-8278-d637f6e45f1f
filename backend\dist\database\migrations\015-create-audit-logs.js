"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('audit_logs', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        user_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'SET NULL'
        },
        action: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            comment: '操作类型：CREATE, UPDATE, DELETE, LOGIN, LOGOUT, VIEW等'
        },
        resource: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            comment: '操作的资源类型：user, article, post, comment等'
        },
        resource_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            comment: '操作的资源ID'
        },
        old_data: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            comment: '操作前的数据（JSON格式）'
        },
        new_data: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            comment: '操作后的数据（JSON格式）'
        },
        ip_address: {
            type: sequelize_1.DataTypes.STRING(45),
            allowNull: true,
            comment: '操作者的IP地址（支持IPv6）'
        },
        user_agent: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            comment: '用户代理字符串'
        },
        session_id: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: true,
            comment: '会话ID'
        },
        status: {
            type: sequelize_1.DataTypes.ENUM('success', 'failed', 'pending'),
            allowNull: false,
            defaultValue: 'success',
            comment: '操作状态'
        },
        error_message: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            comment: '错误信息（如果操作失败）'
        },
        duration: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            comment: '操作耗时（毫秒）'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    try {
        await queryInterface.addIndex('audit_logs', ['user_id']);
        await queryInterface.addIndex('audit_logs', ['action']);
        await queryInterface.addIndex('audit_logs', ['resource']);
        await queryInterface.addIndex('audit_logs', ['resource_id']);
        await queryInterface.addIndex('audit_logs', ['created_at']);
        await queryInterface.addIndex('audit_logs', ['status']);
        await queryInterface.addIndex('audit_logs', ['user_id', 'created_at']);
        await queryInterface.addIndex('audit_logs', ['resource', 'resource_id']);
        await queryInterface.addIndex('audit_logs', ['action', 'resource']);
    }
    catch (error) {
        console.log('Some indexes may already exist, skipping...', error);
    }
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('audit_logs');
};
exports.down = down;
//# sourceMappingURL=015-create-audit-logs.js.map