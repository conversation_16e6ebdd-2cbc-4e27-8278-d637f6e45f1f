{"version": 3, "file": "registration.js", "sourceRoot": "", "sources": ["../../src/middleware/registration.ts"], "names": [], "mappings": ";;;AACA,yDAA0D;AAC1D,iDAA4C;AAC5C,4DAA+D;AAC/D,wDAA2D;AAK9C,QAAA,2BAA2B,GAAG;IACzC,IAAA,wBAAI,EAAC,UAAU,CAAC;SACb,IAAI,EAAE;SACN,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;SAC7B,WAAW,CAAC,iBAAiB,CAAC;SAC9B,OAAO,CAAC,kBAAkB,CAAC;SAC3B,WAAW,CAAC,sBAAsB,CAAC;SACnC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE;QAChB,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAA;QAC/B,CAAC;QACD,IAAI,sCAAqB,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,CAAA;QAC9B,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC,CAAC;IAEJ,IAAA,wBAAI,EAAC,OAAO,CAAC;SACV,IAAI,EAAE;SACN,OAAO,EAAE;SACT,WAAW,CAAC,QAAQ,CAAC;SACrB,cAAc,EAAE;SAChB,QAAQ,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;SACtB,WAAW,CAAC,cAAc,CAAC;IAE9B,IAAA,wBAAI,EAAC,UAAU,CAAC;SACb,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;SAC9B,WAAW,CAAC,iBAAiB,CAAC;SAC9B,OAAO,CAAC,6DAA6D,CAAC;SACtE,WAAW,CAAC,qBAAqB,CAAC;IAErC,IAAA,wBAAI,EAAC,iBAAiB,CAAC;SACpB,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;QACzB,IAAI,KAAK,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAA;QAC/B,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC,CAAC;CACL,CAAA;AAKY,QAAA,oBAAoB,GAAG;IAClC,IAAA,wBAAI,EAAC,UAAU,CAAC;SACb,IAAI,EAAE;SACN,QAAQ,EAAE;SACV,WAAW,CAAC,SAAS,CAAC;SACtB,QAAQ,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;SACrB,WAAW,CAAC,cAAc,CAAC;IAE9B,IAAA,wBAAI,EAAC,UAAU,CAAC;SACb,QAAQ,EAAE;SACV,WAAW,CAAC,QAAQ,CAAC;SACrB,QAAQ,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;SACtB,WAAW,CAAC,cAAc,CAAC;CAC/B,CAAA;AAKY,QAAA,yBAAyB,GAAG;IACvC,IAAA,wBAAI,EAAC,OAAO,CAAC;SACV,IAAI,EAAE;SACN,OAAO,EAAE;SACT,WAAW,CAAC,QAAQ,CAAC;SACrB,cAAc,EAAE;CACpB,CAAA;AAKY,QAAA,kBAAkB,GAAG;IAChC,IAAA,wBAAI,EAAC,OAAO,CAAC;SACV,QAAQ,EAAE;SACV,WAAW,CAAC,UAAU,CAAC;IAE1B,IAAA,wBAAI,EAAC,UAAU,CAAC;SACb,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;SAC9B,WAAW,CAAC,iBAAiB,CAAC;SAC9B,OAAO,CAAC,6DAA6D,CAAC;SACtE,WAAW,CAAC,qBAAqB,CAAC;IAErC,IAAA,wBAAI,EAAC,iBAAiB,CAAC;SACpB,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;QACzB,IAAI,KAAK,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAA;QAC/B,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC,CAAC;CACL,CAAA;AAKY,QAAA,sBAAsB,GAAG;IACpC,IAAA,wBAAI,EAAC,OAAO,CAAC;SACV,QAAQ,EAAE;SACV,WAAW,CAAC,UAAU,CAAC;CAC3B,CAAA;AAKM,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IACvF,MAAM,MAAM,GAAG,IAAA,oCAAgB,EAAC,GAAG,CAAC,CAAA;IAEpC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;QACtB,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC5D,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,kBAAkB,CAAC,CAAA;IACtE,CAAC;IAED,IAAI,EAAE,CAAA;AACR,CAAC,CAAA;AATY,QAAA,eAAe,mBAS3B;AAKM,MAAM,yBAAyB,GAAG,KAAK,EAC5C,GAAY,EACZ,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,IAAI,CAAA;QAE7B,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,EAAE,CAAA;YACN,OAAM;QACR,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,sCAAqB,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAA;QACpF,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;YAC5B,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,YAAY,CAAC,OAAO,IAAI,QAAQ,EAAE,sBAAsB,CAAC,CAAA;QAClF,CAAC;QAED,IAAI,EAAE,CAAA;IACR,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAA;IACb,CAAC;AACH,CAAC,CAAA;AAtBY,QAAA,yBAAyB,6BAsBrC;AAKM,MAAM,sBAAsB,GAAG,KAAK,EACzC,GAAY,EACZ,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,IAAI,CAAA;QAE1B,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,IAAI,EAAE,CAAA;YACN,OAAM;QACR,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,sCAAqB,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAA;QAC9E,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;YAC5B,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,YAAY,CAAC,OAAO,IAAI,OAAO,EAAE,mBAAmB,CAAC,CAAA;QAC9E,CAAC;QAED,IAAI,EAAE,CAAA;IACR,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAA;IACb,CAAC;AACH,CAAC,CAAA;AAtBY,QAAA,sBAAsB,0BAsBlC;AAKM,MAAM,0BAA0B,GAAG,CACxC,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,IAAI,CAAC;QACH,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,IAAI,CAAA;QAE1B,MAAM,MAAM,GAAG,kCAAmB,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAA;QAClE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YAClB,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE,qBAAqB,CAAC,CAAA;QAC1E,CAAC;QAGD,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,CAAA;QAChC,IAAI,EAAE,CAAA;IACR,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAA;IACb,CAAC;AACH,CAAC,CAAA;AAnBY,QAAA,0BAA0B,8BAmBtC;AAKM,MAAM,8BAA8B,GAAG,CAC5C,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,IAAI,CAAC;QACH,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,IAAI,CAAA;QAE1B,MAAM,MAAM,GAAG,kCAAmB,CAAC,4BAA4B,CAAC,KAAK,CAAC,CAAA;QACtE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YAClB,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE,4BAA4B,CAAC,CAAA;QACjF,CAAC;QAGD,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,CAAA;QAChC,IAAI,EAAE,CAAA;IACR,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAA;IACb,CAAC;AACH,CAAC,CAAA;AAnBY,QAAA,8BAA8B,kCAmB1C;AAKY,QAAA,qBAAqB,GAAG,CAAC,GAAG,EAAE;IACzC,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAgD,CAAA;IACxE,MAAM,YAAY,GAAG,CAAC,CAAA;IACtB,MAAM,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;IAEhC,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC/D,MAAM,QAAQ,GAAG,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa,IAAI,SAAS,CAAA;QACpE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAGtB,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;YAC5C,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBACzB,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;YACrB,CAAC;QACH,CAAC;QAED,MAAM,cAAc,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QAE7C,IAAI,CAAC,cAAc,EAAE,CAAC;YAEpB,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,GAAG,SAAS,EAAE,CAAC,CAAA;YAChE,IAAI,EAAE,CAAA;YACN,OAAM;QACR,CAAC;QAED,IAAI,GAAG,GAAG,cAAc,CAAC,SAAS,EAAE,CAAC;YAEnC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,GAAG,SAAS,EAAE,CAAC,CAAA;YAChE,IAAI,EAAE,CAAA;YACN,OAAM;QACR,CAAC;QAED,IAAI,cAAc,CAAC,KAAK,IAAI,YAAY,EAAE,CAAC;YACzC,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,gBAAgB,EAAE,qBAAqB,CAAC,CAAA;QACjE,CAAC;QAGD,cAAc,CAAC,KAAK,EAAE,CAAA;QACtB,IAAI,EAAE,CAAA;IACR,CAAC,CAAA;AACH,CAAC,CAAC,EAAE,CAAA;AAKS,QAAA,sBAAsB,GAAG;IACpC,6BAAqB;IACrB,GAAG,mCAA2B;IAC9B,uBAAe;IACf,iCAAyB;IACzB,8BAAsB;CACvB,CAAA;AAKY,QAAA,eAAe,GAAG;IAC7B,GAAG,4BAAoB;IACvB,uBAAe;CAChB,CAAA;AAKY,QAAA,8BAA8B,GAAG;IAC5C,GAAG,iCAAyB;IAC5B,uBAAe;CAChB,CAAA;AAKY,QAAA,uBAAuB,GAAG;IACrC,GAAG,0BAAkB;IACrB,uBAAe;IACf,kCAA0B;CAC3B,CAAA;AAKY,QAAA,2BAA2B,GAAG;IACzC,GAAG,8BAAsB;IACzB,uBAAe;IACf,sCAA8B;CAC/B,CAAA"}