import { QueryInterface, DataTypes } from 'sequelize'

/**
 * 创建媒体文件表的迁移
 * 用于存储所有上传的媒体文件信息，包括图片、视频、音频和文档
 */
export const up = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.createTable('media', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
      comment: '媒体文件ID'
    },
    filename: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
      comment: '文件名（存储在服务器上的文件名）'
    },
    original_name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '原始文件名（用户上传时的文件名）'
    },
    mime_type: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: 'MIME类型'
    },
    size: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '文件大小（字节）'
    },
    url: {
      type: DataTypes.STRING(500),
      allowNull: false,
      comment: '文件访问URL'
    },
    thumbnail_url: {
      type: DataTypes.STRING(500),
      allowNull: true,
      comment: '缩略图URL（仅适用于图片和视频）'
    },
    width: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '图片/视频宽度（像素）'
    },
    height: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '图片/视频高度（像素）'
    },
    uploader_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      comment: '上传者用户ID'
    },
    category: {
      type: DataTypes.ENUM('image', 'video', 'audio', 'document'),
      allowNull: false,
      defaultValue: 'image',
      comment: '媒体类别'
    },
    tags: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '标签数组（JSON格式）'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '媒体描述'
    },
    is_public: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '是否公开可见'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间'
    }
  })

  // 创建索引以提高查询性能
  try {
    await queryInterface.addIndex('media', ['uploader_id'], {
      name: 'idx_media_uploader_id'
    })
  } catch {
    console.log('Index idx_media_uploader_id already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('media', ['category'], {
      name: 'idx_media_category'
    })
  } catch {
    console.log('Index idx_media_category already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('media', ['mime_type'], {
      name: 'idx_media_mime_type'
    })
  } catch {
    console.log('Index idx_media_mime_type already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('media', ['is_public'], {
      name: 'idx_media_is_public'
    })
  } catch {
    console.log('Index idx_media_is_public already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('media', ['created_at'], {
      name: 'idx_media_created_at'
    })
  } catch {
    console.log('Index idx_media_created_at already exists, skipping...')
  }

  // 创建复合索引
  try {
    await queryInterface.addIndex('media', ['category', 'is_public'], {
      name: 'idx_media_category_public'
    })
  } catch {
    console.log('Index idx_media_category_public already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('media', ['uploader_id', 'category'], {
      name: 'idx_media_uploader_category'
    })
  } catch {
    console.log('Index idx_media_uploader_category already exists, skipping...')
  }

  console.log('✅ Media table created successfully')
}

/**
 * 回滚迁移 - 删除媒体文件表
 */
export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.dropTable('media')
  console.log('✅ Media table dropped successfully')
}
