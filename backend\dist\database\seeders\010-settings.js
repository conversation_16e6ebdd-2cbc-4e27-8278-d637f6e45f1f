"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    console.log('⚙️ 开始创建用户设置种子数据...');
    const existingSettings = await queryInterface.select(null, 'settings', {});
    if (existingSettings.length > 0) {
        console.log('用户设置数据已存在，跳过创建...');
        return;
    }
    const users = await queryInterface.sequelize.query('SELECT id, username FROM users ORDER BY id', { type: sequelize_1.QueryTypes.SELECT });
    if (users.length === 0) {
        console.log('⚠️ 没有找到用户数据，跳过设置种子数据创建');
        return;
    }
    const now = new Date();
    const settingsData = users.map(user => ({
        user_id: user.id,
        display_name: user.username,
        theme: 'auto',
        language: 'zh-CN',
        timezone: 'Asia/Shanghai',
        items_per_page: 10,
        email_notifications: true,
        comment_notifications: true,
        system_notifications: true,
        profile_visibility: 'public',
        default_post_visibility: 'public',
        show_email: false,
        two_factor_enabled: false,
        created_at: now,
        updated_at: now
    }));
    await queryInterface.bulkInsert('settings', settingsData);
    console.log(`✅ 成功为 ${users.length} 个用户创建了默认设置`);
};
exports.up = up;
const down = async (queryInterface) => {
    console.log('🗑️ 删除用户设置种子数据...');
    await queryInterface.bulkDelete('settings', {}, {});
    console.log('✅ 用户设置种子数据删除完成');
};
exports.down = down;
//# sourceMappingURL=010-settings.js.map