import { DataTypes, Model, Optional } from 'sequelize'
import { sequelize } from '../config/database'
import bcrypt from 'bcryptjs'


/**
 * 用户模型的属性接口，定义了用户对象的基本字段结构
 */
export interface UserAttributes {
  id: number
  username: string
  email: string
  passwordHash: string
  isActive: boolean
  emailVerified: boolean
  emailVerifiedAt?: Date
  lastLoginAt?: Date
  passwordResetToken?: string
  passwordResetExpires?: Date
  emailVerificationToken?: string
  emailVerificationExpires?: Date
  createdAt: Date
  updatedAt: Date
}


/**
 * 用户创建时的属性接口，继承自 UserAttributes，但允许部分字段为空（id、createdAt、updatedAt 由系统自动生成）
 */
export interface UserCreationAttributes extends Optional<UserAttributes, 'id' | 'createdAt' | 'updatedAt'> { }


/**
 * 用户模型类，用于与数据库中的 users 表进行交互
 * 实现了 UserAttributes 接口，并扩展了 Sequelize 的 Model 类
 */
export class User extends Model<UserAttributes, UserCreationAttributes> implements UserAttributes {
  public id!: number
  public username!: string
  public email!: string
  public passwordHash!: string
  public isActive!: boolean
  public emailVerified!: boolean
  public emailVerifiedAt?: Date
  public lastLoginAt?: Date
  public passwordResetToken?: string
  public passwordResetExpires?: Date
  public emailVerificationToken?: string
  public emailVerificationExpires?: Date
  public createdAt!: Date
  public updatedAt!: Date


  /**
   * 验证给定的密码是否与当前用户的密码哈希匹配
   * @param password - 待验证的明文密码
   * @returns 返回一个 Promise，解析为布尔值，表示密码是否匹配
   */
  public async validatePassword(password: string): Promise<boolean> {
    return bcrypt.compare(password, this.passwordHash)
  }

  /**
   * 将用户实例转换为 JSON 对象，移除敏感字段 passwordHash
   * @returns 不包含 passwordHash 字段的用户属性对象
   */
  public toJSON(): Omit<UserAttributes, 'passwordHash'> {
    const values = { ...this.get() } as any
    delete values.passwordHash
    return values
  }


  /**
   * 对给定的明文密码进行哈希处理
   * @param password - 需要哈希的明文密码
   * @returns 返回一个 Promise，解析为哈希后的密码字符串
   */
  public static async hashPassword(password: string): Promise<string> {
    const saltRounds = 12
    return bcrypt.hash(password, saltRounds)
  }

  /**
   * 根据用户名查找用户
   * @param username - 要查找的用户名
   * @returns 返回一个 Promise，解析为 User 实例或 null（如果未找到）
   */
  public static async findByUsername(username: string): Promise<User | null> {
    return this.findOne({ where: { username } })
  }

  /**
   * 根据邮箱地址查找用户
   * @param email - 要查找的邮箱地址
   * @returns 返回一个 Promise，解析为 User 实例或 null（如果未找到）
   */
  public static async findByEmail(email: string): Promise<User | null> {
    return this.findOne({ where: { email } })
  }
}


/**
 * 初始化 User 模型，配置其字段、验证规则和生命周期钩子
 */
User.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        len: [3, 50],
        isAlphanumeric: true
      }
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true,
        len: [5, 100]
      }
    },
    passwordHash: {
      type: DataTypes.STRING(255),
      allowNull: false,
      field: 'password_hash'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      field: 'is_active'
    },
    emailVerified: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      field: 'email_verified'
    },
    emailVerifiedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'email_verified_at'
    },
    lastLoginAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'last_login_at'
    },
    passwordResetToken: {
      type: DataTypes.STRING(255),
      allowNull: true,
      field: 'password_reset_token'
    },
    passwordResetExpires: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'password_reset_expires'
    },
    emailVerificationToken: {
      type: DataTypes.STRING(255),
      allowNull: true,
      field: 'email_verification_token'
    },
    emailVerificationExpires: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'email_verification_expires'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  },
  {
    sequelize,
    modelName: 'User',
    tableName: 'users',
    timestamps: true,
    underscored: true,
    // 定义模型的生命周期钩子，在创建和更新前自动对密码进行哈希处理
    hooks: {
      beforeCreate: async (user: User) => {
        if (user.passwordHash) {
          user.passwordHash = await User.hashPassword(user.passwordHash)
        }
      },
      beforeUpdate: async (user: User) => {
        if (user.changed('passwordHash')) {
          user.passwordHash = await User.hashPassword(user.passwordHash)
        }
      }
    }
  }
)