import { QueryInterface, DataTypes } from 'sequelize'

/**
 * 执行数据库迁移升级操作，创建说说点赞表
 * 迁移序号：012 (修复命名冲突，原为009)
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 返回空的Promise，表示异步操作完成
 */
export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // 创建说说点赞表，记录用户对说说的点赞关系
  await queryInterface.createTable('post_likes', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    post_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'posts',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  })

  // 为说说ID和用户ID的组合添加唯一索引，确保同一用户不能重复点赞同一说说
  try {
    await queryInterface.addIndex('post_likes', ['post_id', 'user_id'], {
      unique: true,
      name: 'post_likes_post_user_unique'
    })
  } catch {
    console.log('Unique index post_likes_post_user_unique already exists, skipping...')
  }

  // 为说说ID字段添加索引以提高查询性能
  try {
    await queryInterface.addIndex('post_likes', ['post_id'])
  } catch {
    console.log('Index post_likes_post_id already exists, skipping...')
  }

  // 为用户ID字段添加索引以提高查询性能
  try {
    await queryInterface.addIndex('post_likes', ['user_id'])
  } catch {
    console.log('Index post_likes_user_id already exists, skipping...')
  }

  // 为创建时间字段添加索引以提高时间排序查询性能
  try {
    await queryInterface.addIndex('post_likes', ['created_at'])
  } catch {
    console.log('Index post_likes_created_at already exists, skipping...')
  }
}

/**
 * 执行数据库迁移降级操作，删除说说点赞表
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 返回空的Promise，表示异步操作完成
 */
export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.dropTable('post_likes')
}
