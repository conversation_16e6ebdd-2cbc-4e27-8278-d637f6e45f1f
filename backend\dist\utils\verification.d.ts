export declare enum VerificationType {
    EMAIL_VERIFICATION = "email_verification",
    PASSWORD_RESET = "password_reset",
    TWO_FACTOR_AUTH = "two_factor_auth"
}
interface VerificationData {
    userId?: number;
    email?: string;
    type: VerificationType;
    expiresAt: Date;
}
interface VerificationResult {
    valid: boolean;
    data?: VerificationData;
    error?: string;
}
export declare class VerificationService {
    private static generateVerificationToken;
    private static verifyVerificationToken;
    static generateEmailVerificationToken(email: string, userId?: number): string;
    static generatePasswordResetToken(email: string, userId: number): string;
    static generateTwoFactorCode(): string;
    static generateSecureToken(length?: number): string;
    static verifyToken(token: string): VerificationResult;
    static verifyEmailVerificationToken(token: string): VerificationResult;
    static verifyPasswordResetToken(token: string): VerificationResult;
    static hashVerificationCode(code: string): string;
    static verifyCode(code: string, hashedCode: string): boolean;
    static isTokenExpiringSoon(token: string): boolean;
    static generateUniqueId(): string;
    static isValidEmail(email: string): boolean;
    static validatePasswordStrength(password: string): {
        valid: boolean;
        errors: string[];
    };
    static validateUsername(username: string): {
        valid: boolean;
        errors: string[];
    };
    static generateTemporaryPassword(length?: number): string;
}
export {};
//# sourceMappingURL=verification.d.ts.map