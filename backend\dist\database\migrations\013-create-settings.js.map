{"version": 3, "file": "013-create-settings.js", "sourceRoot": "", "sources": ["../../../src/database/migrations/013-create-settings.ts"], "names": [], "mappings": ";;;AAAA,yCAAqD;AAO9C,MAAM,EAAE,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IACxE,MAAM,cAAc,CAAC,WAAW,CAAC,UAAU,EAAE;QAC3C,EAAE,EAAE;YACF,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK;SACjB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,MAAM,EAAE,IAAI;YACZ,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB;QAGD,YAAY,EAAE;YACZ,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,MAAM;SAChB;QACD,MAAM,EAAE;YACN,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,OAAO;SACjB;QACD,GAAG,EAAE;YACH,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,MAAM;SAChB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,MAAM;SAChB;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,KAAK;SACf;QAGD,KAAK,EAAE;YACL,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;YAC7C,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,MAAM;YACpB,OAAO,EAAE,MAAM;SAChB;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,OAAO;YACrB,OAAO,EAAE,MAAM;SAChB;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,eAAe;YAC7B,OAAO,EAAE,MAAM;SAChB;QACD,cAAc,EAAE;YACd,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,QAAQ;SAClB;QAGD,mBAAmB,EAAE;YACnB,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,MAAM;SAChB;QACD,qBAAqB,EAAE;YACrB,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,MAAM;SAChB;QACD,oBAAoB,EAAE;YACpB,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,MAAM;SAChB;QAGD,kBAAkB,EAAE;YAClB,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC;YACzC,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,QAAQ;YACtB,OAAO,EAAE,SAAS;SACnB;QACD,uBAAuB,EAAE;YACvB,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC;YACzC,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,QAAQ;YACtB,OAAO,EAAE,SAAS;SACnB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,KAAK;YACnB,OAAO,EAAE,QAAQ;SAClB;QAGD,kBAAkB,EAAE;YAClB,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,KAAK;YACnB,OAAO,EAAE,MAAM;SAChB;QAED,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;SAC5B;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;SAC5B;KACF,EAAE;QACD,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,oBAAoB;KAC9B,CAAC,CAAA;IAGF,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,SAAS,CAAC,EAAE;YACrD,MAAM,EAAE,IAAI;YACZ,IAAI,EAAE,yBAAyB;SAChC,CAAC,CAAA;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAA;IAC1E,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,EAAE;YACnD,IAAI,EAAE,sBAAsB;SAC7B,CAAC,CAAA;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAA;IACvE,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,oBAAoB,CAAC,EAAE;YAChE,IAAI,EAAE,mCAAmC;SAC1C,CAAC,CAAA;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,qEAAqE,CAAC,CAAA;IACpF,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,YAAY,CAAC,EAAE;YACxD,IAAI,EAAE,2BAA2B;SAClC,CAAC,CAAA;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAA;IAC5E,CAAC;AACH,CAAC,CAAA;AAzKY,QAAA,EAAE,MAyKd;AAKM,MAAM,IAAI,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IAC1E,MAAM,cAAc,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;AAC5C,CAAC,CAAA;AAFY,QAAA,IAAI,QAEhB;AAKY,QAAA,aAAa,GAAG;IAC3B,IAAI,EAAE,gCAAgC;IACtC,WAAW,EAAE,SAAS;IACtB,OAAO,EAAE,OAAO;IAChB,MAAM,EAAE,QAAQ;IAChB,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;CAClC,CAAA"}