{"version": 3, "file": "UserRole.js", "sourceRoot": "", "sources": ["../../src/models/UserRole.ts"], "names": [], "mappings": ";;;AAAA,yCAAsD;AACtD,iDAA8C;AAwB9C,MAAa,QAAS,SAAQ,iBAAqD;IAmB1E,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,MAAc;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,KAAK,EAAE,EAAE,MAAM,EAAE;YACjB,OAAO,EAAE,CAAC,MAAM,CAAC;SAClB,CAAC,CAAA;IACJ,CAAC;IAOM,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,MAAc;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,KAAK,EAAE,EAAE,MAAM,EAAE;YACjB,OAAO,EAAE,CAAC,MAAM,CAAC;SAClB,CAAC,CAAA;IACJ,CAAC;IAQM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAc,EAAE,MAAc;QACxD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,CAAA;QAC7D,OAAO,KAAK,GAAG,CAAC,CAAA;IAClB,CAAC;IAQM,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,QAAgB;QAChE,MAAM,IAAI,GAAG,oBAAS,CAAC,MAAM,CAAC,IAAW,CAAA;QACzC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC;YAC9B,KAAK,EAAE,EAAE,MAAM,EAAE;YACjB,OAAO,EAAE,CAAC;oBACR,KAAK,EAAE,IAAI;oBACX,EAAE,EAAE,MAAM;oBACV,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;iBAC1B,CAAC;SACH,CAAC,CAAA;QACF,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;QACrD,OAAO,KAAK,GAAG,CAAC,CAAA;IAClB,CAAC;IASM,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,MAAc,EAAE,MAAc,EAAE,UAAmB;QAEhF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,CAAA;QAClE,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,QAAQ,CAAA;QACjB,CAAC;QAED,MAAM,UAAU,GAAQ;YACtB,MAAM;YACN,MAAM;YACN,UAAU,EAAE,IAAI,IAAI,EAAE;SACvB,CAAA;QAED,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;YAC7B,UAAU,CAAC,UAAU,GAAG,UAAU,CAAA;QACpC,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAA;IAChC,CAAC;IAQM,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,MAAc,EAAE,MAAc;QAC3D,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,CAAA;IACpD,CAAC;IASM,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,MAAc,EAAE,OAAiB,EAAE,UAAmB;QAEpF,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,CAAA;QAGzC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvB,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBACrC,MAAM,IAAI,GAAQ;oBAChB,MAAM;oBACN,MAAM;oBACN,UAAU,EAAE,IAAI,IAAI,EAAE;iBACvB,CAAA;gBACD,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;oBAC7B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;gBAC9B,CAAC;gBACD,OAAO,IAAI,CAAA;YACb,CAAC,CAAC,CAAA;YACF,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAA;QAClC,CAAC;IACH,CAAC;IAOM,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,MAAc;QAC7C,MAAM,IAAI,GAAG,oBAAS,CAAC,MAAM,CAAC,IAAW,CAAA;QACzC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;YACnC,KAAK,EAAE,EAAE,MAAM,EAAE;YACjB,OAAO,EAAE,CAAC;oBACR,KAAK,EAAE,IAAI;oBACX,EAAE,EAAE,MAAM;oBACV,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;iBAC1B,CAAC;SACH,CAAC,CAAA;QAEF,OAAO,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA;IACrC,CAAC;IAOM,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAc;QACnD,MAAM,IAAI,GAAG,oBAAS,CAAC,MAAM,CAAC,IAAW,CAAA;QACzC,MAAM,UAAU,GAAG,oBAAS,CAAC,MAAM,CAAC,UAAiB,CAAA;QACrD,MAAM,cAAc,GAAG,oBAAS,CAAC,MAAM,CAAC,cAAqB,CAAA;QAE7D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;YACnC,KAAK,EAAE,EAAE,MAAM,EAAE;YACjB,OAAO,EAAE,CAAC;oBACR,KAAK,EAAE,IAAI;oBACX,EAAE,EAAE,MAAM;oBACV,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;oBACzB,OAAO,EAAE,CAAC;4BACR,KAAK,EAAE,cAAc;4BACrB,EAAE,EAAE,iBAAiB;4BACrB,OAAO,EAAE,CAAC;oCACR,KAAK,EAAE,UAAU;oCACjB,EAAE,EAAE,YAAY;oCAChB,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;iCAC1B,CAAC;yBACH,CAAC;iBACH,CAAC;SACH,CAAC,CAAA;QAEF,MAAM,WAAW,GAAU,EAAE,CAAA;QAC7B,MAAM,aAAa,GAAG,IAAI,GAAG,EAAE,CAAA;QAE/B,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;gBACnD,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,EAAO,EAAE,EAAE;oBAChD,IAAI,EAAE,CAAC,UAAU,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;wBAC1D,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,CAAA;wBAC/B,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA;oBACrC,CAAC;gBACH,CAAC,CAAC,CAAA;YACJ,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,OAAO,WAAW,CAAA;IACpB,CAAC;IAQM,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,cAAsB;QACtE,MAAM,IAAI,GAAG,oBAAS,CAAC,MAAM,CAAC,IAAW,CAAA;QACzC,MAAM,UAAU,GAAG,oBAAS,CAAC,MAAM,CAAC,UAAiB,CAAA;QACrD,MAAM,cAAc,GAAG,oBAAS,CAAC,MAAM,CAAC,cAAqB,CAAA;QAE7D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC;YAC9B,KAAK,EAAE,EAAE,MAAM,EAAE;YACjB,OAAO,EAAE,CAAC;oBACR,KAAK,EAAE,IAAI;oBACX,EAAE,EAAE,MAAM;oBACV,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;oBACzB,OAAO,EAAE,CAAC;4BACR,KAAK,EAAE,cAAc;4BACrB,EAAE,EAAE,iBAAiB;4BACrB,OAAO,EAAE,CAAC;oCACR,KAAK,EAAE,UAAU;oCACjB,EAAE,EAAE,YAAY;oCAChB,KAAK,EAAE;wCACL,IAAI,EAAE,cAAc;wCACpB,QAAQ,EAAE,IAAI;qCACf;iCACF,CAAC;yBACH,CAAC;iBACH,CAAC;SACH,CAAC,CAAA;QAEF,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;QACrD,OAAO,KAAK,GAAG,CAAC,CAAA;IAClB,CAAC;IAOM,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAc;QACjD,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,CAAA;IAC1C,CAAC;IAMM,MAAM;QACX,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAA;QAChC,OAAO,MAAM,CAAA;IACf,CAAC;CACF;AA3PD,4BA2PC;AAKD,QAAQ,CAAC,IAAI,CACX;IACE,EAAE,EAAE;QACF,IAAI,EAAE,qBAAS,CAAC,OAAO;QACvB,UAAU,EAAE,IAAI;QAChB,aAAa,EAAE,IAAI;QACnB,SAAS,EAAE,KAAK;KACjB;IACD,MAAM,EAAE;QACN,IAAI,EAAE,qBAAS,CAAC,OAAO;QACvB,SAAS,EAAE,KAAK;QAChB,KAAK,EAAE,SAAS;QAChB,UAAU,EAAE;YACV,KAAK,EAAE,OAAO;YACd,GAAG,EAAE,IAAI;SACV;QACD,QAAQ,EAAE,SAAS;QACnB,QAAQ,EAAE,SAAS;QACnB,OAAO,EAAE,MAAM;KAChB;IACD,MAAM,EAAE;QACN,IAAI,EAAE,qBAAS,CAAC,OAAO;QACvB,SAAS,EAAE,KAAK;QAChB,KAAK,EAAE,SAAS;QAChB,UAAU,EAAE;YACV,KAAK,EAAE,OAAO;YACd,GAAG,EAAE,IAAI;SACV;QACD,QAAQ,EAAE,SAAS;QACnB,QAAQ,EAAE,SAAS;QACnB,OAAO,EAAE,MAAM;KAChB;IACD,UAAU,EAAE;QACV,IAAI,EAAE,qBAAS,CAAC,OAAO;QACvB,SAAS,EAAE,IAAI;QACf,KAAK,EAAE,aAAa;QACpB,UAAU,EAAE;YACV,KAAK,EAAE,OAAO;YACd,GAAG,EAAE,IAAI;SACV;QACD,QAAQ,EAAE,SAAS;QACnB,QAAQ,EAAE,UAAU;QACpB,OAAO,EAAE,OAAO;KACjB;IACD,UAAU,EAAE;QACV,IAAI,EAAE,qBAAS,CAAC,IAAI;QACpB,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;QAC3B,KAAK,EAAE,aAAa;QACpB,OAAO,EAAE,MAAM;KAChB;IACD,SAAS,EAAE;QACT,IAAI,EAAE,qBAAS,CAAC,IAAI;QACpB,SAAS,EAAE,KAAK;QAChB,KAAK,EAAE,YAAY;KACpB;IACD,SAAS,EAAE;QACT,IAAI,EAAE,qBAAS,CAAC,IAAI;QACpB,SAAS,EAAE,KAAK;QAChB,KAAK,EAAE,YAAY;KACpB;CACF,EACD;IACE,SAAS,EAAT,oBAAS;IACT,SAAS,EAAE,UAAU;IACrB,SAAS,EAAE,YAAY;IACvB,UAAU,EAAE,IAAI;IAChB,WAAW,EAAE,IAAI;IACjB,OAAO,EAAE;QACP;YACE,MAAM,EAAE,IAAI;YACZ,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;SAC/B;QACD;YACE,MAAM,EAAE,CAAC,SAAS,CAAC;SACpB;QACD;YACE,MAAM,EAAE,CAAC,SAAS,CAAC;SACpB;QACD;YACE,MAAM,EAAE,CAAC,aAAa,CAAC;SACxB;QACD;YACE,MAAM,EAAE,CAAC,aAAa,CAAC;SACxB;KACF;CACF,CACF,CAAA"}