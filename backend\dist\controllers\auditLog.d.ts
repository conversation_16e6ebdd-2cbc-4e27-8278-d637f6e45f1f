import { Request, Response, NextFunction } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: number;
        username: string;
        email: string;
    };
}
export declare class AuditLogController {
    static getLogs(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void>;
    static getMyLogs(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void>;
    static getLogById(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void>;
    static getStats(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void>;
    static getMyStats(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void>;
    static cleanupLogs(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void>;
    static getActions(req: Request, res: Response, next: NextFunction): Promise<void>;
    static getResources(req: Request, res: Response, next: NextFunction): Promise<void>;
}
export declare const getLogs: typeof AuditLogController.getLogs, getMyLogs: typeof AuditLogController.getMyLogs, getLogById: typeof AuditLogController.getLogById, getStats: typeof AuditLogController.getStats, getMyStats: typeof AuditLogController.getMyStats, cleanupLogs: typeof AuditLogController.cleanupLogs, getActions: typeof AuditLogController.getActions, getResources: typeof AuditLogController.getResources;
export default AuditLogController;
//# sourceMappingURL=auditLog.d.ts.map