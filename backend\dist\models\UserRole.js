"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserRole = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
class UserRole extends sequelize_1.Model {
    static async findByUserId(userId) {
        return this.findAll({
            where: { userId },
            include: ['role']
        });
    }
    static async findByRoleId(roleId) {
        return this.findAll({
            where: { roleId },
            include: ['user']
        });
    }
    static async hasRole(userId, roleId) {
        const count = await this.count({ where: { userId, roleId } });
        return count > 0;
    }
    static async hasRoleByName(userId, roleName) {
        const Role = database_1.sequelize.models.Role;
        const result = await this.count({
            where: { userId },
            include: [{
                    model: Role,
                    as: 'role',
                    where: { name: roleName }
                }]
        });
        const count = typeof result === 'number' ? result : 0;
        return count > 0;
    }
    static async assignRole(userId, roleId, assignedBy) {
        const existing = await this.findOne({ where: { userId, roleId } });
        if (existing) {
            return existing;
        }
        const createData = {
            userId,
            roleId,
            assignedAt: new Date()
        };
        if (assignedBy !== undefined) {
            createData.assignedBy = assignedBy;
        }
        return this.create(createData);
    }
    static async removeRole(userId, roleId) {
        return this.destroy({ where: { userId, roleId } });
    }
    static async assignRoles(userId, roleIds, assignedBy) {
        await this.destroy({ where: { userId } });
        if (roleIds.length > 0) {
            const userRoles = roleIds.map(roleId => {
                const data = {
                    userId,
                    roleId,
                    assignedAt: new Date()
                };
                if (assignedBy !== undefined) {
                    data.assignedBy = assignedBy;
                }
                return data;
            });
            await this.bulkCreate(userRoles);
        }
    }
    static async getUserRoles(userId) {
        const Role = database_1.sequelize.models.Role;
        const userRoles = await this.findAll({
            where: { userId },
            include: [{
                    model: Role,
                    as: 'role',
                    where: { isActive: true }
                }]
        });
        return userRoles.map(ur => ur.role);
    }
    static async getUserPermissions(userId) {
        const Role = database_1.sequelize.models.Role;
        const Permission = database_1.sequelize.models.Permission;
        const RolePermission = database_1.sequelize.models.RolePermission;
        const userRoles = await this.findAll({
            where: { userId },
            include: [{
                    model: Role,
                    as: 'role',
                    where: { isActive: true },
                    include: [{
                            model: RolePermission,
                            as: 'rolePermissions',
                            include: [{
                                    model: Permission,
                                    as: 'permission',
                                    where: { isActive: true }
                                }]
                        }]
                }]
        });
        const permissions = [];
        const permissionIds = new Set();
        userRoles.forEach(userRole => {
            if (userRole.role && userRole.role.rolePermissions) {
                userRole.role.rolePermissions.forEach((rp) => {
                    if (rp.permission && !permissionIds.has(rp.permission.id)) {
                        permissions.push(rp.permission);
                        permissionIds.add(rp.permission.id);
                    }
                });
            }
        });
        return permissions;
    }
    static async hasPermission(userId, permissionName) {
        const Role = database_1.sequelize.models.Role;
        const Permission = database_1.sequelize.models.Permission;
        const RolePermission = database_1.sequelize.models.RolePermission;
        const result = await this.count({
            where: { userId },
            include: [{
                    model: Role,
                    as: 'role',
                    where: { isActive: true },
                    include: [{
                            model: RolePermission,
                            as: 'rolePermissions',
                            include: [{
                                    model: Permission,
                                    as: 'permission',
                                    where: {
                                        name: permissionName,
                                        isActive: true
                                    }
                                }]
                        }]
                }]
        });
        const count = typeof result === 'number' ? result : 0;
        return count > 0;
    }
    static async getRoleUserCount(roleId) {
        return this.count({ where: { roleId } });
    }
    toJSON() {
        const values = { ...this.get() };
        return values;
    }
}
exports.UserRole = UserRole;
UserRole.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
    },
    userId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        field: 'user_id',
        references: {
            model: 'users',
            key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: '用户ID'
    },
    roleId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        field: 'role_id',
        references: {
            model: 'roles',
            key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: '角色ID'
    },
    assignedBy: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        field: 'assigned_by',
        references: {
            model: 'users',
            key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: '分配者ID'
    },
    assignedAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        field: 'assigned_at',
        comment: '分配时间'
    },
    createdAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        field: 'created_at'
    },
    updatedAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        field: 'updated_at'
    }
}, {
    sequelize: database_1.sequelize,
    modelName: 'UserRole',
    tableName: 'user_roles',
    timestamps: true,
    underscored: true,
    indexes: [
        {
            unique: true,
            fields: ['user_id', 'role_id']
        },
        {
            fields: ['user_id']
        },
        {
            fields: ['role_id']
        },
        {
            fields: ['assigned_by']
        },
        {
            fields: ['assigned_at']
        }
    ]
});
//# sourceMappingURL=UserRole.js.map