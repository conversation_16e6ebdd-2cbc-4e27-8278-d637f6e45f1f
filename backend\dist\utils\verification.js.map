{"version": 3, "file": "verification.js", "sourceRoot": "", "sources": ["../../src/utils/verification.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA2B;AAC3B,gEAA8B;AAK9B,IAAY,gBAIX;AAJD,WAAY,gBAAgB;IAC1B,6DAAyC,CAAA;IACzC,qDAAiC,CAAA;IACjC,uDAAmC,CAAA;AACrC,CAAC,EAJW,gBAAgB,gCAAhB,gBAAgB,QAI3B;AAyBD,MAAa,mBAAmB;IAItB,MAAM,CAAC,yBAAyB,CAAC,OAAyB;QAChE,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAA;QACrC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;QAC9C,CAAC;QAED,OAAO,sBAAG,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE;YAC/B,SAAS,EAAE,KAAK;SACjB,CAAC,CAAA;IACJ,CAAC;IAKO,MAAM,CAAC,uBAAuB,CAAC,KAAa;QAClD,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAA;QACrC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;QAC9C,CAAC;QAED,OAAO,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAqB,CAAA;IACtD,CAAC;IAID,MAAM,CAAC,8BAA8B,CAAC,KAAa,EAAE,MAAe;QAClE,MAAM,OAAO,GAAqB;YAChC,KAAK;YACL,MAAM,EAAE,MAAO;YACf,IAAI,EAAE,gBAAgB,CAAC,kBAAkB;YACzC,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;SACtD,CAAA;QAED,OAAO,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAA;IAChD,CAAC;IAKD,MAAM,CAAC,0BAA0B,CAAC,KAAa,EAAE,MAAc;QAC7D,MAAM,OAAO,GAAqB;YAChC,KAAK;YACL,MAAM;YACN,IAAI,EAAE,gBAAgB,CAAC,cAAc;YACrC,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;SACjD,CAAA;QAED,OAAO,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAA;IAChD,CAAC;IAKD,MAAM,CAAC,qBAAqB;QAC1B,OAAO,gBAAM,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAA;IACpD,CAAC;IAKD,MAAM,CAAC,mBAAmB,CAAC,SAAiB,EAAE;QAC5C,OAAO,gBAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;IACnD,CAAC;IAKD,MAAM,CAAC,WAAW,CAAC,KAAa;QAC9B,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAA;YAGnD,IAAI,IAAI,IAAI,EAAE,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC7C,OAAO;oBACL,KAAK,EAAE,KAAK;oBACZ,KAAK,EAAE,mBAAmB;iBAC3B,CAAA;YACH,CAAC;YAED,OAAO;gBACL,KAAK,EAAE,IAAI;gBACX,IAAI,EAAE,OAAO;aACd,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,KAAK,EAAE,KAAK;gBACZ,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAChE,CAAA;QACH,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,4BAA4B,CAAC,KAAa;QAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;QAEtC,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YAClC,OAAO,MAAM,CAAA;QACf,CAAC;QAED,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;YAC7D,OAAO;gBACL,KAAK,EAAE,KAAK;gBACZ,KAAK,EAAE,oBAAoB;aAC5B,CAAA;QACH,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAKD,MAAM,CAAC,wBAAwB,CAAC,KAAa;QAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;QAEtC,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YAClC,OAAO,MAAM,CAAA;QACf,CAAC;QAED,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,gBAAgB,CAAC,cAAc,EAAE,CAAC;YACzD,OAAO;gBACL,KAAK,EAAE,KAAK;gBACZ,KAAK,EAAE,oBAAoB;aAC5B,CAAA;QACH,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAKD,MAAM,CAAC,oBAAoB,CAAC,IAAY;QACtC,OAAO,gBAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IAC/D,CAAC;IAKD,MAAM,CAAC,UAAU,CAAC,IAAY,EAAE,UAAkB;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;QACjD,OAAO,gBAAM,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAA;IAChF,CAAC;IAKD,MAAM,CAAC,mBAAmB,CAAC,KAAa;QACtC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAA;YACnD,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;YAC7C,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAA;YACtB,MAAM,qBAAqB,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;YAEtE,OAAO,SAAS,IAAI,qBAAqB,CAAA;QAC3C,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,gBAAgB;QACrB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;QACzC,MAAM,UAAU,GAAG,gBAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;QACxD,OAAO,GAAG,SAAS,IAAI,UAAU,EAAE,CAAA;IACrC,CAAC;IAKD,MAAM,CAAC,YAAY,CAAC,KAAa;QAC/B,MAAM,UAAU,GAAG,4BAA4B,CAAA;QAC/C,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAC/B,CAAC;IAKD,MAAM,CAAC,wBAAwB,CAAC,QAAgB;QAI9C,MAAM,MAAM,GAAa,EAAE,CAAA;QAE3B,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QACzB,CAAC;QAED,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QAC7B,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QAC3B,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QAC3B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QACzB,CAAC;QAED,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QAC3B,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;SACP,CAAA;IACH,CAAC;IAKD,MAAM,CAAC,gBAAgB,CAAC,QAAgB;QAItC,MAAM,MAAM,GAAa,EAAE,CAAA;QAE3B,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QAC1B,CAAC;QAED,IAAI,QAAQ,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QAC7B,CAAC;QAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAA;QACrC,CAAC;QAED,IAAI,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QAC3B,CAAC;QAGD,MAAM,aAAa,GAAG;YACpB,OAAO,EAAE,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM;YAChE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW;SAC5D,CAAA;QAED,IAAI,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAA;QACnC,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;SACP,CAAA;IACH,CAAC;IAKD,MAAM,CAAC,yBAAyB,CAAC,SAAiB,EAAE;QAClD,MAAM,SAAS,GAAG,4BAA4B,CAAA;QAC9C,MAAM,SAAS,GAAG,4BAA4B,CAAA;QAC9C,MAAM,OAAO,GAAG,YAAY,CAAA;QAC5B,MAAM,OAAO,GAAG,UAAU,CAAA;QAE1B,MAAM,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAG,OAAO,GAAG,OAAO,CAAA;QAE1D,IAAI,QAAQ,GAAG,EAAE,CAAA;QAGjB,QAAQ,IAAI,SAAS,CAAC,gBAAM,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAA;QACzD,QAAQ,IAAI,SAAS,CAAC,gBAAM,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAA;QACzD,QAAQ,IAAI,OAAO,CAAC,gBAAM,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAA;QACrD,QAAQ,IAAI,OAAO,CAAC,gBAAM,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAA;QAGrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,QAAQ,IAAI,QAAQ,CAAC,gBAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAA;QACzD,CAAC;QAGD,OAAO,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,gBAAM,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;IACxE,CAAC;CACF;AAlSD,kDAkSC"}