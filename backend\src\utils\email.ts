import nodemailer from 'nodemailer'
import { createError } from '../middleware/errorHandler'

/**
 * 邮件配置接口
 */
interface EmailConfig {
  host: string
  port: number
  secure: boolean
  auth: {
    user: string
    pass: string
  }
}

/**
 * 邮件发送选项接口
 */
interface EmailOptions {
  to: string | string[]
  subject: string
  text?: string
  html?: string
  from?: string
}

/**
 * 邮件模板数据接口
 */
interface EmailTemplateData {
  [key: string]: any
}

/**
 * 邮件工具类
 * 提供邮件发送、模板渲染等功能
 */
export class EmailService {
  private static transporter: nodemailer.Transporter | null = null
  private static config: EmailConfig | null = null

  /**
   * 初始化邮件服务
   */
  static async initialize(): Promise<void> {
    try {
      // 检查是否配置了邮件服务
      if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.SMTP_PASS) {
        console.log('⚠️ Email service not configured, running in development mode without email')
        return
      }

      this.config = {
        host: process.env.SMTP_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER || '',
          pass: process.env.SMTP_PASS || ''
        }
      }

      // 创建邮件传输器
      this.transporter = nodemailer.createTransport(this.config)

      // 验证配置
      await this.transporter.verify()
      console.log('✅ Email service initialized successfully')
    } catch (error) {
      console.error('❌ Email service initialization failed:', error)
      if (process.env.NODE_ENV === 'production') {
        throw createError(500, 'Email service initialization failed', 'EMAIL_INIT_ERROR')
      } else {
        console.log('⚠️ Email service failed in development mode, continuing without email')
      }
    }
  }

  /**
   * 发送邮件
   */
  static async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      if (!this.transporter) {
        await this.initialize()
      }

      // 如果邮件服务未配置，在开发环境中跳过发送
      if (!this.transporter) {
        console.log('📧 Email would be sent (development mode):', {
          to: options.to,
          subject: options.subject
        })
        return true
      }

      const mailOptions = {
        from: options.from || process.env.SMTP_FROM || process.env.SMTP_USER,
        to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
        subject: options.subject,
        text: options.text,
        html: options.html
      }

      const result = await this.transporter!.sendMail(mailOptions)
      console.log('📧 Email sent successfully:', result.messageId)
      return true
    } catch (error) {
      console.error('❌ Email sending failed:', error)
      return false
    }
  }

  /**
   * 发送验证邮件
   */
  static async sendVerificationEmail(email: string, token: string, username: string): Promise<boolean> {
    const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${token}`

    const html = this.renderVerificationTemplate({
      username,
      verificationUrl,
      appName: process.env.APP_NAME || 'Personal Blog'
    })

    return await this.sendEmail({
      to: email,
      subject: '验证您的邮箱地址',
      html
    })
  }

  /**
   * 发送密码重置邮件
   */
  static async sendPasswordResetEmail(email: string, token: string, username: string): Promise<boolean> {
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${token}`

    const html = this.renderPasswordResetTemplate({
      username,
      resetUrl,
      appName: process.env.APP_NAME || 'Personal Blog'
    })

    return await this.sendEmail({
      to: email,
      subject: '重置您的密码',
      html
    })
  }

  /**
   * 发送欢迎邮件
   */
  static async sendWelcomeEmail(email: string, username: string): Promise<boolean> {
    const html = this.renderWelcomeTemplate({
      username,
      loginUrl: `${process.env.FRONTEND_URL}/login`,
      appName: process.env.APP_NAME || 'Personal Blog'
    })

    return await this.sendEmail({
      to: email,
      subject: '欢迎加入我们！',
      html
    })
  }

  /**
   * 渲染邮箱验证模板
   */
  private static renderVerificationTemplate(data: EmailTemplateData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>邮箱验证</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #007bff; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .button { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${data.appName}</h1>
          </div>
          <div class="content">
            <h2>验证您的邮箱地址</h2>
            <p>亲爱的 ${data.username}，</p>
            <p>感谢您注册 ${data.appName}！请点击下面的按钮验证您的邮箱地址：</p>
            <p style="text-align: center;">
              <a href="${data.verificationUrl}" class="button">验证邮箱</a>
            </p>
            <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
            <p style="word-break: break-all;">${data.verificationUrl}</p>
            <p>此链接将在24小时后失效。</p>
          </div>
          <div class="footer">
            <p>如果您没有注册此账户，请忽略此邮件。</p>
            <p>&copy; 2024 ${data.appName}. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `
  }

  /**
   * 渲染密码重置模板
   */
  private static renderPasswordResetTemplate(data: EmailTemplateData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>密码重置</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .button { display: inline-block; padding: 12px 24px; background: #dc3545; color: white; text-decoration: none; border-radius: 4px; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${data.appName}</h1>
          </div>
          <div class="content">
            <h2>重置您的密码</h2>
            <p>亲爱的 ${data.username}，</p>
            <p>我们收到了您的密码重置请求。请点击下面的按钮重置您的密码：</p>
            <p style="text-align: center;">
              <a href="${data.resetUrl}" class="button">重置密码</a>
            </p>
            <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
            <p style="word-break: break-all;">${data.resetUrl}</p>
            <p>此链接将在1小时后失效。</p>
          </div>
          <div class="footer">
            <p>如果您没有请求重置密码，请忽略此邮件。</p>
            <p>&copy; 2024 ${data.appName}. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `
  }

  /**
   * 渲染欢迎邮件模板
   */
  private static renderWelcomeTemplate(data: EmailTemplateData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>欢迎加入</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #28a745; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .button { display: inline-block; padding: 12px 24px; background: #28a745; color: white; text-decoration: none; border-radius: 4px; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>欢迎加入 ${data.appName}！</h1>
          </div>
          <div class="content">
            <h2>欢迎，${data.username}！</h2>
            <p>恭喜您成功注册 ${data.appName}！</p>
            <p>您现在可以开始使用我们的服务了：</p>
            <ul>
              <li>创建和发布文章</li>
              <li>与其他用户互动</li>
              <li>个性化您的个人资料</li>
            </ul>
            <p style="text-align: center;">
              <a href="${data.loginUrl}" class="button">立即登录</a>
            </p>
          </div>
          <div class="footer">
            <p>感谢您选择 ${data.appName}！</p>
            <p>&copy; 2024 ${data.appName}. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `
  }
}
