import { Request, Response, NextFunction } from 'express'
import { AuditLogService } from '../services/auditLog'
import { createError } from '../middleware/errorHandler'
import Joi from 'joi'
import { getIdParam, getPaginationParams, getSearchParam } from '../utils/paramValidation'

/**
 * 扩展 Express 的 Request 接口，添加用户认证信息
 */
interface AuthenticatedRequest extends Request {
  user?: {
    id: number
    username: string
    email: string
  }
}

/**
 * 查询操作日志的参数验证模式
 */
const queryLogsSchema = Joi.object({
  userId: Joi.number().integer().min(1).optional(),
  action: Joi.string().max(50).optional(),
  resource: Joi.string().max(50).optional(),
  resourceId: Joi.number().integer().min(1).optional(),
  status: Joi.string().valid('success', 'failed', 'pending').optional(),
  startDate: Joi.date().optional(),
  endDate: Joi.date().optional(),
  ipAddress: Joi.string().max(45).optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  orderBy: Joi.string().valid('createdAt', 'action', 'resource').default('createdAt'),
  orderDirection: Joi.string().valid('ASC', 'DESC').default('DESC')
})

/**
 * 统计查询参数验证模式
 */
const statsQuerySchema = Joi.object({
  userId: Joi.number().integer().min(1).optional(),
  startDate: Joi.date().optional(),
  endDate: Joi.date().optional(),
  resource: Joi.string().max(50).optional(),
  action: Joi.string().max(50).optional(),
  days: Joi.number().integer().min(1).max(365).default(30)
})

/**
 * 操作日志控制器类
 * 提供操作日志相关的API接口
 */
export class AuditLogController {
  /**
   * 获取操作日志列表
   * @param req - Express请求对象
   * @param res - Express响应对象
   * @param next - Express下一个中间件函数
   */
  static async getLogs(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      // 验证查询参数
      const { error, value } = queryLogsSchema.validate(req.query)
      if (error) {
        throw createError(400, error.details[0]?.message || 'Invalid query parameters', 'VALIDATION_ERROR')
      }

      const {
        userId,
        action,
        resource,
        resourceId,
        status,
        startDate,
        endDate,
        ipAddress,
        page,
        limit,
        orderBy,
        orderDirection
      } = value

      // 查询操作日志
      const result = await AuditLogService.queryLogs({
        userId,
        action,
        resource,
        resourceId,
        status,
        startDate,
        endDate,
        ipAddress,
        page,
        limit,
        orderBy,
        orderDirection
      })

      res.json({
        success: true,
        data: result,
        message: 'Audit logs retrieved successfully'
      })
    } catch (error) {
      next(error)
    }
  }

  /**
   * 获取当前用户的操作日志
   * @param req - Express请求对象
   * @param res - Express响应对象
   * @param next - Express下一个中间件函数
   */
  static async getMyLogs(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        throw createError(401, 'Authentication required', 'AUTHENTICATION_REQUIRED')
      }

      // 验证查询参数（排除userId，因为使用当前用户ID）
      const { error, value } = queryLogsSchema.validate({
        ...req.query,
        userId: req.user.id
      })
      if (error) {
        throw createError(400, error.details[0]?.message || 'Invalid query parameters', 'VALIDATION_ERROR')
      }

      const {
        action,
        resource,
        resourceId,
        status,
        startDate,
        endDate,
        page,
        limit,
        orderBy,
        orderDirection
      } = value

      // 查询当前用户的操作日志
      const result = await AuditLogService.getUserLogs(req.user.id, {
        action,
        resource,
        resourceId,
        status,
        startDate,
        endDate,
        page,
        limit,
        orderBy,
        orderDirection
      })

      res.json({
        success: true,
        data: result,
        message: 'User audit logs retrieved successfully'
      })
    } catch (error) {
      next(error)
    }
  }

  /**
   * 根据ID获取操作日志详情
   * @param req - Express请求对象
   * @param res - Express响应对象
   * @param next - Express下一个中间件函数
   */
  static async getLogById(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const logId = getIdParam(req)

      const log = await AuditLogService.getLogById(logId)
      if (!log) {
        throw createError(404, 'Audit log not found', 'LOG_NOT_FOUND')
      }

      // 检查权限：只有管理员或日志所有者可以查看详情
      if (req.user && log.userId !== req.user.id) {
        // 这里应该检查用户是否有管理员权限
        // 为了简化，暂时允许所有认证用户查看
      }

      res.json({
        success: true,
        data: log,
        message: 'Audit log retrieved successfully'
      })
    } catch (error) {
      next(error)
    }
  }

  /**
   * 获取操作日志统计信息
   * @param req - Express请求对象
   * @param res - Express响应对象
   * @param next - Express下一个中间件函数
   */
  static async getStats(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      // 验证查询参数
      const { error, value } = statsQuerySchema.validate(req.query)
      if (error) {
        throw createError(400, error.details[0]?.message || 'Invalid query parameters', 'VALIDATION_ERROR')
      }

      const { userId, startDate, endDate, resource, action } = value

      // 获取详细统计信息
      const stats = await AuditLogService.getDetailedStats({
        userId,
        startDate,
        endDate,
        resource,
        action
      })

      res.json({
        success: true,
        data: stats,
        message: 'Audit log statistics retrieved successfully'
      })
    } catch (error) {
      next(error)
    }
  }

  /**
   * 获取当前用户的操作统计
   * @param req - Express请求对象
   * @param res - Express响应对象
   * @param next - Express下一个中间件函数
   */
  static async getMyStats(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        throw createError(401, 'Authentication required', 'AUTHENTICATION_REQUIRED')
      }

      // 验证查询参数
      const { error, value } = statsQuerySchema.validate({
        ...req.query,
        userId: req.user.id
      })
      if (error) {
        throw createError(400, error.details[0]?.message || 'Invalid query parameters', 'VALIDATION_ERROR')
      }

      const { startDate, endDate, resource, action, days } = value

      // 获取当前用户的统计信息
      const stats = await AuditLogService.getStats(req.user.id, days)

      res.json({
        success: true,
        data: stats,
        message: 'User audit log statistics retrieved successfully'
      })
    } catch (error) {
      next(error)
    }
  }

  /**
   * 清理过期的操作日志（管理员功能）
   * @param req - Express请求对象
   * @param res - Express响应对象
   * @param next - Express下一个中间件函数
   */
  static async cleanupLogs(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const daysSchema = Joi.object({
        days: Joi.number().integer().min(30).max(365).default(90)
      })

      const { error, value } = daysSchema.validate(req.body)
      if (error) {
        throw createError(400, error.details[0]?.message || 'Invalid parameters', 'VALIDATION_ERROR')
      }

      const { days } = value

      const deletedCount = await AuditLogService.cleanupOldLogs(days)

      res.json({
        success: true,
        data: {
          deletedCount,
          retentionDays: days
        },
        message: `Successfully cleaned up ${deletedCount} old audit logs`
      })
    } catch (error) {
      next(error)
    }
  }

  /**
   * 获取操作类型列表
   * @param req - Express请求对象
   * @param res - Express响应对象
   * @param next - Express下一个中间件函数
   */
  static async getActions(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // 预定义的操作类型
      const actions = [
        'CREATE',
        'UPDATE',
        'DELETE',
        'LOGIN',
        'LOGOUT',
        'VIEW',
        'ASSIGN_ROLE',
        'REMOVE_ROLE',
        'GRANT_PERMISSION',
        'REVOKE_PERMISSION',
        'UPLOAD',
        'DOWNLOAD',
        'APPROVE',
        'REJECT',
        'PUBLISH',
        'UNPUBLISH'
      ]

      res.json({
        success: true,
        data: actions,
        message: 'Action types retrieved successfully'
      })
    } catch (error) {
      next(error)
    }
  }

  /**
   * 获取资源类型列表
   * @param req - Express请求对象
   * @param res - Express响应对象
   * @param next - Express下一个中间件函数
   */
  static async getResources(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // 预定义的资源类型
      const resources = [
        'user',
        'article',
        'post',
        'comment',
        'category',
        'tag',
        'media',
        'role',
        'permission',
        'notification',
        'settings',
        'auth'
      ]

      res.json({
        success: true,
        data: resources,
        message: 'Resource types retrieved successfully'
      })
    } catch (error) {
      next(error)
    }
  }
}

// 导出控制器方法
export const {
  getLogs,
  getMyLogs,
  getLogById,
  getStats,
  getMyStats,
  cleanupLogs,
  getActions,
  getResources
} = AuditLogController

export default AuditLogController
