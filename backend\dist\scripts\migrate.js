#!/usr/bin/env ts-node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const migrationRunner_1 = require("../utils/migrationRunner");
const connection_1 = require("../database/connection");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
class MigrateScript {
    constructor() {
        this.migrationRunner = new migrationRunner_1.MigrationRunner();
    }
    async run() {
        console.log('🚀 Starting database migration...');
        console.log('='.repeat(50));
        try {
            const connectionInfo = connection_1.dbConnection.getConnectionInfo();
            console.log(`📊 Database: ${connectionInfo.database}`);
            console.log(`🏠 Host: ${connectionInfo.host}:${connectionInfo.port}`);
            console.log(`👤 User: ${connectionInfo.username}`);
            console.log('');
            console.log('🔍 Testing database connection...');
            const isConnected = await connection_1.dbConnection.testConnection();
            if (!isConnected) {
                console.error('❌ Database connection failed!');
                process.exit(1);
            }
            console.log('✅ Database connection successful!');
            console.log('');
            console.log('📦 Running migrations...');
            await this.migrationRunner.runMigrations();
            console.log('');
            console.log('='.repeat(50));
            console.log('🎉 Migration completed successfully!');
        }
        catch (error) {
            console.error('');
            console.error('='.repeat(50));
            console.error('❌ Migration failed!');
            console.error('Error details:', error);
            process.exit(1);
        }
        finally {
            await connection_1.dbConnection.closeConnection();
        }
    }
    showHelp() {
        console.log(`
📚 Database Migration Tool

Usage:
  npm run migrate              Run all pending migrations
  ts-node src/scripts/migrate.ts    Run migrations directly

Environment Variables:
  DB_HOST      Database host (default: localhost)
  DB_PORT      Database port (default: 3306)
  DB_NAME      Database name (default: person-blog)
  DB_USER      Database user (default: person-blog)
  DB_PASSWORD  Database password

Examples:
  npm run migrate
  NODE_ENV=production npm run migrate:prod
`);
    }
}
async function main() {
    const args = process.argv.slice(2);
    if (args.includes('--help') || args.includes('-h')) {
        const script = new MigrateScript();
        script.showHelp();
        return;
    }
    const script = new MigrateScript();
    await script.run();
}
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});
if (require.main === module) {
    main().catch((error) => {
        console.error('Script execution failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=migrate.js.map