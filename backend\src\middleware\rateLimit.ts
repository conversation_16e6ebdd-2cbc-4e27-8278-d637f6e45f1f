import { Request, Response, NextFunction } from 'express'
import { createError } from './errorHandler'

/**
 * 限流配置接口
 */
interface RateLimitConfig {
  windowMs: number // 时间窗口（毫秒）
  maxRequests: number // 最大请求次数
  message?: string // 自定义错误消息
  skipSuccessfulRequests?: boolean // 是否跳过成功请求
  skipFailedRequests?: boolean // 是否跳过失败请求
  keyGenerator?: (req: Request) => string // 自定义key生成器
}

/**
 * 请求记录接口
 */
interface RequestRecord {
  count: number
  resetTime: number
  firstRequest: number
}

/**
 * 限流存储类
 */
class RateLimitStore {
  private store = new Map<string, RequestRecord>()
  private cleanupInterval: NodeJS.Timeout

  constructor() {
    // 每分钟清理一次过期记录
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, 60 * 1000)
  }

  /**
   * 获取请求记录
   */
  get(key: string): RequestRecord | undefined {
    return this.store.get(key)
  }

  /**
   * 设置请求记录
   */
  set(key: string, record: RequestRecord): void {
    this.store.set(key, record)
  }

  /**
   * 增加请求计数
   */
  increment(key: string, windowMs: number): RequestRecord {
    const now = Date.now()
    const existing = this.store.get(key)

    if (!existing || now > existing.resetTime) {
      // 创建新记录或重置过期记录
      const record: RequestRecord = {
        count: 1,
        resetTime: now + windowMs,
        firstRequest: now
      }
      this.store.set(key, record)
      return record
    }

    // 增加现有记录的计数
    existing.count++
    return existing
  }

  /**
   * 清理过期记录
   */
  private cleanup(): void {
    const now = Date.now()
    for (const [key, record] of this.store.entries()) {
      if (now > record.resetTime) {
        this.store.delete(key)
      }
    }
  }

  /**
   * 销毁存储
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }
    this.store.clear()
  }
}

// 全局存储实例
const globalStore = new RateLimitStore()

/**
 * 创建限流中间件
 */
export function createRateLimit(config: RateLimitConfig) {
  const {
    windowMs,
    maxRequests,
    message = '请求过于频繁，请稍后再试',
    skipSuccessfulRequests = false,
    skipFailedRequests = false,
    keyGenerator = (req: Request) => req.ip || req.connection.remoteAddress || 'unknown'
  } = config

  return (req: Request, res: Response, next: NextFunction): void => {
    const key = keyGenerator(req)
    const record = globalStore.increment(key, windowMs)

    // 设置响应头
    res.set({
      'X-RateLimit-Limit': maxRequests.toString(),
      'X-RateLimit-Remaining': Math.max(0, maxRequests - record.count).toString(),
      'X-RateLimit-Reset': new Date(record.resetTime).toISOString()
    })

    if (record.count > maxRequests) {
      throw createError(429, message, 'RATE_LIMIT_EXCEEDED')
    }

    // 如果配置了跳过条件，在响应完成后检查
    if (skipSuccessfulRequests || skipFailedRequests) {
      const originalSend = res.send
      res.send = function(body) {
        const statusCode = res.statusCode
        const shouldSkip = 
          (skipSuccessfulRequests && statusCode < 400) ||
          (skipFailedRequests && statusCode >= 400)

        if (shouldSkip && record.count > 0) {
          record.count--
        }

        return originalSend.call(this, body)
      }
    }

    next()
  }
}

/**
 * 通用API限流 - 每分钟100次请求
 */
export const generalRateLimit = createRateLimit({
  windowMs: 60 * 1000, // 1分钟
  maxRequests: 100,
  message: 'API请求过于频繁，请稍后再试'
})

/**
 * 认证相关限流 - 每15分钟5次请求
 */
export const authRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  maxRequests: 5,
  message: '登录尝试过于频繁，请15分钟后再试',
  skipSuccessfulRequests: true // 成功的请求不计入限制
})

/**
 * 注册限流 - 每小时3次请求
 */
export const registrationRateLimit = createRateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  maxRequests: 3,
  message: '注册尝试过于频繁，请1小时后再试'
})

/**
 * 密码重置限流 - 每小时3次请求
 */
export const passwordResetRateLimit = createRateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  maxRequests: 3,
  message: '密码重置请求过于频繁，请1小时后再试'
})

/**
 * 邮件发送限流 - 每小时5次请求
 */
export const emailRateLimit = createRateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  maxRequests: 5,
  message: '邮件发送过于频繁，请1小时后再试'
})

/**
 * 文件上传限流 - 每分钟10次请求
 */
export const uploadRateLimit = createRateLimit({
  windowMs: 60 * 1000, // 1分钟
  maxRequests: 10,
  message: '文件上传过于频繁，请稍后再试'
})

/**
 * 评论限流 - 每分钟5次请求
 */
export const commentRateLimit = createRateLimit({
  windowMs: 60 * 1000, // 1分钟
  maxRequests: 5,
  message: '评论发布过于频繁，请稍后再试'
})

/**
 * 搜索限流 - 每分钟30次请求
 */
export const searchRateLimit = createRateLimit({
  windowMs: 60 * 1000, // 1分钟
  maxRequests: 30,
  message: '搜索请求过于频繁，请稍后再试'
})

/**
 * 基于用户ID的限流
 */
export const createUserBasedRateLimit = (config: Omit<RateLimitConfig, 'keyGenerator'>) => {
  return createRateLimit({
    ...config,
    keyGenerator: (req: Request) => {
      // 优先使用用户ID，如果未认证则使用IP
      const user = (req as any).user
      return user ? `user:${user.id}` : `ip:${req.ip || 'unknown'}`
    }
  })
}

/**
 * 严格的用户操作限流 - 每分钟20次请求
 */
export const strictUserRateLimit = createUserBasedRateLimit({
  windowMs: 60 * 1000, // 1分钟
  maxRequests: 20,
  message: '操作过于频繁，请稍后再试'
})

/**
 * 管理员操作限流 - 每分钟50次请求
 */
export const adminRateLimit = createRateLimit({
  windowMs: 60 * 1000, // 1分钟
  maxRequests: 50,
  message: '管理操作过于频繁，请稍后再试',
  keyGenerator: (req: Request) => {
    const user = (req as any).user
    return user ? `admin:${user.id}` : `ip:${req.ip || 'unknown'}`
  }
})

/**
 * 动态限流 - 根据用户角色调整限制
 */
export const dynamicRateLimit = (req: Request, res: Response, next: NextFunction): void => {
  const user = (req as any).user
  
  if (!user) {
    // 未认证用户 - 严格限制
    return generalRateLimit(req, res, next)
  }

  // 根据用户角色选择不同的限流策略
  // 这里可以根据实际的角色系统进行调整
  const isAdmin = user.roles?.some((role: any) => role.name === 'admin')
  const isPremium = user.roles?.some((role: any) => role.name === 'premium')

  if (isAdmin) {
    return adminRateLimit(req, res, next)
  } else if (isPremium) {
    // 高级用户 - 宽松限制
    return createUserBasedRateLimit({
      windowMs: 60 * 1000,
      maxRequests: 100,
      message: '操作过于频繁，请稍后再试'
    })(req, res, next)
  } else {
    // 普通用户 - 标准限制
    return strictUserRateLimit(req, res, next)
  }
}

/**
 * 清理限流存储（用于测试或重启时）
 */
export const clearRateLimitStore = (): void => {
  globalStore.destroy()
}
