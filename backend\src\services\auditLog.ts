import { AuditLog, AuditLogQueryOptions, AuditLogStats, AuditLogCreationAttributes } from '../models/AuditLog'
import { User } from '../models/User'
import { Op, QueryTypes } from 'sequelize'
import { sequelize } from '../config/database'

/**
 * 操作日志查询结果接口
 */
export interface AuditLogQueryResult {
  logs: AuditLog[]
  total: number
  page: number
  limit: number
  totalPages: number
}

/**
 * 操作日志统计查询选项
 */
export interface AuditLogStatsOptions {
  userId?: number
  startDate?: Date
  endDate?: Date
  resource?: string
  action?: string
}

/**
 * 详细统计信息接口
 */
export interface DetailedAuditLogStats extends AuditLogStats {
  userStats: Array<{
    userId: number
    username: string
    count: number
  }>
  hourlyStats: Array<{
    hour: number
    count: number
  }>
  statusDistribution: {
    success: number
    failed: number
    pending: number
  }
  averageDuration: number
  topActions: Array<{
    action: string
    count: number
  }>
  topResources: Array<{
    resource: string
    count: number
  }>
}

/**
 * 操作日志服务类
 * 提供操作日志的业务逻辑处理
 */
export class AuditLogService {
  /**
   * 记录操作日志
   * @param logData - 日志数据
   * @returns Promise<AuditLog> - 创建的日志记录
   */
  static async createLog(logData: AuditLogCreationAttributes): Promise<AuditLog> {
    try {
      return await AuditLog.logAction(logData)
    } catch (error) {
      console.error('Failed to create audit log:', error)
      throw new Error('Failed to create audit log')
    }
  }

  /**
   * 查询操作日志
   * @param options - 查询选项
   * @returns Promise<AuditLogQueryResult> - 查询结果
   */
  static async queryLogs(options: AuditLogQueryOptions): Promise<AuditLogQueryResult> {
    try {
      const { logs, total } = await AuditLog.queryLogs(options)
      const page = options.page || 1
      const limit = options.limit || 20
      const totalPages = Math.ceil(total / limit)

      return {
        logs,
        total,
        page,
        limit,
        totalPages
      }
    } catch (error) {
      console.error('Failed to query audit logs:', error)
      throw new Error('Failed to query audit logs')
    }
  }

  /**
   * 获取用户的操作日志
   * @param userId - 用户ID
   * @param options - 查询选项
   * @returns Promise<AuditLogQueryResult> - 查询结果
   */
  static async getUserLogs(userId: number, options: Omit<AuditLogQueryOptions, 'userId'>): Promise<AuditLogQueryResult> {
    return this.queryLogs({ ...options, userId })
  }

  /**
   * 获取操作日志统计信息
   * @param options - 统计选项
   * @returns Promise<DetailedAuditLogStats> - 统计信息
   */
  static async getDetailedStats(options: AuditLogStatsOptions = {}): Promise<DetailedAuditLogStats> {
    try {
      const { userId, startDate, endDate, resource, action } = options

      // 构建基础查询条件
      const baseWhere: any = {}
      if (userId !== undefined) baseWhere.userId = userId
      if (resource) baseWhere.resource = resource
      if (action) baseWhere.action = action
      if (startDate || endDate) {
        baseWhere.createdAt = {}
        if (startDate) baseWhere.createdAt[Op.gte] = startDate
        if (endDate) baseWhere.createdAt[Op.lte] = endDate
      }

      // 获取基础统计
      const [
        totalLogs,
        successLogs,
        failedLogs,
        pendingLogs
      ] = await Promise.all([
        AuditLog.count({ where: baseWhere }),
        AuditLog.count({ where: { ...baseWhere, status: 'success' } }),
        AuditLog.count({ where: { ...baseWhere, status: 'failed' } }),
        AuditLog.count({ where: { ...baseWhere, status: 'pending' } })
      ])

      // 获取操作类型统计
      const actionStatsQuery = await sequelize.query(`
        SELECT action, COUNT(*) as count
        FROM audit_logs
        WHERE ${this.buildWhereClause(baseWhere)}
        GROUP BY action
        ORDER BY count DESC
        LIMIT 10
      `, {
        type: QueryTypes.SELECT,
        replacements: this.buildReplacements(baseWhere)
      }) as Array<{ action: string; count: string }>

      const actionStats: Record<string, number> = {}
      const topActions = actionStatsQuery.map(item => ({
        action: item.action,
        count: parseInt(item.count)
      }))
      topActions.forEach(item => {
        actionStats[item.action] = item.count
      })

      // 获取资源类型统计
      const resourceStatsQuery = await sequelize.query(`
        SELECT resource, COUNT(*) as count
        FROM audit_logs
        WHERE ${this.buildWhereClause(baseWhere)}
        GROUP BY resource
        ORDER BY count DESC
        LIMIT 10
      `, {
        type: QueryTypes.SELECT,
        replacements: this.buildReplacements(baseWhere)
      }) as Array<{ resource: string; count: string }>

      const resourceStats: Record<string, number> = {}
      const topResources = resourceStatsQuery.map(item => ({
        resource: item.resource,
        count: parseInt(item.count)
      }))
      topResources.forEach(item => {
        resourceStats[item.resource] = item.count
      })

      // 获取用户统计（如果不是查询特定用户）
      let userStats: Array<{ userId: number; username: string; count: number }> = []
      if (userId === undefined) {
        const userStatsQuery = await sequelize.query(`
          SELECT al.user_id, u.username, COUNT(*) as count
          FROM audit_logs al
          LEFT JOIN users u ON al.user_id = u.id
          WHERE ${this.buildWhereClause(baseWhere)}
          GROUP BY al.user_id, u.username
          ORDER BY count DESC
          LIMIT 10
        `, {
          type: QueryTypes.SELECT,
          replacements: this.buildReplacements(baseWhere)
        }) as Array<{ user_id: number; username: string; count: string }>

        userStats = userStatsQuery.map(item => ({
          userId: item.user_id,
          username: item.username || 'Unknown',
          count: parseInt(item.count)
        }))
      }

      // 获取每小时统计
      const hourlyStatsQuery = await sequelize.query(`
        SELECT EXTRACT(HOUR FROM created_at) as hour, COUNT(*) as count
        FROM audit_logs
        WHERE ${this.buildWhereClause(baseWhere)}
        GROUP BY EXTRACT(HOUR FROM created_at)
        ORDER BY hour
      `, {
        type: QueryTypes.SELECT,
        replacements: this.buildReplacements(baseWhere)
      }) as Array<{ hour: string; count: string }>

      const hourlyStats = Array.from({ length: 24 }, (_, i) => ({ hour: i, count: 0 }))
      hourlyStatsQuery.forEach(item => {
        const hour = parseInt(item.hour)
        const count = parseInt(item.count)
        if (hour >= 0 && hour < 24 && hourlyStats[hour]) {
          hourlyStats[hour]!.count = count
        }
      })

      // 获取平均耗时
      const avgDurationQuery = await sequelize.query(`
        SELECT AVG(duration) as avg_duration
        FROM audit_logs
        WHERE ${this.buildWhereClause(baseWhere)} AND duration IS NOT NULL
      `, {
        type: QueryTypes.SELECT,
        replacements: this.buildReplacements(baseWhere)
      }) as unknown as Array<{ avg_duration: string }>

      const averageDuration = avgDurationQuery[0]?.avg_duration ?
        Math.round(parseFloat(avgDurationQuery[0].avg_duration)) : 0

      // 获取每日统计
      const dailyStatsQuery = await sequelize.query(`
        SELECT DATE(created_at) as date, COUNT(*) as count
        FROM audit_logs
        WHERE ${this.buildWhereClause(baseWhere)}
        GROUP BY DATE(created_at)
        ORDER BY date DESC
        LIMIT 30
      `, {
        type: QueryTypes.SELECT,
        replacements: this.buildReplacements(baseWhere)
      }) as Array<{ date: string; count: string }>

      const dailyStats = dailyStatsQuery.map(item => ({
        date: item.date,
        count: parseInt(item.count)
      }))

      return {
        totalLogs,
        successLogs,
        failedLogs,
        actionStats,
        resourceStats,
        dailyStats,
        userStats,
        hourlyStats,
        statusDistribution: {
          success: successLogs,
          failed: failedLogs,
          pending: pendingLogs
        },
        averageDuration,
        topActions,
        topResources
      }
    } catch (error) {
      console.error('Failed to get detailed audit log stats:', error)
      throw new Error('Failed to get audit log statistics')
    }
  }

  /**
   * 获取简单统计信息
   * @param userId - 用户ID（可选）
   * @param days - 统计天数
   * @returns Promise<AuditLogStats> - 统计信息
   */
  static async getStats(userId?: number, days: number = 30): Promise<AuditLogStats> {
    try {
      return await AuditLog.getStats(userId, days)
    } catch (error) {
      console.error('Failed to get audit log stats:', error)
      throw new Error('Failed to get audit log statistics')
    }
  }

  /**
   * 清理过期的操作日志
   * @param days - 保留天数
   * @returns Promise<number> - 删除的记录数
   */
  static async cleanupOldLogs(days: number = 90): Promise<number> {
    try {
      return await AuditLog.cleanupOldLogs(days)
    } catch (error) {
      console.error('Failed to cleanup old audit logs:', error)
      throw new Error('Failed to cleanup old audit logs')
    }
  }

  /**
   * 根据ID获取操作日志详情
   * @param id - 日志ID
   * @returns Promise<AuditLog | null> - 日志记录
   */
  static async getLogById(id: number): Promise<AuditLog | null> {
    try {
      return await AuditLog.findByPk(id, {
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'username', 'email']
          }
        ]
      })
    } catch (error) {
      console.error('Failed to get audit log by id:', error)
      throw new Error('Failed to get audit log')
    }
  }

  /**
   * 构建SQL WHERE子句
   * @param where - 查询条件对象
   * @returns WHERE子句字符串
   */
  private static buildWhereClause(where: any): string {
    const conditions: string[] = ['1=1'] // 基础条件

    if (where.userId !== undefined) {
      conditions.push('user_id = :userId')
    }
    if (where.resource) {
      conditions.push('resource = :resource')
    }
    if (where.action) {
      conditions.push('action = :action')
    }
    if (where.createdAt) {
      if (where.createdAt[Op.gte]) {
        conditions.push('created_at >= :startDate')
      }
      if (where.createdAt[Op.lte]) {
        conditions.push('created_at <= :endDate')
      }
    }

    return conditions.join(' AND ')
  }

  /**
   * 构建SQL查询参数
   * @param where - 查询条件对象
   * @returns 参数对象
   */
  private static buildReplacements(where: any): any {
    const replacements: any = {}

    if (where.userId !== undefined) {
      replacements.userId = where.userId
    }
    if (where.resource) {
      replacements.resource = where.resource
    }
    if (where.action) {
      replacements.action = where.action
    }
    if (where.createdAt) {
      if (where.createdAt[Op.gte]) {
        replacements.startDate = where.createdAt[Op.gte]
      }
      if (where.createdAt[Op.lte]) {
        replacements.endDate = where.createdAt[Op.lte]
      }
    }

    return replacements
  }
}

export default AuditLogService
