{"version": 3, "file": "009-create-notifications.js", "sourceRoot": "", "sources": ["../../../src/database/migrations/009-create-notifications.ts"], "names": [], "mappings": ";;;AAOA,yCAAqD;AAE9C,MAAM,EAAE,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IACxE,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAA;IAGhC,MAAM,cAAc,CAAC,WAAW,CAAC,eAAe,EAAE;QAChD,EAAE,EAAE;YACF,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK;SACjB;QACD,IAAI,EAAE;YACJ,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,CAAC;YACrE,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,kBAAkB;SAC5B;QACD,KAAK,EAAE;YACL,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,QAAQ;SAClB;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC;YAC7C,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,QAAQ;YACtB,OAAO,EAAE,OAAO;SACjB;QACD,YAAY,EAAE;YACZ,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,SAAS;YAClB,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB;QACD,SAAS,EAAE;YACT,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,kBAAkB;YAC3B,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,UAAU;SACrB;QACD,YAAY,EAAE;YACZ,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC;YACpE,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,QAAQ;SAClB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,QAAQ;SAClB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,SAAS;SACnB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,KAAK;YACnB,OAAO,EAAE,MAAM;SAChB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,MAAM;SAChB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;KACF,CAAC,CAAA;IAGF,MAAM,cAAc,CAAC,WAAW,CAAC,0BAA0B,EAAE;QAC3D,EAAE,EAAE;YACF,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK;SACjB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;YACf,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB;QACD,iBAAiB,EAAE;YACjB,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,CAAC;YACrE,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC;YAC/C,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,QAAQ;YACtB,OAAO,EAAE,eAAe;SACzB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,WAAW;SACrB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;KACF,CAAC,CAAA;IAGF,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;IAG5B,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,cAAc,EAAE,YAAY,CAAC,EAAE;YAC7E,IAAI,EAAE,uBAAuB;SAC9B,CAAC,CAAA;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAA;IACxE,CAAC;IAGD,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,cAAc,EAAE,SAAS,CAAC,EAAE;YAC1E,IAAI,EAAE,sBAAsB;SAC7B,CAAC,CAAA;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAA;IACvE,CAAC;IAGD,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE;YACnE,IAAI,EAAE,mBAAmB;SAC1B,CAAC,CAAA;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAA;IACpE,CAAC;IAGD,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,cAAc,EAAE,YAAY,CAAC,EAAE;YAC7E,IAAI,EAAE,aAAa;SACpB,CAAC,CAAA;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAA;IAC9D,CAAC;IAGD,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,YAAY,CAAC,EAAE;YAC7D,IAAI,EAAE,gBAAgB;SACvB,CAAC,CAAA;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAA;IACjE,CAAC;IAGD,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAA;IAGhC,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,0BAA0B,EAAE,CAAC,SAAS,EAAE,mBAAmB,EAAE,SAAS,CAAC,EAAE;YACrG,IAAI,EAAE,0BAA0B;YAChC,MAAM,EAAE,IAAI;SACb,CAAC,CAAA;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAA;IAC3E,CAAC;IAGD,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,0BAA0B,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC,EAAE;YACnF,IAAI,EAAE,kBAAkB;SACzB,CAAC,CAAA;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAA;IACnE,CAAC;IAGD,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,0BAA0B,EAAE,CAAC,mBAAmB,EAAE,SAAS,EAAE,YAAY,CAAC,EAAE;YACxG,IAAI,EAAE,0BAA0B;SACjC,CAAC,CAAA;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAA;IAC3E,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;IAC5B,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;IACvB,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAA;IACvC,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAA;IACtD,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;IACxB,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;IAC5B,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAA;AAChC,CAAC,CAAA;AAtOY,QAAA,EAAE,MAsOd;AAEM,MAAM,IAAI,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IAC1E,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAA;IAGjC,MAAM,cAAc,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAA;IAC1D,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAA;IAG/C,MAAM,cAAc,CAAC,SAAS,CAAC,eAAe,CAAC,CAAA;IAC/C,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAA;IAEpC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAA;AAC/B,CAAC,CAAA;AAZY,QAAA,IAAI,QAYhB"}