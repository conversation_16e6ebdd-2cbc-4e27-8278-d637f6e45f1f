import { DataTypes, Model, Optional } from 'sequelize'
import { sequelize } from '../config/database'

/**
 * 用户关注模型的属性接口定义
 */
export interface UserFollowAttributes {
  id: number
  followerId: number  // 关注者ID
  followingId: number // 被关注者ID
  createdAt: Date
}

/**
 * 用户关注创建时的属性接口定义
 */
export interface UserFollowCreationAttributes extends Optional<UserFollowAttributes, 'id' | 'createdAt'> { }

/**
 * 用户关注模型类
 * 用于管理用户之间的关注关系
 */
export class UserFollow extends Model<UserFollowAttributes, UserFollowCreationAttributes> implements UserFollowAttributes {
  public id!: number
  public followerId!: number
  public followingId!: number
  public createdAt!: Date

  /**
   * 切换关注状态
   */
  public static async toggleFollow(followerId: number, followingId: number): Promise<{ following: boolean; followerCount: number; followingCount: number }> {
    // 不能关注自己
    if (followerId === followingId) {
      throw new Error('不能关注自己')
    }

    const existingFollow = await this.findOne({
      where: { followerId, followingId }
    })

    if (existingFollow) {
      // 取消关注
      await existingFollow.destroy()
      const [followerCount, followingCount] = await Promise.all([
        this.count({ where: { followingId } }), // 被关注者的粉丝数
        this.count({ where: { followerId } })   // 关注者的关注数
      ])
      return { following: false, followerCount, followingCount }
    } else {
      // 添加关注
      await this.create({ followerId, followingId })
      const [followerCount, followingCount] = await Promise.all([
        this.count({ where: { followingId } }),
        this.count({ where: { followerId } })
      ])
      return { following: true, followerCount, followingCount }
    }
  }

  /**
   * 检查用户A是否关注了用户B
   */
  public static async isFollowing(followerId: number, followingId: number): Promise<boolean> {
    const follow = await this.findOne({
      where: { followerId, followingId }
    })
    return !!follow
  }

  /**
   * 获取用户的粉丝数
   */
  public static async getFollowerCount(userId: number): Promise<number> {
    return this.count({
      where: { followingId: userId }
    })
  }

  /**
   * 获取用户的关注数
   */
  public static async getFollowingCount(userId: number): Promise<number> {
    return this.count({
      where: { followerId: userId }
    })
  }

  /**
   * 获取用户的粉丝列表
   */
  public static async getFollowers(userId: number, limit: number = 20, offset: number = 0) {
    return this.findAndCountAll({
      where: { followingId: userId },
      order: [['createdAt', 'DESC']],
      limit,
      offset,
      include: [
        {
          model: sequelize.models.User,
          as: 'follower',
          attributes: ['id', 'username']
        }
      ]
    })
  }

  /**
   * 获取用户的关注列表
   */
  public static async getFollowing(userId: number, limit: number = 20, offset: number = 0) {
    return this.findAndCountAll({
      where: { followerId: userId },
      order: [['createdAt', 'DESC']],
      limit,
      offset,
      include: [
        {
          model: sequelize.models.User,
          as: 'following',
          attributes: ['id', 'username']
        }
      ]
    })
  }

  /**
   * 获取互相关注的用户（好友）
   */
  public static async getMutualFollows(userId: number, limit: number = 20, offset: number = 0) {
    // 查找用户关注的人中也关注了用户的人
    const following = await this.findAll({
      where: { followerId: userId },
      attributes: ['followingId']
    })

    const followingIds = following.map(f => f.followingId)

    if (followingIds.length === 0) {
      return { rows: [], count: 0 }
    }

    return this.findAndCountAll({
      where: {
        followerId: { [sequelize.Op.in]: followingIds },
        followingId: userId
      },
      order: [['createdAt', 'DESC']],
      limit,
      offset,
      include: [
        {
          model: sequelize.models.User,
          as: 'follower',
          attributes: ['id', 'username']
        }
      ]
    })
  }

  /**
   * 获取用户可能感兴趣的人（推荐关注）
   */
  public static async getRecommendedUsers(userId: number, limit: number = 10) {
    // 获取用户关注的人关注的其他人（二度关系）
    const userFollowing = await this.findAll({
      where: { followerId: userId },
      attributes: ['followingId']
    })

    const followingIds = userFollowing.map(f => f.followingId)

    if (followingIds.length === 0) {
      return []
    }

    // 获取这些人关注的其他人
    const recommendations = await this.findAll({
      where: {
        followerId: { [sequelize.Op.in]: followingIds },
        followingId: { [sequelize.Op.ne]: userId } // 排除自己
      },
      attributes: [
        'followingId',
        [sequelize.fn('COUNT', sequelize.col('id')), 'commonFollowers']
      ],
      group: ['followingId'],
      order: [[sequelize.literal('commonFollowers'), 'DESC']],
      limit,
      include: [
        {
          model: sequelize.models.User,
          as: 'following',
          attributes: ['id', 'username']
        }
      ]
    })

    // 过滤掉已经关注的用户
    const alreadyFollowing = await this.findAll({
      where: { followerId: userId },
      attributes: ['followingId']
    })

    const alreadyFollowingIds = alreadyFollowing.map(f => f.followingId)

    return recommendations.filter(rec =>
      !alreadyFollowingIds.includes(rec.followingId)
    )
  }
}

/**
 * 初始化用户关注模型
 */
UserFollow.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    followerId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'follower_id',
      references: {
        model: 'users',
        key: 'id'
      }
    },
    followingId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'following_id',
      references: {
        model: 'users',
        key: 'id'
      }
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    }
  },
  {
    sequelize,
    modelName: 'UserFollow',
    tableName: 'user_follows',
    timestamps: true,
    updatedAt: false,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['follower_id', 'following_id']
      },
      {
        fields: ['follower_id']
      },
      {
        fields: ['following_id']
      },
      {
        fields: ['created_at']
      }
    ]
  }
)
