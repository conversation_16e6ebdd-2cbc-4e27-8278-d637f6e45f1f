import { QueryInterface, DataTypes } from 'sequelize'

/**
 * 创建通知偏好设置表的数据库迁移
 * 该迁移创建 notification_preferences 表，用于存储用户的通知偏好设置
 * 迁移序号：016
 */
export const up = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.createTable('notification_preferences', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    type: {
      type: DataTypes.ENUM('interaction', 'content', 'system', 'marketing'),
      allowNull: false,
      comment: '通知类型'
    },
    channel: {
      type: DataTypes.ENUM('in_app', 'email', 'sms', 'push'),
      allowNull: false,
      comment: '通知渠道'
    },
    enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '是否启用'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci'
  })

  // 创建索引
  await queryInterface.addIndex('notification_preferences', ['user_id'], {
    name: 'notification_preferences_user_id_index'
  })

  await queryInterface.addIndex('notification_preferences', ['type'], {
    name: 'notification_preferences_type_index'
  })

  await queryInterface.addIndex('notification_preferences', ['channel'], {
    name: 'notification_preferences_channel_index'
  })

  await queryInterface.addIndex('notification_preferences', ['user_id', 'type', 'channel'], {
    unique: true,
    name: 'notification_preferences_user_type_channel_unique'
  })
}

/**
 * 回滚迁移，删除 notification_preferences 表
 */
export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.dropTable('notification_preferences')
}

/**
 * 迁移信息
 */
export const migrationInfo = {
  name: '20241229_create_notification_preferences_table',
  description: '创建通知偏好设置表',
  version: '1.0.0',
  author: 'System',
  createdAt: new Date('2024-12-29')
}
