import { QueryInterface, DataTypes } from 'sequelize'

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // 添加媒体设置字段
  await queryInterface.addColumn('settings', 'max_file_size', {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 10,
    validate: {
      min: 1,
      max: 100
    }
  })

  await queryInterface.addColumn('settings', 'allowed_file_types', {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
  })

  await queryInterface.addColumn('settings', 'auto_generate_thumbnail', {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  })

  // 添加内容设置字段
  await queryInterface.addColumn('settings', 'default_article_visibility', {
    type: DataTypes.ENUM('public', 'private'),
    allowNull: false,
    defaultValue: 'public'
  })

  await queryInterface.addColumn('settings', 'enable_comments', {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  })

  await queryInterface.addColumn('settings', 'require_comment_approval', {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  })

  await queryInterface.addColumn('settings', 'enable_article_likes', {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  })
}

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.removeColumn('settings', 'max_file_size')
  await queryInterface.removeColumn('settings', 'allowed_file_types')
  await queryInterface.removeColumn('settings', 'auto_generate_thumbnail')
  await queryInterface.removeColumn('settings', 'default_article_visibility')
  await queryInterface.removeColumn('settings', 'enable_comments')
  await queryInterface.removeColumn('settings', 'require_comment_approval')
  await queryInterface.removeColumn('settings', 'enable_article_likes')
}
