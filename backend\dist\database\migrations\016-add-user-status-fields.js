"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.addColumn('users', 'is_active', {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: '用户是否激活'
    });
    await queryInterface.addColumn('users', 'email_verified', {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '邮箱是否已验证'
    });
    await queryInterface.addColumn('users', 'email_verified_at', {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        comment: '邮箱验证时间'
    });
    await queryInterface.addColumn('users', 'last_login_at', {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        comment: '最后登录时间'
    });
    await queryInterface.addColumn('users', 'password_reset_token', {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: true,
        comment: '密码重置token'
    });
    await queryInterface.addColumn('users', 'password_reset_expires', {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        comment: '密码重置token过期时间'
    });
    await queryInterface.addColumn('users', 'email_verification_token', {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: true,
        comment: '邮箱验证token'
    });
    await queryInterface.addColumn('users', 'email_verification_expires', {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        comment: '邮箱验证token过期时间'
    });
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.removeColumn('users', 'is_active');
    await queryInterface.removeColumn('users', 'email_verified');
    await queryInterface.removeColumn('users', 'email_verified_at');
    await queryInterface.removeColumn('users', 'last_login_at');
    await queryInterface.removeColumn('users', 'password_reset_token');
    await queryInterface.removeColumn('users', 'password_reset_expires');
    await queryInterface.removeColumn('users', 'email_verification_token');
    await queryInterface.removeColumn('users', 'email_verification_expires');
};
exports.down = down;
//# sourceMappingURL=016-add-user-status-fields.js.map