"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    console.log('🎬 开始创建媒体文件种子数据...');
    const [users] = await queryInterface.sequelize.query('SELECT id FROM users ORDER BY id LIMIT 4', { type: sequelize_1.QueryTypes.SELECT });
    if (users.length === 0) {
        console.log('⚠️ 没有找到用户数据，跳过媒体文件种子数据创建');
        return;
    }
    const adminUserId = users[0]?.id || 1;
    const user1Id = users[1]?.id || adminUserId;
    const user2Id = users[2]?.id || adminUserId;
    const user3Id = users[3]?.id || adminUserId;
    const mediaData = [
        {
            filename: 'hero-banner-2024.jpg',
            original_name: '首页横幅图片.jpg',
            mime_type: 'image/jpeg',
            size: 2048576,
            url: '/uploads/images/hero-banner-2024.jpg',
            thumbnail_url: '/uploads/thumbnails/hero-banner-2024-thumb.jpg',
            width: 1920,
            height: 1080,
            uploader_id: adminUserId,
            category: 'image',
            tags: JSON.stringify(['横幅', '首页', '设计', '2024']),
            description: '网站首页的主要横幅图片，展示了现代化的设计风格',
            is_public: true,
            created_at: new Date('2024-01-15 10:00:00'),
            updated_at: new Date('2024-01-15 10:00:00')
        },
        {
            filename: 'blog-cover-tech.png',
            original_name: '技术博客封面.png',
            mime_type: 'image/png',
            size: 1536000,
            url: '/uploads/images/blog-cover-tech.png',
            thumbnail_url: '/uploads/thumbnails/blog-cover-tech-thumb.png',
            width: 1200,
            height: 630,
            uploader_id: user1Id,
            category: 'image',
            tags: JSON.stringify(['博客', '技术', '封面', 'PNG']),
            description: '技术类文章的通用封面图片',
            is_public: true,
            created_at: new Date('2024-02-01 14:30:00'),
            updated_at: new Date('2024-02-01 14:30:00')
        },
        {
            filename: 'avatar-placeholder.svg',
            original_name: '默认头像.svg',
            mime_type: 'image/svg+xml',
            size: 8192,
            url: '/uploads/images/avatar-placeholder.svg',
            width: 200,
            height: 200,
            uploader_id: adminUserId,
            category: 'image',
            tags: JSON.stringify(['头像', '默认', 'SVG', '图标']),
            description: '用户默认头像图标',
            is_public: true,
            created_at: new Date('2024-01-10 09:00:00'),
            updated_at: new Date('2024-01-10 09:00:00')
        },
        {
            filename: 'gallery-photo-1.jpg',
            original_name: '风景照片1.jpg',
            mime_type: 'image/jpeg',
            size: 3145728,
            url: '/uploads/images/gallery-photo-1.jpg',
            thumbnail_url: '/uploads/thumbnails/gallery-photo-1-thumb.jpg',
            width: 2560,
            height: 1440,
            uploader_id: user2Id,
            category: 'image',
            tags: JSON.stringify(['风景', '摄影', '自然', '高清']),
            description: '美丽的自然风景摄影作品',
            is_public: true,
            created_at: new Date('2024-02-10 16:45:00'),
            updated_at: new Date('2024-02-10 16:45:00')
        },
        {
            filename: 'intro-video-2024.mp4',
            original_name: '产品介绍视频.mp4',
            mime_type: 'video/mp4',
            size: 52428800,
            url: '/uploads/videos/intro-video-2024.mp4',
            thumbnail_url: '/uploads/thumbnails/intro-video-2024-thumb.jpg',
            width: 1920,
            height: 1080,
            uploader_id: adminUserId,
            category: 'video',
            tags: JSON.stringify(['介绍', '产品', '视频', 'MP4', '1080p']),
            description: '公司产品的详细介绍视频，时长约5分钟',
            is_public: true,
            created_at: new Date('2024-01-20 11:15:00'),
            updated_at: new Date('2024-01-20 11:15:00')
        },
        {
            filename: 'tutorial-basics.webm',
            original_name: '基础教程.webm',
            mime_type: 'video/webm',
            size: 31457280,
            url: '/uploads/videos/tutorial-basics.webm',
            thumbnail_url: '/uploads/thumbnails/tutorial-basics-thumb.jpg',
            width: 1280,
            height: 720,
            uploader_id: user1Id,
            category: 'video',
            tags: JSON.stringify(['教程', '基础', '学习', 'WebM', '720p']),
            description: '面向初学者的基础操作教程视频',
            is_public: true,
            created_at: new Date('2024-02-05 13:20:00'),
            updated_at: new Date('2024-02-05 13:20:00')
        },
        {
            filename: 'background-music.mp3',
            original_name: '背景音乐.mp3',
            mime_type: 'audio/mpeg',
            size: 4194304,
            url: '/uploads/audio/background-music.mp3',
            uploader_id: user2Id,
            category: 'audio',
            tags: JSON.stringify(['音乐', '背景', 'MP3', '轻音乐']),
            description: '适合作为网站背景的轻柔音乐',
            is_public: true,
            created_at: new Date('2024-01-25 15:30:00'),
            updated_at: new Date('2024-01-25 15:30:00')
        },
        {
            filename: 'podcast-episode-1.wav',
            original_name: '播客第一期.wav',
            mime_type: 'audio/wav',
            size: 25165824,
            url: '/uploads/audio/podcast-episode-1.wav',
            uploader_id: user3Id,
            category: 'audio',
            tags: JSON.stringify(['播客', '音频', 'WAV', '高质量']),
            description: '技术播客第一期：前端开发趋势讨论',
            is_public: true,
            created_at: new Date('2024-02-15 10:00:00'),
            updated_at: new Date('2024-02-15 10:00:00')
        },
        {
            filename: 'user-manual-v2.pdf',
            original_name: '用户手册v2.0.pdf',
            mime_type: 'application/pdf',
            size: 2097152,
            url: '/uploads/documents/user-manual-v2.pdf',
            uploader_id: adminUserId,
            category: 'document',
            tags: JSON.stringify(['手册', '用户指南', 'PDF', '文档']),
            description: '产品用户操作手册，版本2.0',
            is_public: true,
            created_at: new Date('2024-01-30 09:45:00'),
            updated_at: new Date('2024-01-30 09:45:00')
        },
        {
            filename: 'api-documentation.docx',
            original_name: 'API接口文档.docx',
            mime_type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            size: 1048576,
            url: '/uploads/documents/api-documentation.docx',
            uploader_id: user1Id,
            category: 'document',
            tags: JSON.stringify(['API', '文档', 'DOCX', '开发']),
            description: '系统API接口的详细说明文档',
            is_public: false,
            created_at: new Date('2024-02-08 14:15:00'),
            updated_at: new Date('2024-02-08 14:15:00')
        },
        {
            filename: 'project-proposal.pptx',
            original_name: '项目提案.pptx',
            mime_type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            size: 5242880,
            url: '/uploads/documents/project-proposal.pptx',
            uploader_id: user2Id,
            category: 'document',
            tags: JSON.stringify(['提案', '项目', 'PPT', '演示']),
            description: '新项目的详细提案演示文稿',
            is_public: false,
            created_at: new Date('2024-02-12 11:30:00'),
            updated_at: new Date('2024-02-12 11:30:00')
        },
        {
            filename: 'data-export.xlsx',
            original_name: '数据导出表格.xlsx',
            mime_type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            size: 3145728,
            url: '/uploads/documents/data-export.xlsx',
            uploader_id: user3Id,
            category: 'document',
            tags: JSON.stringify(['数据', '导出', 'Excel', '表格']),
            description: '系统数据的导出表格文件',
            is_public: false,
            created_at: new Date('2024-02-18 16:00:00'),
            updated_at: new Date('2024-02-18 16:00:00')
        }
    ];
    await queryInterface.bulkInsert('media', mediaData);
    console.log(`✅ 成功创建 ${mediaData.length} 条媒体文件记录`);
    console.log('📊 媒体文件类型分布:');
    console.log(`   - 图片: ${mediaData.filter(m => m.category === 'image').length} 个`);
    console.log(`   - 视频: ${mediaData.filter(m => m.category === 'video').length} 个`);
    console.log(`   - 音频: ${mediaData.filter(m => m.category === 'audio').length} 个`);
    console.log(`   - 文档: ${mediaData.filter(m => m.category === 'document').length} 个`);
};
exports.up = up;
const down = async (queryInterface) => {
    console.log('🗑️ 删除媒体文件种子数据...');
    await queryInterface.bulkDelete('media', {}, {});
    console.log('✅ 媒体文件种子数据删除完成');
};
exports.down = down;
//# sourceMappingURL=008-media.js.map