"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationController = void 0;
const models_1 = require("../models");
const paramValidation_1 = require("../utils/paramValidation");
class NotificationController {
    static async getNotifications(req, res) {
        try {
            const userId = req.user.id;
            const { page = 1, limit = 20, type, is_read, priority } = req.query;
            const offset = (Number(page) - 1) * Number(limit);
            const whereClause = { recipientId: userId };
            if (type) {
                whereClause.type = type;
            }
            if (is_read !== undefined) {
                whereClause.isRead = is_read === 'true';
            }
            if (priority) {
                whereClause.priority = priority;
            }
            const { rows: notifications, count: total } = await models_1.Notification.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: models_1.User,
                        as: 'sender',
                        attributes: ['id', 'username'],
                        required: false
                    }
                ],
                order: [
                    ['isRead', 'ASC'],
                    ['priority', 'DESC'],
                    ['createdAt', 'DESC']
                ],
                limit: Number(limit),
                offset,
                distinct: true
            });
            res.json({
                success: true,
                data: {
                    notifications,
                    pagination: {
                        page: Number(page),
                        limit: Number(limit),
                        total,
                        totalPages: Math.ceil(total / Number(limit))
                    }
                }
            });
        }
        catch (error) {
            console.error('获取通知列表失败:', error);
            res.status(500).json({
                success: false,
                message: '获取通知列表失败'
            });
        }
    }
    static async getUnreadCount(req, res) {
        try {
            const userId = req.user.id;
            const count = await models_1.Notification.getUnreadCount(userId);
            res.json({
                success: true,
                data: { count }
            });
        }
        catch (error) {
            console.error('获取未读通知数量失败:', error);
            res.status(500).json({
                success: false,
                message: '获取未读通知数量失败'
            });
        }
    }
    static async markAsRead(req, res) {
        try {
            const userId = req.user.id;
            const notificationId = (0, paramValidation_1.getIdParam)(req);
            const notification = await models_1.Notification.findOne({
                where: {
                    id: notificationId,
                    recipientId: userId
                }
            });
            if (!notification) {
                res.status(404).json({
                    success: false,
                    message: '通知不存在'
                });
                return;
            }
            await notification.markAsRead();
            res.json({
                success: true,
                message: '通知已标记为已读',
                data: notification
            });
        }
        catch (error) {
            console.error('标记通知已读失败:', error);
            res.status(500).json({
                success: false,
                message: '标记通知已读失败'
            });
        }
    }
    static async markBatchAsRead(req, res) {
        try {
            const userId = req.user.id;
            const { notificationIds } = req.body;
            if (!Array.isArray(notificationIds) || notificationIds.length === 0) {
                res.status(400).json({
                    success: false,
                    message: '请提供有效的通知ID列表'
                });
                return;
            }
            const affectedCount = await models_1.Notification.markBulkAsRead(notificationIds, userId);
            res.json({
                success: true,
                message: `成功标记 ${affectedCount} 条通知为已读`,
                data: { affectedCount }
            });
        }
        catch (error) {
            console.error('批量标记通知已读失败:', error);
            res.status(500).json({
                success: false,
                message: '批量标记通知已读失败'
            });
        }
    }
    static async markAllAsRead(req, res) {
        try {
            const userId = req.user.id;
            const [affectedCount] = await models_1.Notification.update({
                isRead: true,
                readAt: new Date()
            }, {
                where: {
                    recipientId: userId,
                    isRead: false
                }
            });
            res.json({
                success: true,
                message: `成功标记 ${affectedCount} 条通知为已读`,
                data: { affectedCount }
            });
        }
        catch (error) {
            console.error('标记所有通知已读失败:', error);
            res.status(500).json({
                success: false,
                message: '标记所有通知已读失败'
            });
        }
    }
    static async deleteNotification(req, res) {
        try {
            const userId = req.user.id;
            const notificationId = (0, paramValidation_1.getIdParam)(req);
            const notification = await models_1.Notification.findOne({
                where: {
                    id: notificationId,
                    recipientId: userId
                }
            });
            if (!notification) {
                res.status(404).json({
                    success: false,
                    message: '通知不存在'
                });
                return;
            }
            await notification.destroy();
            res.json({
                success: true,
                message: '通知删除成功'
            });
        }
        catch (error) {
            console.error('删除通知失败:', error);
            res.status(500).json({
                success: false,
                message: '删除通知失败'
            });
        }
    }
    static async deleteBatchNotifications(req, res) {
        try {
            const userId = req.user.id;
            const { notificationIds } = req.body;
            if (!Array.isArray(notificationIds) || notificationIds.length === 0) {
                res.status(400).json({
                    success: false,
                    message: '请提供有效的通知ID列表'
                });
                return;
            }
            const deletedCount = await models_1.Notification.destroy({
                where: {
                    id: notificationIds,
                    recipientId: userId
                }
            });
            res.json({
                success: true,
                message: `成功删除 ${deletedCount} 条通知`,
                data: { deletedCount }
            });
        }
        catch (error) {
            console.error('批量删除通知失败:', error);
            res.status(500).json({
                success: false,
                message: '批量删除通知失败'
            });
        }
    }
    static async getPreferences(req, res) {
        try {
            const userId = req.user.id;
            let preferences = await models_1.NotificationPreference.getUserPreferences(userId);
            if (preferences.length === 0) {
                preferences = await models_1.NotificationPreference.initializeDefaultPreferences(userId);
            }
            res.json({
                success: true,
                data: preferences
            });
        }
        catch (error) {
            console.error('获取通知偏好设置失败:', error);
            res.status(500).json({
                success: false,
                message: '获取通知偏好设置失败'
            });
        }
    }
    static async updatePreferences(req, res) {
        try {
            const userId = req.user.id;
            const { preferences } = req.body;
            if (!Array.isArray(preferences)) {
                res.status(400).json({
                    success: false,
                    message: '请提供有效的偏好设置列表'
                });
                return;
            }
            const updatedPreferences = await models_1.NotificationPreference.updateUserPreferences(userId, preferences);
            res.json({
                success: true,
                message: '通知偏好设置更新成功',
                data: updatedPreferences
            });
        }
        catch (error) {
            console.error('更新通知偏好设置失败:', error);
            res.status(500).json({
                success: false,
                message: '更新通知偏好设置失败'
            });
        }
    }
}
exports.NotificationController = NotificationController;
//# sourceMappingURL=notification.js.map