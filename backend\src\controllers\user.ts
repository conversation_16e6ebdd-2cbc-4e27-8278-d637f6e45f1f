import { Request, Response, NextFunction } from 'express'
import { User } from '../models/User'
import { UserRole } from '../models/UserRole'
import { Role } from '../models/Role'
import { createError } from '../middleware/errorHandler'
import { hashPassword } from '../utils/password'
import Joi from 'joi'
import { Op } from 'sequelize'
import { getIdParam, getUserIdParam, getPaginationParams, getSearchParam } from '../utils/paramValidation'

/**
 * 扩展 Express 的 Request 接口，添加用户认证信息
 */
interface AuthenticatedRequest extends Request {
  user?: {
    id: number
    username: string
    email: string
  }
}

/**
 * 用户查询参数验证模式
 */
const getUsersQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  search: Joi.string().max(100).optional(),
  orderBy: Joi.string().valid('id', 'username', 'email', 'createdAt').default('createdAt'),
  orderDirection: Joi.string().valid('ASC', 'DESC').default('DESC'),
  includeRoles: Joi.boolean().default(false)
})

/**
 * 用户创建参数验证模式
 */
const createUserSchema = Joi.object({
  username: Joi.string().alphanum().min(3).max(50).required(),
  email: Joi.string().email().max(100).required(),
  password: Joi.string().min(6).max(100).required(),
  roleIds: Joi.array().items(Joi.number().integer().min(1)).optional()
})

/**
 * 用户更新参数验证模式
 */
const updateUserSchema = Joi.object({
  username: Joi.string().alphanum().min(3).max(50).optional(),
  email: Joi.string().email().max(100).optional(),
  password: Joi.string().min(6).max(100).optional(),
  roleIds: Joi.array().items(Joi.number().integer().min(1)).optional()
})

/**
 * 用户控制器类
 * 提供用户管理相关的API接口
 */
export class UserController {
  /**
   * 获取用户列表
   * @param req - Express请求对象
   * @param res - Express响应对象
   * @param next - Express下一个中间件函数
   */
  static async getUsers(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // 验证查询参数
      const { error, value } = getUsersQuerySchema.validate(req.query)
      if (error) {
        throw createError(400, error.details[0]?.message || 'Invalid query parameters', 'VALIDATION_ERROR')
      }

      const { page, limit, search, orderBy, orderDirection, includeRoles } = value
      const offset = (page - 1) * limit

      // 构建查询条件
      const where: any = {}
      if (search) {
        where[Op.or] = [
          { username: { [Op.iLike]: `%${search}%` } },
          { email: { [Op.iLike]: `%${search}%` } }
        ]
      }

      // 构建包含关系
      const include: any[] = []
      if (includeRoles) {
        include.push({
          model: Role,
          as: 'roles',
          attributes: ['id', 'name', 'description'],
          through: { attributes: [] }
        })
      }

      // 查询用户
      const { rows: users, count: total } = await User.findAndCountAll({
        where,
        include,
        attributes: { exclude: ['passwordHash'] },
        order: [[orderBy, orderDirection]],
        limit,
        offset
      })

      const totalPages = Math.ceil(total / limit)

      res.json({
        success: true,
        data: {
          users,
          pagination: {
            page,
            limit,
            total,
            totalPages
          }
        },
        message: 'Users retrieved successfully'
      })
    } catch (error) {
      next(error)
    }
  }

  /**
   * 根据ID获取用户详情
   * @param req - Express请求对象
   * @param res - Express响应对象
   * @param next - Express下一个中间件函数
   */
  static async getUserById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = getUserIdParam(req)
      if (isNaN(userId)) {
        throw createError(400, 'Invalid user ID', 'INVALID_PARAMETER')
      }

      const user = await User.findByPk(userId, {
        attributes: { exclude: ['passwordHash'] },
        include: [
          {
            model: Role,
            as: 'roles',
            attributes: ['id', 'name', 'description'],
            through: { attributes: ['assignedAt', 'assignedBy'] }
          }
        ]
      })

      if (!user) {
        throw createError(404, 'User not found', 'USER_NOT_FOUND')
      }

      res.json({
        success: true,
        data: user,
        message: 'User retrieved successfully'
      })
    } catch (error) {
      next(error)
    }
  }

  /**
   * 创建新用户
   * @param req - Express请求对象
   * @param res - Express响应对象
   * @param next - Express下一个中间件函数
   */
  static async createUser(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      // 验证请求参数
      const { error, value } = createUserSchema.validate(req.body)
      if (error) {
        throw createError(400, error.details[0]?.message || 'Invalid parameters', 'VALIDATION_ERROR')
      }

      const { username, email, password, roleIds = [] } = value

      // 检查用户名是否已存在
      const existingUserByUsername = await User.findByUsername(username)
      if (existingUserByUsername) {
        throw createError(400, 'Username already exists', 'USERNAME_EXISTS')
      }

      // 检查邮箱是否已存在
      const existingUserByEmail = await User.findByEmail(email)
      if (existingUserByEmail) {
        throw createError(400, 'Email already exists', 'EMAIL_EXISTS')
      }

      // 验证角色ID是否有效
      if (roleIds.length > 0) {
        const validRoles = await Role.findAll({
          where: { id: roleIds, isActive: true }
        })
        if (validRoles.length !== roleIds.length) {
          throw createError(400, 'Invalid role IDs provided', 'INVALID_ROLE_IDS')
        }
      }

      // 创建用户
      const passwordHash = await hashPassword(password)
      const user = await User.create({
        username,
        email,
        passwordHash,
        isActive: true,
        emailVerified: false
      })

      // 分配角色
      if (roleIds.length > 0) {
        const userRoleData = roleIds.map((roleId: number) => ({
          userId: user.id,
          roleId,
          assignedBy: req.user?.id
        }))
        await UserRole.bulkCreate(userRoleData)
      }

      // 获取完整的用户信息（包含角色）
      const createdUser = await User.findByPk(user.id, {
        attributes: { exclude: ['passwordHash'] },
        include: [
          {
            model: Role,
            as: 'roles',
            attributes: ['id', 'name', 'description'],
            through: { attributes: [] }
          }
        ]
      })

      res.status(201).json({
        success: true,
        data: createdUser,
        message: 'User created successfully'
      })
    } catch (error) {
      next(error)
    }
  }

  /**
   * 更新用户信息
   * @param req - Express请求对象
   * @param res - Express响应对象
   * @param next - Express下一个中间件函数
   */
  static async updateUser(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = getUserIdParam(req)
      if (isNaN(userId)) {
        throw createError(400, 'Invalid user ID', 'INVALID_PARAMETER')
      }

      // 验证请求参数
      const { error, value } = updateUserSchema.validate(req.body)
      if (error) {
        throw createError(400, error.details[0]?.message || 'Invalid parameters', 'VALIDATION_ERROR')
      }

      const { username, email, password, roleIds } = value

      // 查找用户
      const user = await User.findByPk(userId)
      if (!user) {
        throw createError(404, 'User not found', 'USER_NOT_FOUND')
      }

      // 检查用户名是否与其他用户重复
      if (username && username !== user.username) {
        const existingUser = await User.findByUsername(username)
        if (existingUser) {
          throw createError(400, 'Username already exists', 'USERNAME_EXISTS')
        }
      }

      // 检查邮箱是否与其他用户重复
      if (email && email !== user.email) {
        const existingUser = await User.findByEmail(email)
        if (existingUser) {
          throw createError(400, 'Email already exists', 'EMAIL_EXISTS')
        }
      }

      // 准备更新数据
      const updateData: any = {}
      if (username) updateData.username = username
      if (email) updateData.email = email
      if (password) updateData.passwordHash = await hashPassword(password)

      // 更新用户基本信息
      if (Object.keys(updateData).length > 0) {
        await user.update(updateData)
      }

      // 更新角色分配
      if (roleIds !== undefined) {
        // 验证角色ID是否有效
        if (roleIds.length > 0) {
          const validRoles = await Role.findAll({
            where: { id: roleIds, isActive: true }
          })
          if (validRoles.length !== roleIds.length) {
            throw createError(400, 'Invalid role IDs provided', 'INVALID_ROLE_IDS')
          }
        }

        // 删除现有角色分配
        await UserRole.destroy({ where: { userId } })

        // 创建新的角色分配
        if (roleIds.length > 0) {
          const userRoleData = roleIds.map((roleId: number) => ({
            userId,
            roleId,
            assignedBy: req.user?.id
          }))
          await UserRole.bulkCreate(userRoleData)
        }
      }

      // 获取更新后的用户信息
      const updatedUser = await User.findByPk(userId, {
        attributes: { exclude: ['passwordHash'] },
        include: [
          {
            model: Role,
            as: 'roles',
            attributes: ['id', 'name', 'description'],
            through: { attributes: [] }
          }
        ]
      })

      res.json({
        success: true,
        data: updatedUser,
        message: 'User updated successfully'
      })
    } catch (error) {
      next(error)
    }
  }

  /**
   * 删除用户
   * @param req - Express请求对象
   * @param res - Express响应对象
   * @param next - Express下一个中间件函数
   */
  static async deleteUser(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = getUserIdParam(req)
      if (isNaN(userId)) {
        throw createError(400, 'Invalid user ID', 'INVALID_PARAMETER')
      }

      // 防止删除自己
      if (req.user && req.user.id === userId) {
        throw createError(400, 'Cannot delete your own account', 'CANNOT_DELETE_SELF')
      }

      const user = await User.findByPk(userId)
      if (!user) {
        throw createError(404, 'User not found', 'USER_NOT_FOUND')
      }

      // 删除用户（会级联删除相关的角色分配）
      await user.destroy()

      res.json({
        success: true,
        message: 'User deleted successfully'
      })
    } catch (error) {
      next(error)
    }
  }

  /**
   * 获取用户统计信息
   * @param req - Express请求对象
   * @param res - Express响应对象
   * @param next - Express下一个中间件函数
   */
  static async getUserStats(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const totalUsers = await User.count()

      // 获取最近30天注册的用户数
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
      const recentUsers = await User.count({
        where: {
          createdAt: {
            [Op.gte]: thirtyDaysAgo
          }
        }
      })

      // 获取角色分布统计
      const roleStats = await UserRole.findAll({
        include: [
          {
            model: Role,
            as: 'role',
            attributes: ['name']
          }
        ],
        attributes: ['roleId'],
        group: ['roleId', 'role.id', 'role.name'],
        raw: true
      })

      res.json({
        success: true,
        data: {
          totalUsers,
          recentUsers,
          roleStats
        },
        message: 'User statistics retrieved successfully'
      })
    } catch (error) {
      next(error)
    }
  }
}

// 导出控制器方法
export const {
  getUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  getUserStats
} = UserController

export default UserController
