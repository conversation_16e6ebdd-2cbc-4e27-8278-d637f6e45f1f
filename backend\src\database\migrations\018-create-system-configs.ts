import { QueryInterface, DataTypes } from 'sequelize'

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.createTable('system_configs', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    key: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      comment: '配置键名'
    },
    value: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '配置值'
    },
    type: {
      type: DataTypes.ENUM('string', 'number', 'boolean', 'json'),
      allowNull: false,
      defaultValue: 'string',
      comment: '配置值类型'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '配置描述'
    },
    category: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: 'general',
      comment: '配置分类'
    },
    is_public: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否公开（前端可访问）'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  })

  // 添加索引
  await queryInterface.addIndex('system_configs', ['key'], {
    unique: true,
    name: 'system_configs_key_unique'
  })

  await queryInterface.addIndex('system_configs', ['category'], {
    name: 'system_configs_category_index'
  })

  await queryInterface.addIndex('system_configs', ['is_public'], {
    name: 'system_configs_is_public_index'
  })
}

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.dropTable('system_configs')
}
