{"version": 3, "file": "permission.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/permission.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAA;AAOzD,MAAM,WAAW,oBAAqB,SAAQ,OAAO;IACnD,IAAI,CAAC,EAAE;QACL,EAAE,EAAE,MAAM,CAAA;QACV,QAAQ,EAAE,MAAM,CAAA;QAChB,KAAK,EAAE,MAAM,CAAA;KACd,CAAA;CACF;AAQD,eAAO,MAAM,iBAAiB,GAAI,gBAAgB,MAAM,MACxC,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,OAAO,CAAC,IAAI,CAqB1F,CAAA;AAQD,eAAO,MAAM,WAAW,GAAI,UAAU,MAAM,MAC5B,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,OAAO,CAAC,IAAI,CAqB1F,CAAA;AAOD,eAAO,MAAM,qBAAqB,GAAI,iBAAiB,MAAM,EAAE,MAC/C,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,OAAO,CAAC,IAAI,CAsB1F,CAAA;AAOD,eAAO,MAAM,oBAAoB,GAAI,iBAAiB,MAAM,EAAE,MAC9C,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,OAAO,CAAC,IAAI,CA4B1F,CAAA;AAOD,eAAO,MAAM,eAAe,GAAI,WAAW,MAAM,EAAE,MACnC,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,OAAO,CAAC,IAAI,CAsB1F,CAAA;AAOD,eAAO,MAAM,cAAc,GAAI,WAAW,MAAM,EAAE,MAClC,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,OAAO,CAAC,IAAI,CA4B1F,CAAA;AAMD,eAAO,MAAM,iBAAiB,QA/JT,oBAAoB,OAAO,QAAQ,QAAQ,YAAY,KAAG,OAAO,CAAC,IAAI,CA+JhC,CAAA;AAM3D,eAAO,MAAM,YAAY,QAxCJ,oBAAoB,OAAO,QAAQ,QAAQ,YAAY,KAAG,OAAO,CAAC,IAAI,CAwCvB,CAAA;AAMpE,eAAO,MAAM,aAAa,QA9CL,oBAAoB,OAAO,QAAQ,QAAQ,YAAY,KAAG,OAAO,CAAC,IAAI,CA8CZ,CAAA;AAS/E,eAAO,MAAM,4BAA4B,GACvC,oBAAoB,CAAC,GAAG,EAAE,oBAAoB,KAAK,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,EACzE,kBAAkB,MAAM,MAEV,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,OAAO,CAAC,IAAI,CA+B1F,CAAA;AAOD,eAAO,MAAM,kBAAkB,GAAU,QAAQ,MAAM,KAAG,OAAO,CAAC,MAAM,EAAE,CAGzE,CAAA;AAOD,eAAO,MAAM,YAAY,GAAU,QAAQ,MAAM,KAAG,OAAO,CAAC,MAAM,EAAE,CAGnE,CAAA;AASD,eAAO,MAAM,4BAA4B,GACvC,gBAAgB,MAAM,EACtB,oBAAoB,CAAC,GAAG,EAAE,oBAAoB,KAAK,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,MAE3D,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,kBAuB3E,CAAA"}