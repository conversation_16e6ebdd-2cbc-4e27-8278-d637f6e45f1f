import { DataTypes, Model, Optional, Op } from 'sequelize'
import { sequelize } from '../config/database'
import { Article } from './Article'

/**
 * 文章阅读记录模型的属性接口定义
 */
export interface ArticleViewAttributes {
  id: number
  articleId: number
  userId?: number  // 可选，匿名用户为null
  ipAddress: string
  userAgent?: string
  viewedAt: Date
}

/**
 * 文章阅读记录创建时的属性接口定义
 */
export interface ArticleViewCreationAttributes extends Optional<ArticleViewAttributes, 'id' | 'userId' | 'userAgent' | 'viewedAt'> { }

/**
 * 文章阅读记录模型类
 * 用于统计文章的阅读量和分析用户行为
 */
export class ArticleView extends Model<ArticleViewAttributes, ArticleViewCreationAttributes> implements ArticleViewAttributes {
  public id!: number
  public articleId!: number
  public userId?: number
  public ipAddress!: string
  public userAgent?: string
  public viewedAt!: Date

  /**
   * 记录文章阅读
   */
  public static async recordView(articleId: number, ipAddress: string, userId?: number, userAgent?: string) {
    // 检查是否在短时间内重复访问（防刷）
    const recentView = await this.findOne({
      where: {
        articleId,
        ipAddress,
        ...(userId && { userId }),
        viewedAt: {
          [Op.gte]: new Date(Date.now() - 30 * 60 * 1000) // 30分钟内
        }
      }
    })

    if (!recentView) {
      const createData: any = {
        articleId,
        ipAddress,
        viewedAt: new Date()
      }

      if (userId !== undefined) {
        createData.userId = userId
      }

      if (userAgent !== undefined) {
        createData.userAgent = userAgent
      }

      return this.create(createData)
    }

    return recentView
  }

  /**
   * 获取文章的总阅读量
   */
  public static async getArticleViewCount(articleId: number): Promise<number> {
    return this.count({
      where: { articleId },
      distinct: true,
      col: 'ip_address'  // 按IP去重
    })
  }

  /**
   * 获取文章的唯一访客数
   */
  public static async getArticleUniqueViewers(articleId: number): Promise<number> {
    return this.count({
      where: { articleId },
      distinct: true,
      col: 'ip_address'
    })
  }

  /**
   * 获取用户的阅读历史
   */
  public static async getUserViewHistory(userId: number, limit: number = 20) {
    return this.findAll({
      where: { userId },
      order: [['viewedAt', 'DESC']],
      limit,
      include: [
        {
          model: Article,
          as: 'article',
          attributes: ['id', 'title', 'slug', 'excerpt']
        }
      ]
    })
  }

  /**
   * 获取热门文章（按阅读量排序）
   */
  public static async getPopularArticles(limit: number = 10, days: number = 7) {
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000)

    return this.findAll({
      attributes: [
        'articleId',
        [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('ip_address'))), 'viewCount']
      ],
      where: {
        viewedAt: {
          [Op.gte]: startDate
        }
      },
      group: ['articleId'],
      order: [[sequelize.literal('viewCount'), 'DESC']],
      limit,
      include: [
        {
          model: Article,
          as: 'article',
          attributes: ['id', 'title', 'slug', 'excerpt', 'publishedAt']
        }
      ]
    })
  }
}

/**
 * 初始化文章阅读记录模型
 */
ArticleView.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    articleId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'article_id',
      references: {
        model: 'articles',
        key: 'id'
      }
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'id'
      }
    },
    ipAddress: {
      type: DataTypes.STRING(45),
      allowNull: false,
      field: 'ip_address'
    },
    userAgent: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'user_agent'
    },
    viewedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: 'viewed_at'
    }
  },
  {
    sequelize,
    modelName: 'ArticleView',
    tableName: 'article_views',
    timestamps: false,
    underscored: true,
    indexes: [
      {
        fields: ['article_id']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['ip_address']
      },
      {
        fields: ['viewed_at']
      },
      {
        fields: ['article_id', 'ip_address', 'viewed_at']
      }
    ]
  }
)
