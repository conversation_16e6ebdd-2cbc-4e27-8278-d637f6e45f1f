"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('post_likes', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        post_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'posts',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        user_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    try {
        await queryInterface.addIndex('post_likes', ['post_id', 'user_id'], {
            unique: true,
            name: 'post_likes_post_user_unique'
        });
    }
    catch {
        console.log('Unique index post_likes_post_user_unique already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('post_likes', ['post_id']);
    }
    catch {
        console.log('Index post_likes_post_id already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('post_likes', ['user_id']);
    }
    catch {
        console.log('Index post_likes_user_id already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('post_likes', ['created_at']);
    }
    catch {
        console.log('Index post_likes_created_at already exists, skipping...');
    }
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('post_likes');
};
exports.down = down;
//# sourceMappingURL=012-create-post-likes.js.map