import { AuditLog, AuditLogQueryOptions, AuditLogStats, AuditLogCreationAttributes } from '../models/AuditLog';
export interface AuditLogQueryResult {
    logs: AuditLog[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
export interface AuditLogStatsOptions {
    userId?: number;
    startDate?: Date;
    endDate?: Date;
    resource?: string;
    action?: string;
}
export interface DetailedAuditLogStats extends AuditLogStats {
    userStats: Array<{
        userId: number;
        username: string;
        count: number;
    }>;
    hourlyStats: Array<{
        hour: number;
        count: number;
    }>;
    statusDistribution: {
        success: number;
        failed: number;
        pending: number;
    };
    averageDuration: number;
    topActions: Array<{
        action: string;
        count: number;
    }>;
    topResources: Array<{
        resource: string;
        count: number;
    }>;
}
export declare class AuditLogService {
    static createLog(logData: AuditLogCreationAttributes): Promise<AuditLog>;
    static queryLogs(options: AuditLogQueryOptions): Promise<AuditLogQueryResult>;
    static getUserLogs(userId: number, options: Omit<AuditLogQueryOptions, 'userId'>): Promise<AuditLogQueryResult>;
    static getDetailedStats(options?: AuditLogStatsOptions): Promise<DetailedAuditLogStats>;
    static getStats(userId?: number, days?: number): Promise<AuditLogStats>;
    static cleanupOldLogs(days?: number): Promise<number>;
    static getLogById(id: number): Promise<AuditLog | null>;
    private static buildWhereClause;
    private static buildReplacements;
}
export default AuditLogService;
//# sourceMappingURL=auditLog.d.ts.map