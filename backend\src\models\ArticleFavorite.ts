import { DataTypes, Model, Optional } from 'sequelize'
import { sequelize } from '../config/database'

/**
 * 文章收藏模型的属性接口定义
 */
export interface ArticleFavoriteAttributes {
  id: number
  articleId: number
  userId: number
  createdAt: Date
}

/**
 * 文章收藏创建时的属性接口定义
 */
export interface ArticleFavoriteCreationAttributes extends Optional<ArticleFavoriteAttributes, 'id' | 'createdAt'> { }

/**
 * 文章收藏模型类
 * 用于管理用户对文章的收藏功能
 */
export class ArticleFavorite extends Model<ArticleFavoriteAttributes, ArticleFavoriteCreationAttributes> implements ArticleFavoriteAttributes {
  public id!: number
  public articleId!: number
  public userId!: number
  public createdAt!: Date

  /**
   * 切换收藏状态
   */
  public static async toggleFavorite(articleId: number, userId: number): Promise<{ favorited: boolean; favoriteCount: number }> {
    const existingFavorite = await this.findOne({
      where: { articleId, userId }
    })

    if (existingFavorite) {
      // 取消收藏
      await existingFavorite.destroy()
      const favoriteCount = await this.count({ where: { articleId } })
      return { favorited: false, favoriteCount }
    } else {
      // 添加收藏
      await this.create({ articleId, userId })
      const favoriteCount = await this.count({ where: { articleId } })
      return { favorited: true, favoriteCount }
    }
  }

  /**
   * 检查用户是否已收藏文章
   */
  public static async isFavoritedByUser(articleId: number, userId: number): Promise<boolean> {
    const favorite = await this.findOne({
      where: { articleId, userId }
    })
    return !!favorite
  }

  /**
   * 获取文章的收藏数
   */
  public static async getArticleFavoriteCount(articleId: number): Promise<number> {
    return this.count({
      where: { articleId }
    })
  }

  /**
   * 获取用户收藏的文章列表
   */
  public static async getUserFavoriteArticles(userId: number, limit: number = 20, offset: number = 0) {
    return this.findAndCountAll({
      where: { userId },
      order: [['createdAt', 'DESC']],
      limit,
      offset,
      include: [
        {
          model: sequelize.models.Article,
          as: 'article',
          attributes: ['id', 'title', 'slug', 'excerpt', 'publishedAt'],
          include: [
            {
              model: sequelize.models.User,
              as: 'author',
              attributes: ['id', 'username']
            },
            {
              model: sequelize.models.Category,
              as: 'category',
              attributes: ['id', 'name', 'slug']
            }
          ]
        }
      ]
    })
  }

  /**
   * 获取最受收藏的文章
   */
  public static async getMostFavoritedArticles(limit: number = 10, days?: number) {
    const whereClause: any = {}

    if (days) {
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000)
      whereClause.createdAt = {
        [sequelize.Op.gte]: startDate
      }
    }

    return this.findAll({
      attributes: [
        'articleId',
        [sequelize.fn('COUNT', sequelize.col('id')), 'favoriteCount']
      ],
      where: whereClause,
      group: ['articleId'],
      order: [[sequelize.literal('favoriteCount'), 'DESC']],
      limit,
      include: [
        {
          model: sequelize.models.Article,
          as: 'article',
          attributes: ['id', 'title', 'slug', 'excerpt', 'publishedAt']
        }
      ]
    })
  }

  /**
   * 获取收藏了某篇文章的用户列表
   */
  public static async getArticleFavoriters(articleId: number, limit: number = 20, offset: number = 0) {
    return this.findAndCountAll({
      where: { articleId },
      order: [['createdAt', 'DESC']],
      limit,
      offset,
      include: [
        {
          model: sequelize.models.User,
          as: 'user',
          attributes: ['id', 'username']
        }
      ]
    })
  }
}

/**
 * 初始化文章收藏模型
 */
ArticleFavorite.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    articleId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'article_id',
      references: {
        model: 'articles',
        key: 'id'
      }
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'id'
      }
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    }
  },
  {
    sequelize,
    modelName: 'ArticleFavorite',
    tableName: 'article_favorites',
    timestamps: true,
    updatedAt: false,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['article_id', 'user_id']
      },
      {
        fields: ['article_id']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['created_at']
      }
    ]
  }
)
