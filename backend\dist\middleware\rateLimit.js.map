{"version": 3, "file": "rateLimit.js", "sourceRoot": "", "sources": ["../../src/middleware/rateLimit.ts"], "names": [], "mappings": ";;;AAwGA,0CA4CC;AAnJD,iDAA4C;AA0B5C,MAAM,cAAc;IAIlB;QAHQ,UAAK,GAAG,IAAI,GAAG,EAAyB,CAAA;QAK9C,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;YACtC,IAAI,CAAC,OAAO,EAAE,CAAA;QAChB,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,CAAA;IACf,CAAC;IAKD,GAAG,CAAC,GAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;IAC5B,CAAC;IAKD,GAAG,CAAC,GAAW,EAAE,MAAqB;QACpC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;IAC7B,CAAC;IAKD,SAAS,CAAC,GAAW,EAAE,QAAgB;QACrC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAEpC,IAAI,CAAC,QAAQ,IAAI,GAAG,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;YAE1C,MAAM,MAAM,GAAkB;gBAC5B,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,GAAG,GAAG,QAAQ;gBACzB,YAAY,EAAE,GAAG;aAClB,CAAA;YACD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;YAC3B,OAAO,MAAM,CAAA;QACf,CAAC;QAGD,QAAQ,CAAC,KAAK,EAAE,CAAA;QAChB,OAAO,QAAQ,CAAA;IACjB,CAAC;IAKO,OAAO;QACb,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,KAAK,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YACjD,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;gBAC3B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;YACxB,CAAC;QACH,CAAC;IACH,CAAC;IAKD,OAAO;QACL,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;QACrC,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;IACpB,CAAC;CACF;AAGD,MAAM,WAAW,GAAG,IAAI,cAAc,EAAE,CAAA;AAKxC,SAAgB,eAAe,CAAC,MAAuB;IACrD,MAAM,EACJ,QAAQ,EACR,WAAW,EACX,OAAO,GAAG,cAAc,EACxB,sBAAsB,GAAG,KAAK,EAC9B,kBAAkB,GAAG,KAAK,EAC1B,YAAY,GAAG,CAAC,GAAY,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa,IAAI,SAAS,EACrF,GAAG,MAAM,CAAA;IAEV,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC/D,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAA;QAC7B,MAAM,MAAM,GAAG,WAAW,CAAC,SAAS,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;QAGnD,GAAG,CAAC,GAAG,CAAC;YACN,mBAAmB,EAAE,WAAW,CAAC,QAAQ,EAAE;YAC3C,uBAAuB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE;YAC3E,mBAAmB,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE;SAC9D,CAAC,CAAA;QAEF,IAAI,MAAM,CAAC,KAAK,GAAG,WAAW,EAAE,CAAC;YAC/B,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,OAAO,EAAE,qBAAqB,CAAC,CAAA;QACxD,CAAC;QAGD,IAAI,sBAAsB,IAAI,kBAAkB,EAAE,CAAC;YACjD,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAA;YAC7B,GAAG,CAAC,IAAI,GAAG,UAAS,IAAI;gBACtB,MAAM,UAAU,GAAG,GAAG,CAAC,UAAU,CAAA;gBACjC,MAAM,UAAU,GACd,CAAC,sBAAsB,IAAI,UAAU,GAAG,GAAG,CAAC;oBAC5C,CAAC,kBAAkB,IAAI,UAAU,IAAI,GAAG,CAAC,CAAA;gBAE3C,IAAI,UAAU,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;oBACnC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAChB,CAAC;gBAED,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;YACtC,CAAC,CAAA;QACH,CAAC;QAED,IAAI,EAAE,CAAA;IACR,CAAC,CAAA;AACH,CAAC;AAKY,QAAA,gBAAgB,GAAG,eAAe,CAAC;IAC9C,QAAQ,EAAE,EAAE,GAAG,IAAI;IACnB,WAAW,EAAE,GAAG;IAChB,OAAO,EAAE,iBAAiB;CAC3B,CAAC,CAAA;AAKW,QAAA,aAAa,GAAG,eAAe,CAAC;IAC3C,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IACxB,WAAW,EAAE,CAAC;IACd,OAAO,EAAE,mBAAmB;IAC5B,sBAAsB,EAAE,IAAI;CAC7B,CAAC,CAAA;AAKW,QAAA,qBAAqB,GAAG,eAAe,CAAC;IACnD,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IACxB,WAAW,EAAE,CAAC;IACd,OAAO,EAAE,kBAAkB;CAC5B,CAAC,CAAA;AAKW,QAAA,sBAAsB,GAAG,eAAe,CAAC;IACpD,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IACxB,WAAW,EAAE,CAAC;IACd,OAAO,EAAE,oBAAoB;CAC9B,CAAC,CAAA;AAKW,QAAA,cAAc,GAAG,eAAe,CAAC;IAC5C,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IACxB,WAAW,EAAE,CAAC;IACd,OAAO,EAAE,kBAAkB;CAC5B,CAAC,CAAA;AAKW,QAAA,eAAe,GAAG,eAAe,CAAC;IAC7C,QAAQ,EAAE,EAAE,GAAG,IAAI;IACnB,WAAW,EAAE,EAAE;IACf,OAAO,EAAE,gBAAgB;CAC1B,CAAC,CAAA;AAKW,QAAA,gBAAgB,GAAG,eAAe,CAAC;IAC9C,QAAQ,EAAE,EAAE,GAAG,IAAI;IACnB,WAAW,EAAE,CAAC;IACd,OAAO,EAAE,gBAAgB;CAC1B,CAAC,CAAA;AAKW,QAAA,eAAe,GAAG,eAAe,CAAC;IAC7C,QAAQ,EAAE,EAAE,GAAG,IAAI;IACnB,WAAW,EAAE,EAAE;IACf,OAAO,EAAE,gBAAgB;CAC1B,CAAC,CAAA;AAKK,MAAM,wBAAwB,GAAG,CAAC,MAA6C,EAAE,EAAE;IACxF,OAAO,eAAe,CAAC;QACrB,GAAG,MAAM;QACT,YAAY,EAAE,CAAC,GAAY,EAAE,EAAE;YAE7B,MAAM,IAAI,GAAI,GAAW,CAAC,IAAI,CAAA;YAC9B,OAAO,IAAI,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,SAAS,EAAE,CAAA;QAC/D,CAAC;KACF,CAAC,CAAA;AACJ,CAAC,CAAA;AATY,QAAA,wBAAwB,4BASpC;AAKY,QAAA,mBAAmB,GAAG,IAAA,gCAAwB,EAAC;IAC1D,QAAQ,EAAE,EAAE,GAAG,IAAI;IACnB,WAAW,EAAE,EAAE;IACf,OAAO,EAAE,cAAc;CACxB,CAAC,CAAA;AAKW,QAAA,cAAc,GAAG,eAAe,CAAC;IAC5C,QAAQ,EAAE,EAAE,GAAG,IAAI;IACnB,WAAW,EAAE,EAAE;IACf,OAAO,EAAE,gBAAgB;IACzB,YAAY,EAAE,CAAC,GAAY,EAAE,EAAE;QAC7B,MAAM,IAAI,GAAI,GAAW,CAAC,IAAI,CAAA;QAC9B,OAAO,IAAI,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,SAAS,EAAE,CAAA;IAChE,CAAC;CACF,CAAC,CAAA;AAKK,MAAM,gBAAgB,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IACxF,MAAM,IAAI,GAAI,GAAW,CAAC,IAAI,CAAA;IAE9B,IAAI,CAAC,IAAI,EAAE,CAAC;QAEV,OAAO,IAAA,wBAAgB,EAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAA;IACzC,CAAC;IAID,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,CAAA;IACtE,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,CAAA;IAE1E,IAAI,OAAO,EAAE,CAAC;QACZ,OAAO,IAAA,sBAAc,EAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAA;IACvC,CAAC;SAAM,IAAI,SAAS,EAAE,CAAC;QAErB,OAAO,IAAA,gCAAwB,EAAC;YAC9B,QAAQ,EAAE,EAAE,GAAG,IAAI;YACnB,WAAW,EAAE,GAAG;YAChB,OAAO,EAAE,cAAc;SACxB,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAA;IACpB,CAAC;SAAM,CAAC;QAEN,OAAO,IAAA,2BAAmB,EAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAA;IAC5C,CAAC;AACH,CAAC,CAAA;AA1BY,QAAA,gBAAgB,oBA0B5B;AAKM,MAAM,mBAAmB,GAAG,GAAS,EAAE;IAC5C,WAAW,CAAC,OAAO,EAAE,CAAA;AACvB,CAAC,CAAA;AAFY,QAAA,mBAAmB,uBAE/B"}