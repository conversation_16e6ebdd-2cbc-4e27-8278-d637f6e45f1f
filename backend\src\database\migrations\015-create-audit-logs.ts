import { QueryInterface, DataTypes } from 'sequelize'

/**
 * 执行数据库迁移升级操作，创建操作日志表
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 返回空的Promise，表示异步操作完成
 * 迁移序号：015 (重命名自 20250129_create_audit_logs_table.ts)
 */
export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // 创建操作日志表，记录用户的所有关键操作
  await queryInterface.createTable('audit_logs', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: true, // 允许为空，因为可能有系统操作
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    },
    action: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '操作类型：CREATE, UPDATE, DELETE, LOGIN, LOGOUT, VIEW等'
    },
    resource: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '操作的资源类型：user, article, post, comment等'
    },
    resource_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '操作的资源ID'
    },
    old_data: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '操作前的数据（JSON格式）'
    },
    new_data: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '操作后的数据（JSON格式）'
    },
    ip_address: {
      type: DataTypes.STRING(45),
      allowNull: true,
      comment: '操作者的IP地址（支持IPv6）'
    },
    user_agent: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '用户代理字符串'
    },
    session_id: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '会话ID'
    },
    status: {
      type: DataTypes.ENUM('success', 'failed', 'pending'),
      allowNull: false,
      defaultValue: 'success',
      comment: '操作状态'
    },
    error_message: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '错误信息（如果操作失败）'
    },
    duration: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '操作耗时（毫秒）'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  })

  // 添加索引以提高查询性能
  try {
    await queryInterface.addIndex('audit_logs', ['user_id'])
    await queryInterface.addIndex('audit_logs', ['action'])
    await queryInterface.addIndex('audit_logs', ['resource'])
    await queryInterface.addIndex('audit_logs', ['resource_id'])
    await queryInterface.addIndex('audit_logs', ['created_at'])
    await queryInterface.addIndex('audit_logs', ['status'])
    // 复合索引用于常见查询
    await queryInterface.addIndex('audit_logs', ['user_id', 'created_at'])
    await queryInterface.addIndex('audit_logs', ['resource', 'resource_id'])
    await queryInterface.addIndex('audit_logs', ['action', 'resource'])
  } catch (error) {
    console.log('Some indexes may already exist, skipping...', error)
  }
}

/**
 * 执行数据库迁移降级操作，删除操作日志表
 * @param queryInterface - Sequelize查询接口对象，用于执行数据库操作
 * @returns Promise<void> - 返回空的Promise，表示异步操作完成
 */
export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.dropTable('audit_logs')
}
