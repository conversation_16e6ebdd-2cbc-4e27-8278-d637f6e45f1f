import { DataTypes, Model, Optional, Association } from 'sequelize'
import { sequelize } from '../config/database'

/**
 * 权限模型的属性接口，定义了权限对象的基本字段结构
 */
export interface PermissionAttributes {
  id: number
  name: string
  description?: string
  resource: string
  action: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

/**
 * 权限创建时的属性接口，继承自 PermissionAttributes，但允许部分字段为空
 */
export interface PermissionCreationAttributes extends Optional<PermissionAttributes, 'id' | 'isActive' | 'createdAt' | 'updatedAt'> { }

/**
 * 权限模型类，用于与数据库中的 permissions 表进行交互
 * 实现了 PermissionAttributes 接口，并扩展了 Sequelize 的 Model 类
 */
export class Permission extends Model<PermissionAttributes, PermissionCreationAttributes> implements PermissionAttributes {
  public id!: number
  public name!: string
  public description?: string
  public resource!: string
  public action!: string
  public isActive!: boolean
  public createdAt!: Date
  public updatedAt!: Date

  // 关联关系
  public readonly roles?: any[]

  public static associations: {
    roles: Association<Permission, any>
  }

  /**
   * 根据权限名称查找权限
   * @param name - 权限名称
   * @returns 返回权限实例或null
   */
  public static async findByName(name: string): Promise<Permission | null> {
    return this.findOne({ where: { name } })
  }

  /**
   * 根据资源和操作查找权限
   * @param resource - 资源名称
   * @param action - 操作名称
   * @returns 返回权限实例或null
   */
  public static async findByResourceAndAction(resource: string, action: string): Promise<Permission | null> {
    return this.findOne({ where: { resource, action } })
  }

  /**
   * 获取所有活跃的权限
   * @returns 返回活跃权限列表
   */
  public static async getActivePermissions(): Promise<Permission[]> {
    return this.findAll({
      where: { isActive: true },
      order: [['resource', 'ASC'], ['action', 'ASC']]
    })
  }

  /**
   * 根据资源分组获取权限
   * @returns 返回按资源分组的权限
   */
  public static async getPermissionsByResource(): Promise<Record<string, Permission[]>> {
    const permissions = await this.getActivePermissions()
    const grouped: Record<string, Permission[]> = {}

    permissions.forEach(permission => {
      if (!grouped[permission.resource]) {
        grouped[permission.resource] = []
      }
      grouped[permission.resource]!.push(permission)
    })

    return grouped
  }

  /**
   * 获取所有资源列表
   * @returns 返回资源名称数组
   */
  public static async getResources(): Promise<string[]> {
    const permissions = await this.findAll({
      attributes: ['resource'],
      group: ['resource'],
      order: [['resource', 'ASC']]
    })

    return permissions.map(p => p.resource)
  }

  /**
   * 获取指定资源的所有操作
   * @param resource - 资源名称
   * @returns 返回操作名称数组
   */
  public static async getActionsByResource(resource: string): Promise<string[]> {
    const permissions = await this.findAll({
      attributes: ['action'],
      where: { resource },
      order: [['action', 'ASC']]
    })

    return permissions.map(p => p.action)
  }

  /**
   * 批量创建权限
   * @param permissionsData - 权限数据数组
   * @returns 返回创建的权限实例数组
   */
  public static async bulkCreatePermissions(permissionsData: PermissionCreationAttributes[]): Promise<Permission[]> {
    return this.bulkCreate(permissionsData, {
      ignoreDuplicates: true,
      returning: true
    })
  }

  /**
   * 获取权限的完整名称（resource.action格式）
   * @returns 返回完整权限名称
   */
  public getFullName(): string {
    return `${this.resource}.${this.action}`
  }

  /**
   * 检查权限是否匹配指定的资源和操作
   * @param resource - 资源名称
   * @param action - 操作名称
   * @returns 返回是否匹配
   */
  public matches(resource: string, action: string): boolean {
    return this.resource === resource && this.action === action
  }

  /**
   * 获取使用此权限的角色数量
   * @returns 返回角色数量
   */
  public async getRoleCount(): Promise<number> {
    const RolePermission = sequelize.models.RolePermission as any
    const result = await RolePermission.count({ where: { permissionId: this.id } })
    return typeof result === 'number' ? result : 0
  }

  /**
   * 获取拥有此权限的所有角色
   * @returns 返回角色列表
   */
  public async getRoles(): Promise<any[]> {
    const Role = sequelize.models.Role as any
    const RolePermission = sequelize.models.RolePermission as any

    const rolePermissions = await RolePermission.findAll({
      where: { permissionId: this.id },
      include: [{
        model: Role,
        as: 'role'
      }]
    })

    return rolePermissions.map((rp: any) => rp.role)
  }

  /**
   * 序列化权限信息，用于API响应
   * @returns 返回序列化后的权限信息
   */
  public toJSON(): PermissionAttributes & { fullName: string } {
    const values = { ...this.get() } as PermissionAttributes
    return {
      ...values,
      fullName: this.getFullName()
    }
  }
}

/**
 * 初始化 Permission 模型，配置其字段、验证规则和索引
 */
Permission.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 100],
        notEmpty: true
      },
      comment: '权限名称'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '权限描述'
    },
    resource: {
      type: DataTypes.STRING(50),
      allowNull: false,
      validate: {
        len: [2, 50],
        notEmpty: true
      },
      comment: '资源名称'
    },
    action: {
      type: DataTypes.STRING(50),
      allowNull: false,
      validate: {
        len: [2, 50],
        notEmpty: true
      },
      comment: '操作名称'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      field: 'is_active',
      comment: '是否激活'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  },
  {
    sequelize,
    modelName: 'Permission',
    tableName: 'permissions',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['name']
      },
      {
        unique: true,
        fields: ['resource', 'action']
      },
      {
        fields: ['resource']
      },
      {
        fields: ['action']
      },
      {
        fields: ['is_active']
      }
    ]
  }
)
