import { QueryInterface, DataTypes } from 'sequelize'

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.createTable('article_views', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    article_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'articles',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    },
    ip_address: {
      type: DataTypes.STRING(45),
      allowNull: false
    },
    user_agent: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    viewed_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  })

  // 添加索引
  await queryInterface.addIndex('article_views', ['article_id'])
  await queryInterface.addIndex('article_views', ['user_id'])
  await queryInterface.addIndex('article_views', ['ip_address'])
  await queryInterface.addIndex('article_views', ['viewed_at'])
  await queryInterface.addIndex('article_views', ['article_id', 'ip_address', 'viewed_at'])
}

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.dropTable('article_views')
}
