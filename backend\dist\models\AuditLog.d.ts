import { Model, Optional, Association } from 'sequelize';
import { User } from './User';
export interface AuditLogAttributes {
    id: number;
    userId?: number;
    action: string;
    resource: string;
    resourceId?: number;
    oldData?: string;
    newData?: string;
    ipAddress?: string;
    userAgent?: string;
    sessionId?: string;
    status: 'success' | 'failed' | 'pending';
    errorMessage?: string;
    duration?: number;
    createdAt: Date;
    updatedAt: Date;
}
export interface AuditLogCreationAttributes extends Optional<AuditLogAttributes, 'id' | 'userId' | 'resourceId' | 'oldData' | 'newData' | 'ipAddress' | 'userAgent' | 'sessionId' | 'status' | 'errorMessage' | 'duration' | 'createdAt' | 'updatedAt'> {
}
export interface AuditLogQueryOptions {
    userId?: number;
    action?: string;
    resource?: string;
    resourceId?: number;
    status?: 'success' | 'failed' | 'pending';
    startDate?: Date;
    endDate?: Date;
    ipAddress?: string;
    page?: number;
    limit?: number;
    orderBy?: 'createdAt' | 'action' | 'resource';
    orderDirection?: 'ASC' | 'DESC';
}
export interface AuditLogStats {
    totalLogs: number;
    successLogs: number;
    failedLogs: number;
    actionStats: Record<string, number>;
    resourceStats: Record<string, number>;
    dailyStats: Array<{
        date: string;
        count: number;
    }>;
}
export declare class AuditLog extends Model<AuditLogAttributes, AuditLogCreationAttributes> implements AuditLogAttributes {
    id: number;
    userId?: number;
    action: string;
    resource: string;
    resourceId?: number;
    oldData?: string;
    newData?: string;
    ipAddress?: string;
    userAgent?: string;
    sessionId?: string;
    status: 'success' | 'failed' | 'pending';
    errorMessage?: string;
    duration?: number;
    createdAt: Date;
    updatedAt: Date;
    readonly user?: User;
    static associations: {
        user: Association<AuditLog, User>;
    };
    static logAction(logData: AuditLogCreationAttributes): Promise<AuditLog>;
    static queryLogs(options: AuditLogQueryOptions): Promise<{
        logs: AuditLog[];
        total: number;
    }>;
    static getStats(userId?: number, days?: number): Promise<AuditLogStats>;
    static cleanupOldLogs(days?: number): Promise<number>;
    parseOldData(): any;
    parseNewData(): any;
}
export default AuditLog;
//# sourceMappingURL=AuditLog.d.ts.map