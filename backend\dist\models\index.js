"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.syncModels = exports.AuditLog = exports.RolePermission = exports.UserRole = exports.Permission = exports.Role = exports.Settings = exports.NotificationPreference = exports.Notification = exports.Media = exports.PostLike = exports.Post = exports.Comment = exports.Category = exports.ArticleTag = exports.Tag = exports.Article = exports.User = void 0;
const User_1 = require("./User");
Object.defineProperty(exports, "User", { enumerable: true, get: function () { return User_1.User; } });
const Article_1 = require("./Article");
Object.defineProperty(exports, "Article", { enumerable: true, get: function () { return Article_1.Article; } });
const Tag_1 = require("./Tag");
Object.defineProperty(exports, "Tag", { enumerable: true, get: function () { return Tag_1.Tag; } });
const ArticleTag_1 = require("./ArticleTag");
Object.defineProperty(exports, "ArticleTag", { enumerable: true, get: function () { return ArticleTag_1.ArticleTag; } });
const Category_1 = require("./Category");
Object.defineProperty(exports, "Category", { enumerable: true, get: function () { return Category_1.Category; } });
const Comment_1 = require("./Comment");
Object.defineProperty(exports, "Comment", { enumerable: true, get: function () { return Comment_1.Comment; } });
const Post_1 = require("./Post");
Object.defineProperty(exports, "Post", { enumerable: true, get: function () { return Post_1.Post; } });
const PostLike_1 = require("./PostLike");
Object.defineProperty(exports, "PostLike", { enumerable: true, get: function () { return PostLike_1.PostLike; } });
const Media_1 = require("./Media");
Object.defineProperty(exports, "Media", { enumerable: true, get: function () { return Media_1.Media; } });
const Notification_1 = require("./Notification");
Object.defineProperty(exports, "Notification", { enumerable: true, get: function () { return Notification_1.Notification; } });
const NotificationPreference_1 = require("./NotificationPreference");
Object.defineProperty(exports, "NotificationPreference", { enumerable: true, get: function () { return NotificationPreference_1.NotificationPreference; } });
const Settings_1 = require("./Settings");
Object.defineProperty(exports, "Settings", { enumerable: true, get: function () { return Settings_1.Settings; } });
const Role_1 = require("./Role");
Object.defineProperty(exports, "Role", { enumerable: true, get: function () { return Role_1.Role; } });
const Permission_1 = require("./Permission");
Object.defineProperty(exports, "Permission", { enumerable: true, get: function () { return Permission_1.Permission; } });
const UserRole_1 = require("./UserRole");
Object.defineProperty(exports, "UserRole", { enumerable: true, get: function () { return UserRole_1.UserRole; } });
const RolePermission_1 = require("./RolePermission");
Object.defineProperty(exports, "RolePermission", { enumerable: true, get: function () { return RolePermission_1.RolePermission; } });
const AuditLog_1 = require("./AuditLog");
Object.defineProperty(exports, "AuditLog", { enumerable: true, get: function () { return AuditLog_1.AuditLog; } });
User_1.User.hasMany(Article_1.Article, {
    foreignKey: 'authorId',
    as: 'articles'
});
Article_1.Article.belongsTo(User_1.User, {
    foreignKey: 'authorId',
    as: 'author'
});
Category_1.Category.hasMany(Article_1.Article, {
    foreignKey: 'categoryId',
    as: 'articles'
});
Article_1.Article.belongsTo(Category_1.Category, {
    foreignKey: 'categoryId',
    as: 'category'
});
Category_1.Category.hasMany(Category_1.Category, {
    foreignKey: 'parentId',
    as: 'children'
});
Category_1.Category.belongsTo(Category_1.Category, {
    foreignKey: 'parentId',
    as: 'parent'
});
Article_1.Article.belongsToMany(Tag_1.Tag, {
    through: ArticleTag_1.ArticleTag,
    foreignKey: 'articleId',
    otherKey: 'tagId',
    as: 'tags'
});
Tag_1.Tag.belongsToMany(Article_1.Article, {
    through: ArticleTag_1.ArticleTag,
    foreignKey: 'tagId',
    otherKey: 'articleId',
    as: 'articles'
});
User_1.User.hasMany(Comment_1.Comment, {
    foreignKey: 'authorId',
    as: 'comments'
});
Comment_1.Comment.belongsTo(User_1.User, {
    foreignKey: 'authorId',
    as: 'author'
});
Article_1.Article.hasMany(Comment_1.Comment, {
    foreignKey: 'articleId',
    as: 'comments'
});
Comment_1.Comment.belongsTo(Article_1.Article, {
    foreignKey: 'articleId',
    as: 'article'
});
Comment_1.Comment.hasMany(Comment_1.Comment, {
    foreignKey: 'parentId',
    as: 'replies'
});
Comment_1.Comment.belongsTo(Comment_1.Comment, {
    foreignKey: 'parentId',
    as: 'parent'
});
User_1.User.hasMany(Post_1.Post, {
    foreignKey: 'authorId',
    as: 'posts'
});
Post_1.Post.belongsTo(User_1.User, {
    foreignKey: 'authorId',
    as: 'author'
});
Post_1.Post.hasMany(Comment_1.Comment, {
    foreignKey: 'postId',
    as: 'comments'
});
Comment_1.Comment.belongsTo(Post_1.Post, {
    foreignKey: 'postId',
    as: 'post'
});
Post_1.Post.hasMany(PostLike_1.PostLike, {
    foreignKey: 'postId',
    as: 'likes'
});
PostLike_1.PostLike.belongsTo(Post_1.Post, {
    foreignKey: 'postId',
    as: 'post'
});
User_1.User.hasMany(PostLike_1.PostLike, {
    foreignKey: 'userId',
    as: 'postLikes'
});
PostLike_1.PostLike.belongsTo(User_1.User, {
    foreignKey: 'userId',
    as: 'user'
});
User_1.User.hasMany(Media_1.Media, {
    foreignKey: 'uploaderId',
    as: 'media'
});
Media_1.Media.belongsTo(User_1.User, {
    foreignKey: 'uploaderId',
    as: 'uploader'
});
User_1.User.hasMany(Notification_1.Notification, {
    foreignKey: 'recipientId',
    as: 'receivedNotifications'
});
User_1.User.hasMany(Notification_1.Notification, {
    foreignKey: 'senderId',
    as: 'sentNotifications'
});
Notification_1.Notification.belongsTo(User_1.User, {
    foreignKey: 'recipientId',
    as: 'recipient'
});
Notification_1.Notification.belongsTo(User_1.User, {
    foreignKey: 'senderId',
    as: 'sender'
});
User_1.User.hasMany(NotificationPreference_1.NotificationPreference, {
    foreignKey: 'userId',
    as: 'notificationPreferences'
});
NotificationPreference_1.NotificationPreference.belongsTo(User_1.User, {
    foreignKey: 'userId',
    as: 'user'
});
User_1.User.hasOne(Settings_1.Settings, {
    foreignKey: 'userId',
    as: 'settings'
});
Settings_1.Settings.belongsTo(User_1.User, {
    foreignKey: 'userId',
    as: 'user'
});
User_1.User.belongsToMany(Role_1.Role, {
    through: UserRole_1.UserRole,
    foreignKey: 'userId',
    otherKey: 'roleId',
    as: 'roles'
});
Role_1.Role.belongsToMany(User_1.User, {
    through: UserRole_1.UserRole,
    foreignKey: 'roleId',
    otherKey: 'userId',
    as: 'users'
});
Role_1.Role.belongsToMany(Permission_1.Permission, {
    through: RolePermission_1.RolePermission,
    foreignKey: 'roleId',
    otherKey: 'permissionId',
    as: 'permissions'
});
Permission_1.Permission.belongsToMany(Role_1.Role, {
    through: RolePermission_1.RolePermission,
    foreignKey: 'permissionId',
    otherKey: 'roleId',
    as: 'roles'
});
UserRole_1.UserRole.belongsTo(User_1.User, {
    foreignKey: 'userId',
    as: 'user'
});
UserRole_1.UserRole.belongsTo(Role_1.Role, {
    foreignKey: 'roleId',
    as: 'role'
});
UserRole_1.UserRole.belongsTo(User_1.User, {
    foreignKey: 'assignedBy',
    as: 'assigner'
});
RolePermission_1.RolePermission.belongsTo(Role_1.Role, {
    foreignKey: 'roleId',
    as: 'role'
});
RolePermission_1.RolePermission.belongsTo(Permission_1.Permission, {
    foreignKey: 'permissionId',
    as: 'permission'
});
RolePermission_1.RolePermission.belongsTo(User_1.User, {
    foreignKey: 'assignedBy',
    as: 'assigner'
});
User_1.User.hasMany(AuditLog_1.AuditLog, {
    foreignKey: 'userId',
    as: 'auditLogs'
});
AuditLog_1.AuditLog.belongsTo(User_1.User, {
    foreignKey: 'userId',
    as: 'user'
});
const syncModels = async (force = false) => {
    try {
        await User_1.User.sync({ force });
        await Settings_1.Settings.sync({ force });
        await Role_1.Role.sync({ force });
        await Permission_1.Permission.sync({ force });
        await UserRole_1.UserRole.sync({ force });
        await RolePermission_1.RolePermission.sync({ force });
        await Category_1.Category.sync({ force });
        await Tag_1.Tag.sync({ force });
        await Article_1.Article.sync({ force });
        await ArticleTag_1.ArticleTag.sync({ force });
        await Post_1.Post.sync({ force });
        await PostLike_1.PostLike.sync({ force });
        await Media_1.Media.sync({ force });
        await Notification_1.Notification.sync({ force });
        await NotificationPreference_1.NotificationPreference.sync({ force });
        await Comment_1.Comment.sync({ force });
        await AuditLog_1.AuditLog.sync({ force });
        console.log('All models synchronized successfully');
    }
    catch (error) {
        console.error('Error synchronizing models:', error);
        throw error;
    }
};
exports.syncModels = syncModels;
//# sourceMappingURL=index.js.map