"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditLog = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
const User_1 = require("./User");
class AuditLog extends sequelize_1.Model {
    static async logAction(logData) {
        try {
            if (logData.oldData && typeof logData.oldData === 'object') {
                logData.oldData = JSON.stringify(logData.oldData);
            }
            if (logData.newData && typeof logData.newData === 'object') {
                logData.newData = JSON.stringify(logData.newData);
            }
            return await this.create(logData);
        }
        catch (error) {
            console.error('Failed to create audit log:', error);
            throw error;
        }
    }
    static async queryLogs(options) {
        const { userId, action, resource, resourceId, status, startDate, endDate, ipAddress, page = 1, limit = 20, orderBy = 'createdAt', orderDirection = 'DESC' } = options;
        const where = {};
        if (userId !== undefined)
            where.userId = userId;
        if (action)
            where.action = action;
        if (resource)
            where.resource = resource;
        if (resourceId !== undefined)
            where.resourceId = resourceId;
        if (status)
            where.status = status;
        if (ipAddress)
            where.ipAddress = ipAddress;
        if (startDate || endDate) {
            where.createdAt = {};
            if (startDate)
                where.createdAt.gte = startDate;
            if (endDate)
                where.createdAt.lte = endDate;
        }
        const offset = (page - 1) * limit;
        const { rows: logs, count: total } = await this.findAndCountAll({
            where,
            include: [
                {
                    model: User_1.User,
                    as: 'user',
                    attributes: ['id', 'username', 'email'],
                    required: false
                }
            ],
            order: [[orderBy, orderDirection]],
            limit,
            offset
        });
        return { logs, total };
    }
    static async getStats(userId, days = 30) {
        const where = {};
        if (userId !== undefined)
            where.userId = userId;
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        where.createdAt = { gte: startDate };
        const totalLogs = await this.count({ where });
        const successLogs = await this.count({ where: { ...where, status: 'success' } });
        const failedLogs = await this.count({ where: { ...where, status: 'failed' } });
        const actionStats = {};
        const actions = await this.findAll({
            where,
            attributes: ['action'],
            group: ['action'],
            raw: true
        });
        const resourceStats = {};
        const resources = await this.findAll({
            where,
            attributes: ['resource'],
            group: ['resource'],
            raw: true
        });
        const dailyStats = [];
        return {
            totalLogs,
            successLogs,
            failedLogs,
            actionStats,
            resourceStats,
            dailyStats
        };
    }
    static async cleanupOldLogs(days = 90) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);
        const deletedCount = await this.destroy({
            where: {
                createdAt: {
                    lt: cutoffDate
                }
            }
        });
        return deletedCount;
    }
    parseOldData() {
        if (!this.oldData)
            return null;
        try {
            return JSON.parse(this.oldData);
        }
        catch {
            return null;
        }
    }
    parseNewData() {
        if (!this.newData)
            return null;
        try {
            return JSON.parse(this.newData);
        }
        catch {
            return null;
        }
    }
}
exports.AuditLog = AuditLog;
AuditLog.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
    },
    userId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        field: 'user_id'
    },
    action: {
        type: sequelize_1.DataTypes.STRING(50),
        allowNull: false,
        validate: {
            notEmpty: true,
            len: [1, 50]
        }
    },
    resource: {
        type: sequelize_1.DataTypes.STRING(50),
        allowNull: false,
        validate: {
            notEmpty: true,
            len: [1, 50]
        }
    },
    resourceId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        field: 'resource_id'
    },
    oldData: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
        field: 'old_data'
    },
    newData: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
        field: 'new_data'
    },
    ipAddress: {
        type: sequelize_1.DataTypes.STRING(45),
        allowNull: true,
        field: 'ip_address'
    },
    userAgent: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
        field: 'user_agent'
    },
    sessionId: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: true,
        field: 'session_id'
    },
    status: {
        type: sequelize_1.DataTypes.ENUM('success', 'failed', 'pending'),
        allowNull: false,
        defaultValue: 'success'
    },
    errorMessage: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
        field: 'error_message'
    },
    duration: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        validate: {
            min: 0
        }
    },
    createdAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false
    }
}, {
    sequelize: database_1.sequelize,
    modelName: 'AuditLog',
    tableName: 'audit_logs',
    timestamps: true,
    underscored: true,
    indexes: [
        {
            fields: ['user_id']
        },
        {
            fields: ['action']
        },
        {
            fields: ['resource']
        },
        {
            fields: ['resource_id']
        },
        {
            fields: ['created_at']
        },
        {
            fields: ['status']
        },
        {
            fields: ['user_id', 'created_at']
        },
        {
            fields: ['resource', 'resource_id']
        },
        {
            fields: ['action', 'resource']
        }
    ]
});
exports.default = AuditLog;
//# sourceMappingURL=AuditLog.js.map