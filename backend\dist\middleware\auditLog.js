"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.auditPermissionGrant = exports.auditRoleAssign = exports.auditLogout = exports.auditLogin = exports.auditArticleDelete = exports.auditArticleUpdate = exports.auditArticleCreate = exports.auditUserDelete = exports.auditUserUpdate = exports.auditUserCreate = exports.createAuditLogMiddleware = void 0;
const AuditLog_1 = require("../models/AuditLog");
const getClientIP = (req) => {
    const forwardedFor = req.headers['x-forwarded-for'];
    const realIP = req.headers['x-real-ip'];
    if (typeof forwardedFor === 'string') {
        return forwardedFor.split(',')[0]?.trim() || 'unknown';
    }
    if (Array.isArray(forwardedFor) && forwardedFor[0]) {
        return forwardedFor[0];
    }
    if (typeof realIP === 'string') {
        return realIP;
    }
    return req.socket?.remoteAddress || req.ip || 'unknown';
};
const getSessionId = (req) => {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
        return authHeader.substring(7, 47);
    }
    return undefined;
};
const createAuditLogMiddleware = (config) => {
    return async (req, res, next) => {
        try {
            if (config.skipCondition && config.skipCondition(req)) {
                return next();
            }
            const startTime = Date.now();
            req.auditData = {
                action: config.action,
                resource: config.resource,
                resourceId: config.getResourceId ? config.getResourceId(req) : undefined,
                startTime
            };
            if (config.captureOldData && req.auditData.resourceId) {
                try {
                    req.auditData.oldData = await getResourceData(config.resource, req.auditData.resourceId);
                }
                catch (error) {
                    console.warn('Failed to capture old data:', error);
                }
            }
            const originalJson = res.json.bind(res);
            res.json = function (data) {
                setImmediate(async () => {
                    try {
                        await recordAuditLog(req, res, config, data);
                    }
                    catch (error) {
                        console.error('Failed to record audit log:', error);
                    }
                });
                return originalJson(data);
            };
            next();
        }
        catch (error) {
            console.error('Audit middleware error:', error);
            next();
        }
    };
};
exports.createAuditLogMiddleware = createAuditLogMiddleware;
const recordAuditLog = async (req, res, config, responseData) => {
    try {
        const endTime = Date.now();
        const duration = req.auditData ? endTime - req.auditData.startTime : 0;
        const status = res.statusCode >= 200 && res.statusCode < 300 ? 'success' : 'failed';
        let newData;
        if (config.captureNewData && responseData && status === 'success') {
            try {
                if (responseData.data) {
                    newData = JSON.stringify(responseData.data);
                }
                else if (typeof responseData === 'object') {
                    newData = JSON.stringify(responseData);
                }
            }
            catch (error) {
                console.warn('Failed to serialize new data:', error);
            }
        }
        let errorMessage;
        if (status === 'failed') {
            if (responseData && responseData.error && responseData.error.message) {
                errorMessage = responseData.error.message;
            }
            else if (responseData && responseData.message) {
                errorMessage = responseData.message;
            }
            else {
                errorMessage = `HTTP ${res.statusCode}`;
            }
        }
        await AuditLog_1.AuditLog.logAction({
            userId: req.user?.id,
            action: req.auditData?.action || config.action,
            resource: req.auditData?.resource || config.resource,
            resourceId: req.auditData?.resourceId,
            oldData: req.auditData?.oldData ? JSON.stringify(req.auditData.oldData) : undefined,
            newData,
            ipAddress: getClientIP(req),
            userAgent: req.headers['user-agent'],
            sessionId: getSessionId(req),
            status,
            errorMessage,
            duration
        });
    }
    catch (error) {
        console.error('Failed to record audit log:', error);
    }
};
const getResourceData = async (resource, resourceId) => {
    try {
        const models = await Promise.resolve().then(() => __importStar(require('../models')));
        switch (resource.toLowerCase()) {
            case 'user':
                const user = await models.User.findByPk(resourceId, {
                    attributes: { exclude: ['passwordHash'] }
                });
                return user?.toJSON();
            case 'article':
                const article = await models.Article.findByPk(resourceId);
                return article?.toJSON();
            case 'post':
                const post = await models.Post.findByPk(resourceId);
                return post?.toJSON();
            case 'comment':
                const comment = await models.Comment.findByPk(resourceId);
                return comment?.toJSON();
            case 'category':
                const category = await models.Category.findByPk(resourceId);
                return category?.toJSON();
            case 'tag':
                const tag = await models.Tag.findByPk(resourceId);
                return tag?.toJSON();
            case 'role':
                const role = await models.Role.findByPk(resourceId);
                return role?.toJSON();
            case 'permission':
                const permission = await models.Permission.findByPk(resourceId);
                return permission?.toJSON();
            default:
                return null;
        }
    }
    catch (error) {
        console.warn(`Failed to get ${resource} data:`, error);
        return null;
    }
};
exports.auditUserCreate = (0, exports.createAuditLogMiddleware)({
    action: 'CREATE',
    resource: 'user',
    captureNewData: true
});
exports.auditUserUpdate = (0, exports.createAuditLogMiddleware)({
    action: 'UPDATE',
    resource: 'user',
    getResourceId: (req) => parseInt(req.params.id),
    captureOldData: true,
    captureNewData: true
});
exports.auditUserDelete = (0, exports.createAuditLogMiddleware)({
    action: 'DELETE',
    resource: 'user',
    getResourceId: (req) => parseInt(req.params.id),
    captureOldData: true
});
exports.auditArticleCreate = (0, exports.createAuditLogMiddleware)({
    action: 'CREATE',
    resource: 'article',
    captureNewData: true
});
exports.auditArticleUpdate = (0, exports.createAuditLogMiddleware)({
    action: 'UPDATE',
    resource: 'article',
    getResourceId: (req) => parseInt(req.params.id),
    captureOldData: true,
    captureNewData: true
});
exports.auditArticleDelete = (0, exports.createAuditLogMiddleware)({
    action: 'DELETE',
    resource: 'article',
    getResourceId: (req) => parseInt(req.params.id),
    captureOldData: true
});
exports.auditLogin = (0, exports.createAuditLogMiddleware)({
    action: 'LOGIN',
    resource: 'auth',
    captureNewData: false
});
exports.auditLogout = (0, exports.createAuditLogMiddleware)({
    action: 'LOGOUT',
    resource: 'auth',
    captureNewData: false
});
exports.auditRoleAssign = (0, exports.createAuditLogMiddleware)({
    action: 'ASSIGN_ROLE',
    resource: 'user_role',
    getResourceId: (req) => parseInt(req.params.userId),
    captureOldData: true,
    captureNewData: true
});
exports.auditPermissionGrant = (0, exports.createAuditLogMiddleware)({
    action: 'GRANT_PERMISSION',
    resource: 'role_permission',
    getResourceId: (req) => parseInt(req.params.roleId),
    captureOldData: true,
    captureNewData: true
});
exports.default = {
    createAuditLogMiddleware: exports.createAuditLogMiddleware,
    auditUserCreate: exports.auditUserCreate,
    auditUserUpdate: exports.auditUserUpdate,
    auditUserDelete: exports.auditUserDelete,
    auditArticleCreate: exports.auditArticleCreate,
    auditArticleUpdate: exports.auditArticleUpdate,
    auditArticleDelete: exports.auditArticleDelete,
    auditLogin: exports.auditLogin,
    auditLogout: exports.auditLogout,
    auditRoleAssign: exports.auditRoleAssign,
    auditPermissionGrant: exports.auditPermissionGrant
};
//# sourceMappingURL=auditLog.js.map