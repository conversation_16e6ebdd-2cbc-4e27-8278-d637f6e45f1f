import { Model, Optional } from 'sequelize';
export interface UserAttributes {
    id: number;
    username: string;
    email: string;
    passwordHash: string;
    isActive: boolean;
    emailVerified: boolean;
    emailVerifiedAt?: Date;
    lastLoginAt?: Date;
    passwordResetToken?: string;
    passwordResetExpires?: Date;
    emailVerificationToken?: string;
    emailVerificationExpires?: Date;
    createdAt: Date;
    updatedAt: Date;
}
export interface UserCreationAttributes extends Optional<UserAttributes, 'id' | 'createdAt' | 'updatedAt'> {
}
export declare class User extends Model<UserAttributes, UserCreationAttributes> implements UserAttributes {
    id: number;
    username: string;
    email: string;
    passwordHash: string;
    isActive: boolean;
    emailVerified: boolean;
    emailVerifiedAt?: Date;
    lastLoginAt?: Date;
    passwordResetToken?: string;
    passwordResetExpires?: Date;
    emailVerificationToken?: string;
    emailVerificationExpires?: Date;
    createdAt: Date;
    updatedAt: Date;
    validatePassword(password: string): Promise<boolean>;
    toJSON(): Omit<UserAttributes, 'passwordHash'>;
    static hashPassword(password: string): Promise<string>;
    static findByUsername(username: string): Promise<User | null>;
    static findByEmail(email: string): Promise<User | null>;
}
//# sourceMappingURL=User.d.ts.map