"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerificationService = exports.VerificationType = void 0;
const crypto_1 = __importDefault(require("crypto"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
var VerificationType;
(function (VerificationType) {
    VerificationType["EMAIL_VERIFICATION"] = "email_verification";
    VerificationType["PASSWORD_RESET"] = "password_reset";
    VerificationType["TWO_FACTOR_AUTH"] = "two_factor_auth";
})(VerificationType || (exports.VerificationType = VerificationType = {}));
class VerificationService {
    static generateVerificationToken(payload) {
        const secret = process.env.JWT_SECRET;
        if (!secret) {
            throw new Error('JWT_SECRET is not defined');
        }
        return jsonwebtoken_1.default.sign(payload, secret, {
            expiresIn: '24h'
        });
    }
    static verifyVerificationToken(token) {
        const secret = process.env.JWT_SECRET;
        if (!secret) {
            throw new Error('JWT_SECRET is not defined');
        }
        return jsonwebtoken_1.default.verify(token, secret);
    }
    static generateEmailVerificationToken(email, userId) {
        const payload = {
            email,
            userId: userId,
            type: VerificationType.EMAIL_VERIFICATION,
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
        };
        return this.generateVerificationToken(payload);
    }
    static generatePasswordResetToken(email, userId) {
        const payload = {
            email,
            userId,
            type: VerificationType.PASSWORD_RESET,
            expiresAt: new Date(Date.now() + 60 * 60 * 1000)
        };
        return this.generateVerificationToken(payload);
    }
    static generateTwoFactorCode() {
        return crypto_1.default.randomInt(100000, 999999).toString();
    }
    static generateSecureToken(length = 32) {
        return crypto_1.default.randomBytes(length).toString('hex');
    }
    static verifyToken(token) {
        try {
            const payload = this.verifyVerificationToken(token);
            if (new Date() > new Date(payload.expiresAt)) {
                return {
                    valid: false,
                    error: 'Token has expired'
                };
            }
            return {
                valid: true,
                data: payload
            };
        }
        catch (error) {
            return {
                valid: false,
                error: error instanceof Error ? error.message : 'Invalid token'
            };
        }
    }
    static verifyEmailVerificationToken(token) {
        const result = this.verifyToken(token);
        if (!result.valid || !result.data) {
            return result;
        }
        if (result.data.type !== VerificationType.EMAIL_VERIFICATION) {
            return {
                valid: false,
                error: 'Invalid token type'
            };
        }
        return result;
    }
    static verifyPasswordResetToken(token) {
        const result = this.verifyToken(token);
        if (!result.valid || !result.data) {
            return result;
        }
        if (result.data.type !== VerificationType.PASSWORD_RESET) {
            return {
                valid: false,
                error: 'Invalid token type'
            };
        }
        return result;
    }
    static hashVerificationCode(code) {
        return crypto_1.default.createHash('sha256').update(code).digest('hex');
    }
    static verifyCode(code, hashedCode) {
        const inputHash = this.hashVerificationCode(code);
        return crypto_1.default.timingSafeEqual(Buffer.from(inputHash), Buffer.from(hashedCode));
    }
    static isTokenExpiringSoon(token) {
        try {
            const payload = this.verifyVerificationToken(token);
            const expiresAt = new Date(payload.expiresAt);
            const now = new Date();
            const fifteenMinutesFromNow = new Date(now.getTime() + 15 * 60 * 1000);
            return expiresAt <= fifteenMinutesFromNow;
        }
        catch {
            return true;
        }
    }
    static generateUniqueId() {
        const timestamp = Date.now().toString(36);
        const randomPart = crypto_1.default.randomBytes(8).toString('hex');
        return `${timestamp}-${randomPart}`;
    }
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    static validatePasswordStrength(password) {
        const errors = [];
        if (password.length < 8) {
            errors.push('密码长度至少8位');
        }
        if (password.length > 128) {
            errors.push('密码长度不能超过128位');
        }
        if (!/[a-z]/.test(password)) {
            errors.push('密码必须包含小写字母');
        }
        if (!/[A-Z]/.test(password)) {
            errors.push('密码必须包含大写字母');
        }
        if (!/\d/.test(password)) {
            errors.push('密码必须包含数字');
        }
        if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
            errors.push('密码必须包含特殊字符');
        }
        return {
            valid: errors.length === 0,
            errors
        };
    }
    static validateUsername(username) {
        const errors = [];
        if (username.length < 3) {
            errors.push('用户名长度至少3位');
        }
        if (username.length > 50) {
            errors.push('用户名长度不能超过50位');
        }
        if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
            errors.push('用户名只能包含字母、数字、下划线和连字符');
        }
        if (/^[0-9]/.test(username)) {
            errors.push('用户名不能以数字开头');
        }
        const reservedNames = [
            'admin', 'administrator', 'root', 'system', 'api', 'www', 'mail',
            'ftp', 'blog', 'test', 'demo', 'guest', 'null', 'undefined'
        ];
        if (reservedNames.includes(username.toLowerCase())) {
            errors.push('该用户名为系统保留，请选择其他用户名');
        }
        return {
            valid: errors.length === 0,
            errors
        };
    }
    static generateTemporaryPassword(length = 12) {
        const lowercase = 'abcdefghijklmnopqrstuvwxyz';
        const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const numbers = '0123456789';
        const symbols = '!@#$%^&*';
        const allChars = lowercase + uppercase + numbers + symbols;
        let password = '';
        password += lowercase[crypto_1.default.randomInt(lowercase.length)];
        password += uppercase[crypto_1.default.randomInt(uppercase.length)];
        password += numbers[crypto_1.default.randomInt(numbers.length)];
        password += symbols[crypto_1.default.randomInt(symbols.length)];
        for (let i = 4; i < length; i++) {
            password += allChars[crypto_1.default.randomInt(allChars.length)];
        }
        return password.split('').sort(() => crypto_1.default.randomInt(3) - 1).join('');
    }
}
exports.VerificationService = VerificationService;
//# sourceMappingURL=verification.js.map