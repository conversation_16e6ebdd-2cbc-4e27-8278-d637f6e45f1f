"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.clearRateLimitStore = exports.dynamicRateLimit = exports.adminRateLimit = exports.strictUserRateLimit = exports.createUserBasedRateLimit = exports.searchRateLimit = exports.commentRateLimit = exports.uploadRateLimit = exports.emailRateLimit = exports.passwordResetRateLimit = exports.registrationRateLimit = exports.authRateLimit = exports.generalRateLimit = void 0;
exports.createRateLimit = createRateLimit;
const errorHandler_1 = require("./errorHandler");
class RateLimitStore {
    constructor() {
        this.store = new Map();
        this.cleanupInterval = setInterval(() => {
            this.cleanup();
        }, 60 * 1000);
    }
    get(key) {
        return this.store.get(key);
    }
    set(key, record) {
        this.store.set(key, record);
    }
    increment(key, windowMs) {
        const now = Date.now();
        const existing = this.store.get(key);
        if (!existing || now > existing.resetTime) {
            const record = {
                count: 1,
                resetTime: now + windowMs,
                firstRequest: now
            };
            this.store.set(key, record);
            return record;
        }
        existing.count++;
        return existing;
    }
    cleanup() {
        const now = Date.now();
        for (const [key, record] of this.store.entries()) {
            if (now > record.resetTime) {
                this.store.delete(key);
            }
        }
    }
    destroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        this.store.clear();
    }
}
const globalStore = new RateLimitStore();
function createRateLimit(config) {
    const { windowMs, maxRequests, message = '请求过于频繁，请稍后再试', skipSuccessfulRequests = false, skipFailedRequests = false, keyGenerator = (req) => req.ip || req.connection.remoteAddress || 'unknown' } = config;
    return (req, res, next) => {
        const key = keyGenerator(req);
        const record = globalStore.increment(key, windowMs);
        res.set({
            'X-RateLimit-Limit': maxRequests.toString(),
            'X-RateLimit-Remaining': Math.max(0, maxRequests - record.count).toString(),
            'X-RateLimit-Reset': new Date(record.resetTime).toISOString()
        });
        if (record.count > maxRequests) {
            throw (0, errorHandler_1.createError)(429, message, 'RATE_LIMIT_EXCEEDED');
        }
        if (skipSuccessfulRequests || skipFailedRequests) {
            const originalSend = res.send;
            res.send = function (body) {
                const statusCode = res.statusCode;
                const shouldSkip = (skipSuccessfulRequests && statusCode < 400) ||
                    (skipFailedRequests && statusCode >= 400);
                if (shouldSkip && record.count > 0) {
                    record.count--;
                }
                return originalSend.call(this, body);
            };
        }
        next();
    };
}
exports.generalRateLimit = createRateLimit({
    windowMs: 60 * 1000,
    maxRequests: 100,
    message: 'API请求过于频繁，请稍后再试'
});
exports.authRateLimit = createRateLimit({
    windowMs: 15 * 60 * 1000,
    maxRequests: 5,
    message: '登录尝试过于频繁，请15分钟后再试',
    skipSuccessfulRequests: true
});
exports.registrationRateLimit = createRateLimit({
    windowMs: 60 * 60 * 1000,
    maxRequests: 3,
    message: '注册尝试过于频繁，请1小时后再试'
});
exports.passwordResetRateLimit = createRateLimit({
    windowMs: 60 * 60 * 1000,
    maxRequests: 3,
    message: '密码重置请求过于频繁，请1小时后再试'
});
exports.emailRateLimit = createRateLimit({
    windowMs: 60 * 60 * 1000,
    maxRequests: 5,
    message: '邮件发送过于频繁，请1小时后再试'
});
exports.uploadRateLimit = createRateLimit({
    windowMs: 60 * 1000,
    maxRequests: 10,
    message: '文件上传过于频繁，请稍后再试'
});
exports.commentRateLimit = createRateLimit({
    windowMs: 60 * 1000,
    maxRequests: 5,
    message: '评论发布过于频繁，请稍后再试'
});
exports.searchRateLimit = createRateLimit({
    windowMs: 60 * 1000,
    maxRequests: 30,
    message: '搜索请求过于频繁，请稍后再试'
});
const createUserBasedRateLimit = (config) => {
    return createRateLimit({
        ...config,
        keyGenerator: (req) => {
            const user = req.user;
            return user ? `user:${user.id}` : `ip:${req.ip || 'unknown'}`;
        }
    });
};
exports.createUserBasedRateLimit = createUserBasedRateLimit;
exports.strictUserRateLimit = (0, exports.createUserBasedRateLimit)({
    windowMs: 60 * 1000,
    maxRequests: 20,
    message: '操作过于频繁，请稍后再试'
});
exports.adminRateLimit = createRateLimit({
    windowMs: 60 * 1000,
    maxRequests: 50,
    message: '管理操作过于频繁，请稍后再试',
    keyGenerator: (req) => {
        const user = req.user;
        return user ? `admin:${user.id}` : `ip:${req.ip || 'unknown'}`;
    }
});
const dynamicRateLimit = (req, res, next) => {
    const user = req.user;
    if (!user) {
        return (0, exports.generalRateLimit)(req, res, next);
    }
    const isAdmin = user.roles?.some((role) => role.name === 'admin');
    const isPremium = user.roles?.some((role) => role.name === 'premium');
    if (isAdmin) {
        return (0, exports.adminRateLimit)(req, res, next);
    }
    else if (isPremium) {
        return (0, exports.createUserBasedRateLimit)({
            windowMs: 60 * 1000,
            maxRequests: 100,
            message: '操作过于频繁，请稍后再试'
        })(req, res, next);
    }
    else {
        return (0, exports.strictUserRateLimit)(req, res, next);
    }
};
exports.dynamicRateLimit = dynamicRateLimit;
const clearRateLimitStore = () => {
    globalStore.destroy();
};
exports.clearRateLimitStore = clearRateLimitStore;
//# sourceMappingURL=rateLimit.js.map