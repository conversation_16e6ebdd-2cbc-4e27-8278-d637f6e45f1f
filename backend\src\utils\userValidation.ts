import { User } from '../models'
import { VerificationService } from './verification'

/**
 * 用户验证结果接口
 */
interface ValidationResult {
  valid: boolean
  errors: string[]
}

/**
 * 用户可用性检查结果接口
 */
interface AvailabilityResult {
  available: boolean
  message?: string
}

/**
 * 用户验证工具类
 * 提供用户相关的验证功能
 */
export class UserValidationService {
  /**
   * 验证用户注册数据
   */
  static async validateRegistrationData(data: {
    username: string
    email: string
    password: string
    confirmPassword?: string
  }): Promise<ValidationResult> {
    const errors: string[] = []

    // 验证用户名
    const usernameValidation = VerificationService.validateUsername(data.username)
    if (!usernameValidation.valid) {
      errors.push(...usernameValidation.errors)
    }

    // 验证邮箱格式
    if (!VerificationService.isValidEmail(data.email)) {
      errors.push('邮箱格式无效')
    }

    // 验证密码强度
    const passwordValidation = VerificationService.validatePasswordStrength(data.password)
    if (!passwordValidation.valid) {
      errors.push(...passwordValidation.errors)
    }

    // 验证密码确认
    if (data.confirmPassword && data.password !== data.confirmPassword) {
      errors.push('两次输入的密码不一致')
    }

    // 检查用户名是否已存在
    const usernameAvailability = await this.checkUsernameAvailability(data.username)
    if (!usernameAvailability.available) {
      errors.push(usernameAvailability.message || '用户名已存在')
    }

    // 检查邮箱是否已存在
    const emailAvailability = await this.checkEmailAvailability(data.email)
    if (!emailAvailability.available) {
      errors.push(emailAvailability.message || '邮箱已被注册')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 检查用户名可用性
   */
  static async checkUsernameAvailability(username: string): Promise<AvailabilityResult> {
    try {
      // 首先验证用户名格式
      const validation = VerificationService.validateUsername(username)
      if (!validation.valid) {
        return {
          available: false,
          message: validation.errors[0] || '用户名格式无效'
        }
      }

      // 检查数据库中是否已存在
      const existingUser = await User.findByUsername(username)
      if (existingUser) {
        return {
          available: false,
          message: '用户名已存在'
        }
      }

      return {
        available: true
      }
    } catch (error) {
      console.error('检查用户名可用性失败:', error)
      return {
        available: false,
        message: '检查用户名可用性时发生错误'
      }
    }
  }

  /**
   * 检查邮箱可用性
   */
  static async checkEmailAvailability(email: string): Promise<AvailabilityResult> {
    try {
      // 首先验证邮箱格式
      if (!VerificationService.isValidEmail(email)) {
        return {
          available: false,
          message: '邮箱格式无效'
        }
      }

      // 检查数据库中是否已存在
      const existingUser = await User.findByEmail(email)
      if (existingUser) {
        return {
          available: false,
          message: '邮箱已被注册'
        }
      }

      return {
        available: true
      }
    } catch (error) {
      console.error('检查邮箱可用性失败:', error)
      return {
        available: false,
        message: '检查邮箱可用性时发生错误'
      }
    }
  }

  /**
   * 验证用户登录数据
   */
  static validateLoginData(data: {
    username: string
    password: string
  }): ValidationResult {
    const errors: string[] = []

    if (!data.username || data.username.trim().length === 0) {
      errors.push('用户名不能为空')
    }

    if (!data.password || data.password.length === 0) {
      errors.push('密码不能为空')
    }

    if (data.username && data.username.length > 50) {
      errors.push('用户名长度不能超过50位')
    }

    if (data.password && data.password.length > 128) {
      errors.push('密码长度不能超过128位')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 验证密码重置数据
   */
  static validatePasswordResetData(data: {
    password: string
    confirmPassword: string
  }): ValidationResult {
    const errors: string[] = []

    // 验证密码强度
    const passwordValidation = VerificationService.validatePasswordStrength(data.password)
    if (!passwordValidation.valid) {
      errors.push(...passwordValidation.errors)
    }

    // 验证密码确认
    if (data.password !== data.confirmPassword) {
      errors.push('两次输入的密码不一致')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 验证用户更新数据
   */
  static async validateUserUpdateData(
    userId: number,
    data: {
      username?: string
      email?: string
      password?: string
      confirmPassword?: string
    }
  ): Promise<ValidationResult> {
    const errors: string[] = []

    // 验证用户名（如果提供）
    if (data.username !== undefined) {
      const usernameValidation = VerificationService.validateUsername(data.username)
      if (!usernameValidation.valid) {
        errors.push(...usernameValidation.errors)
      } else {
        // 检查用户名是否被其他用户使用
        const existingUser = await User.findByUsername(data.username)
        if (existingUser && existingUser.id !== userId) {
          errors.push('用户名已被其他用户使用')
        }
      }
    }

    // 验证邮箱（如果提供）
    if (data.email !== undefined) {
      if (!VerificationService.isValidEmail(data.email)) {
        errors.push('邮箱格式无效')
      } else {
        // 检查邮箱是否被其他用户使用
        const existingUser = await User.findByEmail(data.email)
        if (existingUser && existingUser.id !== userId) {
          errors.push('邮箱已被其他用户使用')
        }
      }
    }

    // 验证密码（如果提供）
    if (data.password !== undefined) {
      const passwordValidation = VerificationService.validatePasswordStrength(data.password)
      if (!passwordValidation.valid) {
        errors.push(...passwordValidation.errors)
      }

      // 验证密码确认
      if (data.confirmPassword && data.password !== data.confirmPassword) {
        errors.push('两次输入的密码不一致')
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 验证邮箱验证请求
   */
  static validateEmailVerificationRequest(email: string): ValidationResult {
    const errors: string[] = []

    if (!email || email.trim().length === 0) {
      errors.push('邮箱不能为空')
    } else if (!VerificationService.isValidEmail(email)) {
      errors.push('邮箱格式无效')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 验证密码重置请求
   */
  static validatePasswordResetRequest(email: string): ValidationResult {
    return this.validateEmailVerificationRequest(email)
  }

  /**
   * 检查用户是否可以执行敏感操作
   */
  static async canPerformSensitiveOperation(userId: number): Promise<{
    allowed: boolean
    reason?: string
  }> {
    try {
      const user = await User.findByPk(userId)
      if (!user) {
        return {
          allowed: false,
          reason: '用户不存在'
        }
      }

      // 检查用户是否被禁用
      if (!user.isActive) {
        return {
          allowed: false,
          reason: '用户账户已被禁用'
        }
      }

      // 检查邮箱是否已验证
      if (!user.emailVerified) {
        return {
          allowed: false,
          reason: '请先验证邮箱地址'
        }
      }

      return {
        allowed: true
      }
    } catch (error) {
      console.error('检查用户权限失败:', error)
      return {
        allowed: false,
        reason: '检查用户权限时发生错误'
      }
    }
  }

  /**
   * 生成用户显示名称建议
   */
  static generateDisplayNameSuggestions(username: string): string[] {
    const suggestions: string[] = []

    // 基于用户名的建议
    suggestions.push(username)
    suggestions.push(username.charAt(0).toUpperCase() + username.slice(1))

    // 添加一些变体
    if (username.length > 3) {
      suggestions.push(username.substring(0, 3) + '***')
    }

    return suggestions.slice(0, 3) // 返回最多3个建议
  }

  /**
   * 检查用户名是否包含敏感词
   */
  static containsProfanity(username: string): boolean {
    const profanityList = [
      'admin', 'administrator', 'moderator', 'system', 'root',
      'fuck', 'shit', 'damn', 'hell', 'ass', 'bitch'
      // 可以根据需要添加更多敏感词
    ]

    const lowerUsername = username.toLowerCase()
    return profanityList.some(word => lowerUsername.includes(word))
  }
}
