"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../controllers/auth");
const auth_2 = require("../middleware/auth");
const registration_1 = require("../middleware/registration");
const rateLimit_1 = require("../middleware/rateLimit");
const router = (0, express_1.Router)();
router.post('/login', rateLimit_1.authRateLimit, ...registration_1.loginMiddleware, auth_1.login);
router.post('/logout', auth_1.logout);
router.get('/profile', auth_2.authenticateToken, auth_1.getProfile);
router.post('/refresh', auth_1.refreshToken);
router.post('/validate', auth_1.validateToken);
router.post('/register', ...registration_1.registrationMiddleware, auth_1.register);
router.post('/verify-email', ...registration_1.emailVerificationMiddleware, auth_1.verifyEmail);
router.post('/resend-verification', rateLimit_1.emailRateLimit, auth_1.resendVerificationEmail);
router.post('/request-password-reset', rateLimit_1.passwordResetRateLimit, ...registration_1.passwordResetRequestMiddleware, auth_1.requestPasswordReset);
router.post('/reset-password', ...registration_1.passwordResetMiddleware, auth_1.resetPassword);
exports.default = router;
//# sourceMappingURL=auth.js.map