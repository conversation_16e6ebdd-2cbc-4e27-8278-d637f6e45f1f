"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Settings = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
class Settings extends sequelize_1.Model {
    static async findByUserId(userId) {
        return this.findOne({ where: { userId } });
    }
    static async upsertByUserId(userId, settingsData) {
        const upsertData = {
            userId,
            ...settingsData
        };
        const [settings] = await this.upsert(upsertData);
        return settings;
    }
    static getDefaultSettings() {
        return {
            theme: 'auto',
            language: 'zh-CN',
            timezone: 'Asia/Shanghai',
            itemsPerPage: 10,
            emailNotifications: true,
            commentNotifications: true,
            systemNotifications: true,
            profileVisibility: 'public',
            defaultPostVisibility: 'public',
            showEmail: false,
            twoFactorEnabled: false
        };
    }
    static validateSettings(settingsData) {
        const errors = [];
        if (settingsData.theme && !['light', 'dark', 'auto'].includes(settingsData.theme)) {
            errors.push('主题设置必须是 light、dark 或 auto');
        }
        if (settingsData.itemsPerPage && (settingsData.itemsPerPage < 5 || settingsData.itemsPerPage > 100)) {
            errors.push('每页显示数量必须在 5-100 之间');
        }
        if (settingsData.profileVisibility && !['public', 'private'].includes(settingsData.profileVisibility)) {
            errors.push('个人资料可见性必须是 public 或 private');
        }
        if (settingsData.defaultPostVisibility && !['public', 'private'].includes(settingsData.defaultPostVisibility)) {
            errors.push('默认文章可见性必须是 public 或 private');
        }
        if (settingsData.website && settingsData.website.trim()) {
            try {
                new URL(settingsData.website);
            }
            catch {
                errors.push('网站URL格式不正确');
            }
        }
        return {
            valid: errors.length === 0,
            errors
        };
    }
    toJSON() {
        const values = { ...this.get() };
        return values;
    }
}
exports.Settings = Settings;
Settings.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
    },
    userId: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        unique: true,
        field: 'user_id',
        references: {
            model: 'users',
            key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
    },
    displayName: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
        field: 'display_name',
        validate: {
            len: [1, 100]
        }
    },
    avatar: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
        validate: {
            isUrl: true
        }
    },
    bio: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
        validate: {
            len: [0, 500]
        }
    },
    website: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: true,
        validate: {
            isUrl: true
        }
    },
    location: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
        validate: {
            len: [1, 100]
        }
    },
    theme: {
        type: sequelize_1.DataTypes.ENUM('light', 'dark', 'auto'),
        allowNull: false,
        defaultValue: 'auto'
    },
    language: {
        type: sequelize_1.DataTypes.STRING(10),
        allowNull: false,
        defaultValue: 'zh-CN',
        validate: {
            len: [2, 10]
        }
    },
    timezone: {
        type: sequelize_1.DataTypes.STRING(50),
        allowNull: false,
        defaultValue: 'Asia/Shanghai',
        validate: {
            len: [1, 50]
        }
    },
    itemsPerPage: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 10,
        field: 'items_per_page',
        validate: {
            min: 5,
            max: 100
        }
    },
    emailNotifications: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        field: 'email_notifications'
    },
    commentNotifications: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        field: 'comment_notifications'
    },
    systemNotifications: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        field: 'system_notifications'
    },
    profileVisibility: {
        type: sequelize_1.DataTypes.ENUM('public', 'private'),
        allowNull: false,
        defaultValue: 'public',
        field: 'profile_visibility'
    },
    defaultPostVisibility: {
        type: sequelize_1.DataTypes.ENUM('public', 'private'),
        allowNull: false,
        defaultValue: 'public',
        field: 'default_post_visibility'
    },
    showEmail: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'show_email'
    },
    twoFactorEnabled: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'two_factor_enabled'
    },
    createdAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false
    }
}, {
    sequelize: database_1.sequelize,
    modelName: 'Settings',
    tableName: 'settings',
    timestamps: true,
    underscored: true,
    indexes: [
        {
            unique: true,
            fields: ['user_id']
        }
    ]
});
//# sourceMappingURL=Settings.js.map