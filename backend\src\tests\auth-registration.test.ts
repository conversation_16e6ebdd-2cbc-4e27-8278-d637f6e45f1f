import request from 'supertest'
import express from 'express'
import { sequelize } from '../config/database'
import { User } from '../models'
import { EmailService } from '../utils/email'
import { VerificationService } from '../utils/verification'
import { UserValidationService } from '../utils/userValidation'
import authRoutes from '../routes/auth'

// 创建测试应用
const app = express()
app.use(express.json())
app.use('/api/auth', authRoutes)

// Mock邮件服务
jest.mock('../utils/email')
const mockEmailService = EmailService as jest.Mocked<typeof EmailService>

describe('用户注册和邮件服务测试', () => {
  beforeAll(async () => {
    // 连接测试数据库
    await sequelize.authenticate()
    await sequelize.sync({ force: true })
    
    // Mock邮件服务方法
    mockEmailService.initialize = jest.fn().mockResolvedValue(undefined)
    mockEmailService.sendVerificationEmail = jest.fn().mockResolvedValue(true)
    mockEmailService.sendWelcomeEmail = jest.fn().mockResolvedValue(true)
    mockEmailService.sendPasswordResetEmail = jest.fn().mockResolvedValue(true)
  })

  afterAll(async () => {
    await sequelize.close()
  })

  beforeEach(async () => {
    // 清理数据库
    await User.destroy({ where: {} })
    jest.clearAllMocks()
  })

  describe('工具类测试', () => {
    describe('VerificationService', () => {
      test('应该生成有效的邮箱验证token', () => {
        const email = '<EMAIL>'
        const userId = 1
        const token = VerificationService.generateEmailVerificationToken(email, userId)
        
        expect(token).toBeDefined()
        expect(typeof token).toBe('string')
        
        const result = VerificationService.verifyEmailVerificationToken(token)
        expect(result.valid).toBe(true)
        expect(result.data?.email).toBe(email)
        expect(result.data?.userId).toBe(userId)
      })

      test('应该生成有效的密码重置token', () => {
        const email = '<EMAIL>'
        const userId = 1
        const token = VerificationService.generatePasswordResetToken(email, userId)
        
        expect(token).toBeDefined()
        expect(typeof token).toBe('string')
        
        const result = VerificationService.verifyPasswordResetToken(token)
        expect(result.valid).toBe(true)
        expect(result.data?.email).toBe(email)
        expect(result.data?.userId).toBe(userId)
      })

      test('应该验证密码强度', () => {
        const weakPassword = '123456'
        const strongPassword = 'StrongPass123!'
        
        const weakResult = VerificationService.validatePasswordStrength(weakPassword)
        expect(weakResult.valid).toBe(false)
        expect(weakResult.errors.length).toBeGreaterThan(0)
        
        const strongResult = VerificationService.validatePasswordStrength(strongPassword)
        expect(strongResult.valid).toBe(true)
        expect(strongResult.errors.length).toBe(0)
      })

      test('应该验证用户名格式', () => {
        const invalidUsername = '123'
        const validUsername = 'testuser'
        
        const invalidResult = VerificationService.validateUsername(invalidUsername)
        expect(invalidResult.valid).toBe(false)
        
        const validResult = VerificationService.validateUsername(validUsername)
        expect(validResult.valid).toBe(true)
      })
    })

    describe('UserValidationService', () => {
      test('应该验证注册数据', async () => {
        const validData = {
          username: 'testuser',
          email: '<EMAIL>',
          password: 'StrongPass123!',
          confirmPassword: 'StrongPass123!'
        }
        
        const result = await UserValidationService.validateRegistrationData(validData)
        expect(result.valid).toBe(true)
        expect(result.errors.length).toBe(0)
      })

      test('应该检测密码不匹配', async () => {
        const invalidData = {
          username: 'testuser',
          email: '<EMAIL>',
          password: 'StrongPass123!',
          confirmPassword: 'DifferentPass123!'
        }
        
        const result = await UserValidationService.validateRegistrationData(invalidData)
        expect(result.valid).toBe(false)
        expect(result.errors).toContain('两次输入的密码不一致')
      })
    })
  })

  describe('API端点测试', () => {
    describe('POST /api/auth/register', () => {
      test('应该成功注册新用户', async () => {
        const userData = {
          username: 'testuser',
          email: '<EMAIL>',
          password: 'StrongPass123!',
          confirmPassword: 'StrongPass123!'
        }

        const response = await request(app)
          .post('/api/auth/register')
          .send(userData)
          .expect(201)

        expect(response.body.success).toBe(true)
        expect(response.body.data.user.username).toBe(userData.username)
        expect(response.body.data.user.email).toBe(userData.email)
        expect(response.body.data.user.emailVerified).toBe(false)
        expect(mockEmailService.sendVerificationEmail).toHaveBeenCalledWith(
          userData.email,
          expect.any(String),
          userData.username
        )
      })

      test('应该拒绝重复的用户名', async () => {
        // 先创建一个用户
        await User.create({
          username: 'testuser',
          email: '<EMAIL>',
          password: 'hashedpassword',
          isActive: true,
          emailVerified: false
        })

        const userData = {
          username: 'testuser', // 重复的用户名
          email: '<EMAIL>',
          password: 'StrongPass123!',
          confirmPassword: 'StrongPass123!'
        }

        const response = await request(app)
          .post('/api/auth/register')
          .send(userData)
          .expect(400)

        expect(response.body.success).toBe(false)
        expect(response.body.error.message).toContain('用户名')
      })

      test('应该拒绝重复的邮箱', async () => {
        // 先创建一个用户
        await User.create({
          username: 'existinguser',
          email: '<EMAIL>',
          password: 'hashedpassword',
          isActive: true,
          emailVerified: false
        })

        const userData = {
          username: 'newuser',
          email: '<EMAIL>', // 重复的邮箱
          password: 'StrongPass123!',
          confirmPassword: 'StrongPass123!'
        }

        const response = await request(app)
          .post('/api/auth/register')
          .send(userData)
          .expect(400)

        expect(response.body.success).toBe(false)
        expect(response.body.error.message).toContain('邮箱')
      })

      test('应该拒绝弱密码', async () => {
        const userData = {
          username: 'testuser',
          email: '<EMAIL>',
          password: '123456', // 弱密码
          confirmPassword: '123456'
        }

        const response = await request(app)
          .post('/api/auth/register')
          .send(userData)
          .expect(400)

        expect(response.body.success).toBe(false)
        expect(response.body.error.message).toContain('密码')
      })
    })

    describe('POST /api/auth/verify-email', () => {
      test('应该成功验证邮箱', async () => {
        // 创建未验证的用户
        const user = await User.create({
          username: 'testuser',
          email: '<EMAIL>',
          password: 'hashedpassword',
          isActive: true,
          emailVerified: false
        })

        // 生成验证token
        const token = VerificationService.generateEmailVerificationToken(user.email, user.id)

        const response = await request(app)
          .post('/api/auth/verify-email')
          .send({ token })
          .expect(200)

        expect(response.body.success).toBe(true)
        expect(response.body.message).toContain('验证成功')

        // 检查用户是否已验证
        await user.reload()
        expect(user.emailVerified).toBe(true)
        expect(mockEmailService.sendWelcomeEmail).toHaveBeenCalledWith(
          user.email,
          user.username
        )
      })

      test('应该拒绝无效的验证token', async () => {
        const response = await request(app)
          .post('/api/auth/verify-email')
          .send({ token: 'invalid-token' })
          .expect(400)

        expect(response.body.success).toBe(false)
        expect(response.body.error.message).toContain('无效')
      })
    })

    describe('POST /api/auth/request-password-reset', () => {
      test('应该发送密码重置邮件', async () => {
        // 创建用户
        const user = await User.create({
          username: 'testuser',
          email: '<EMAIL>',
          password: 'hashedpassword',
          isActive: true,
          emailVerified: true
        })

        const response = await request(app)
          .post('/api/auth/request-password-reset')
          .send({ email: user.email })
          .expect(200)

        expect(response.body.success).toBe(true)
        expect(mockEmailService.sendPasswordResetEmail).toHaveBeenCalledWith(
          user.email,
          expect.any(String),
          user.username
        )
      })

      test('对于不存在的邮箱也应该返回成功（安全考虑）', async () => {
        const response = await request(app)
          .post('/api/auth/request-password-reset')
          .send({ email: '<EMAIL>' })
          .expect(200)

        expect(response.body.success).toBe(true)
        expect(mockEmailService.sendPasswordResetEmail).not.toHaveBeenCalled()
      })
    })
  })
})
