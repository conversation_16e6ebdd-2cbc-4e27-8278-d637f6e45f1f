#!/usr/bin/env ts-node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const connection_1 = require("../database/connection");
const database_1 = require("../config/database");
const _011_rbac_initial_data_1 = require("../database/seeders/011-rbac-initial-data");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
class SeedRbacScript {
    async seed() {
        console.log('🌱 Starting RBAC seed data insertion...');
        console.log('='.repeat(50));
        try {
            const connectionInfo = connection_1.dbConnection.getConnectionInfo();
            console.log(`📊 Database: ${connectionInfo.database}`);
            console.log(`🏠 Host: ${connectionInfo.host}:${connectionInfo.port}`);
            console.log(`👤 User: ${connectionInfo.username}`);
            console.log('');
            console.log('🔍 Testing database connection...');
            const isConnected = await connection_1.dbConnection.testConnection();
            if (!isConnected) {
                console.error('❌ Database connection failed!');
                process.exit(1);
            }
            console.log('✅ Database connection successful!');
            console.log('');
            const queryInterface = database_1.sequelize.getQueryInterface();
            console.log('🌱 Inserting RBAC seed data...');
            await (0, _011_rbac_initial_data_1.up)(queryInterface);
            console.log('');
            console.log('='.repeat(50));
            console.log('🎉 RBAC seed data insertion completed successfully!');
            console.log('');
            console.log('📋 Created data:');
            console.log('  • 4 default roles (super_admin, admin, editor, user)');
            console.log('  • 50+ permissions covering all system resources');
            console.log('  • Role-permission assignments for each role');
            console.log('');
            console.log('🔐 Default roles and their permissions:');
            console.log('  • super_admin: All permissions');
            console.log('  • admin: Most management permissions (except system-level)');
            console.log('  • editor: Content management permissions');
            console.log('  • user: Basic read and create permissions');
        }
        catch (error) {
            console.error('');
            console.error('='.repeat(50));
            console.error('❌ RBAC seed data insertion failed!');
            console.error('Error details:', error);
            process.exit(1);
        }
    }
    async clean() {
        console.log('🧹 Starting RBAC seed data cleanup...');
        console.log('='.repeat(50));
        try {
            const connectionInfo = connection_1.dbConnection.getConnectionInfo();
            console.log(`📊 Database: ${connectionInfo.database}`);
            console.log(`🏠 Host: ${connectionInfo.host}:${connectionInfo.port}`);
            console.log(`👤 User: ${connectionInfo.username}`);
            console.log('');
            console.log('🔍 Testing database connection...');
            const isConnected = await connection_1.dbConnection.testConnection();
            if (!isConnected) {
                console.error('❌ Database connection failed!');
                process.exit(1);
            }
            console.log('✅ Database connection successful!');
            console.log('');
            const queryInterface = database_1.sequelize.getQueryInterface();
            console.log('🧹 Cleaning RBAC seed data...');
            await (0, _011_rbac_initial_data_1.down)(queryInterface);
            console.log('');
            console.log('='.repeat(50));
            console.log('🎉 RBAC seed data cleanup completed successfully!');
        }
        catch (error) {
            console.error('');
            console.error('='.repeat(50));
            console.error('❌ RBAC seed data cleanup failed!');
            console.error('Error details:', error);
            process.exit(1);
        }
    }
    showHelp() {
        console.log(`
📚 RBAC Seed Data Tool

Usage:
  npm run seed:rbac              Insert RBAC seed data
  npm run seed:rbac clean        Clean RBAC seed data
  ts-node src/scripts/seedRbac.ts       Insert seed data directly
  ts-node src/scripts/seedRbac.ts clean Clean seed data directly

Environment Variables:
  DB_HOST      Database host (default: localhost)
  DB_PORT      Database port (default: 3306)
  DB_NAME      Database name (default: person-blog)
  DB_USER      Database user (default: person-blog)
  DB_PASSWORD  Database password

Examples:
  npm run seed:rbac
  npm run seed:rbac clean
  NODE_ENV=production npm run seed:rbac

Note:
  Make sure to run database migrations before seeding RBAC data:
  npm run migrate
`);
    }
}
async function main() {
    const args = process.argv.slice(2);
    if (args.includes('--help') || args.includes('-h')) {
        const script = new SeedRbacScript();
        script.showHelp();
        return;
    }
    const isClean = args.includes('clean');
    const script = new SeedRbacScript();
    if (isClean) {
        await script.clean();
    }
    else {
        await script.seed();
    }
}
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});
if (require.main === module) {
    main().catch((error) => {
        console.error('Script execution failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=seedRbac.js.map