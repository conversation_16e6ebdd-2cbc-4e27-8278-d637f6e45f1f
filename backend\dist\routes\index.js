"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = __importDefault(require("./auth"));
const article_1 = __importDefault(require("./article"));
const tag_1 = __importDefault(require("./tag"));
const category_1 = __importDefault(require("./category"));
const comment_1 = __importDefault(require("./comment"));
const post_1 = __importDefault(require("./post"));
const media_1 = __importDefault(require("./media"));
const notification_1 = __importDefault(require("./notification"));
const settings_1 = __importDefault(require("./settings"));
const roles_1 = __importDefault(require("./roles"));
const permissions_1 = __importDefault(require("./permissions"));
const userRoles_1 = __importDefault(require("./userRoles"));
const user_1 = __importDefault(require("./user"));
const auditLog_1 = __importDefault(require("./auditLog"));
const timeline_1 = __importDefault(require("./timeline"));
const upload_1 = __importDefault(require("./upload"));
const router = (0, express_1.Router)();
router.use('/auth', auth_1.default);
router.use('/articles', article_1.default);
router.use('/tags', tag_1.default);
router.use('/categories', category_1.default);
router.use('/comments', comment_1.default);
router.use('/posts', post_1.default);
router.use('/media', media_1.default);
router.use('/notifications', notification_1.default);
router.use('/settings', settings_1.default);
router.use('/roles', roles_1.default);
router.use('/permissions', permissions_1.default);
router.use('/user-roles', userRoles_1.default);
router.use('/users', user_1.default);
router.use('/audit-logs', auditLog_1.default);
router.use('/timeline', timeline_1.default);
router.use('/upload', upload_1.default);
router.get('/', (req, res) => {
    res.json({
        success: true,
        message: 'Personal Blog API',
        version: '1.0.0',
        endpoints: {
            auth: '/api/auth',
            articles: '/api/articles',
            tags: '/api/tags',
            categories: '/api/categories',
            comments: '/api/comments',
            posts: '/api/posts',
            media: '/api/media',
            notifications: '/api/notifications',
            settings: '/api/settings',
            roles: '/api/roles',
            permissions: '/api/permissions',
            userRoles: '/api/user-roles',
            users: '/api/users',
            auditLogs: '/api/audit-logs',
            timeline: '/api/timeline',
            upload: '/api/upload',
            health: '/health',
            docs: '/api-docs'
        }
    });
});
exports.default = router;
//# sourceMappingURL=index.js.map