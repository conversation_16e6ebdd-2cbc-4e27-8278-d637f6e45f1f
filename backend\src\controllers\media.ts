import { Request, Response } from 'express'
import { Media } from '../models/Media'
import { User } from '../models/User'
import { Op } from 'sequelize'
import fs from 'fs'
import path from 'path'
import { getIdParam } from '../utils/paramValidation'

/**
 * 扩展Request接口，添加用户信息
 */
interface AuthenticatedRequest extends Request {
  user?: {
    id: number
    username: string
    email: string
  }
}

/**
 * 媒体查询参数接口
 */
interface MediaQueryParams {
  page?: string
  limit?: string
  category?: 'image' | 'video' | 'audio' | 'document'
  search?: string
  uploaderId?: string
  isPublic?: string
  sortBy?: 'createdAt' | 'size' | 'originalName'
  sortOrder?: 'ASC' | 'DESC'
}

/**
 * 媒体控制器类
 * 处理媒体文件的CRUD操作和管理功能
 */
export class MediaController {
  /**
   * 获取媒体列表
   * GET /api/media
   */
  public static async getMediaList(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const {
        page = '1',
        limit = '20',
        category,
        search,
        uploaderId,
        isPublic,
        sortBy = 'createdAt',
        sortOrder = 'DESC'
      } = req.query as MediaQueryParams

      const pageNum = parseInt(page, 10)
      const limitNum = parseInt(limit, 10)
      const offset = (pageNum - 1) * limitNum

      // 构建查询条件
      const whereClause: any = {}

      if (category) {
        whereClause.category = category
      }

      if (uploaderId) {
        whereClause.uploaderId = parseInt(uploaderId, 10)
      }

      if (isPublic !== undefined) {
        whereClause.isPublic = isPublic === 'true'
      }

      if (search) {
        whereClause[Op.or] = [
          { originalName: { [Op.like]: `%${search}%` } },
          { description: { [Op.like]: `%${search}%` } },
          { filename: { [Op.like]: `%${search}%` } }
        ]
      }

      // 如果不是管理员，只能看到公开的媒体文件或自己上传的文件
      if (req.user) {
        whereClause[Op.or] = [
          { isPublic: true },
          { uploaderId: req.user.id }
        ]
      } else {
        whereClause.isPublic = true
      }

      const { count, rows } = await Media.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: 'uploader',
            attributes: ['id', 'username']
          }
        ],
        order: [[sortBy, sortOrder]],
        limit: limitNum,
        offset
      })

      const totalPages = Math.ceil(count / limitNum)

      res.json({
        success: true,
        data: {
          media: rows,
          pagination: {
            currentPage: pageNum,
            totalPages,
            totalItems: count,
            itemsPerPage: limitNum,
            hasNextPage: pageNum < totalPages,
            hasPrevPage: pageNum > 1
          }
        }
      })
    } catch (error) {
      console.error('获取媒体列表失败:', error)
      res.status(500).json({
        success: false,
        message: '获取媒体列表失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  /**
   * 获取单个媒体详情
   * GET /api/media/:id
   */
  public static async getMediaById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params
      const mediaId = getIdParam(req)

      if (isNaN(mediaId)) {
        res.status(400).json({
          success: false,
          message: '无效的媒体ID'
        })
        return
      }

      const media = await Media.findByPk(mediaId, {
        include: [
          {
            model: User,
            as: 'uploader',
            attributes: ['id', 'username', 'email']
          }
        ]
      })

      if (!media) {
        res.status(404).json({
          success: false,
          message: '媒体文件不存在'
        })
        return
      }

      // 检查访问权限
      if (!media.isPublic && (!req.user || req.user.id !== media.uploaderId)) {
        res.status(403).json({
          success: false,
          message: '没有权限访问此媒体文件'
        })
        return
      }

      res.json({
        success: true,
        data: media
      })
    } catch (error) {
      console.error('获取媒体详情失败:', error)
      res.status(500).json({
        success: false,
        message: '获取媒体详情失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  /**
   * 更新媒体信息
   * PUT /api/media/:id
   */
  public static async updateMedia(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params
      const mediaId = getIdParam(req)
      const { description, tags, isPublic } = req.body

      if (isNaN(mediaId)) {
        res.status(400).json({
          success: false,
          message: '无效的媒体ID'
        })
        return
      }

      const media = await Media.findByPk(mediaId)

      if (!media) {
        res.status(404).json({
          success: false,
          message: '媒体文件不存在'
        })
        return
      }

      // 检查权限：只有上传者可以修改
      if (!req.user || req.user.id !== media.uploaderId) {
        res.status(403).json({
          success: false,
          message: '没有权限修改此媒体文件'
        })
        return
      }

      // 更新媒体信息
      const updateData: any = {}
      if (description !== undefined) updateData.description = description
      if (tags !== undefined) updateData.tags = tags
      if (isPublic !== undefined) updateData.isPublic = isPublic

      await media.update(updateData)

      // 重新获取更新后的数据
      const updatedMedia = await Media.findByPk(mediaId, {
        include: [
          {
            model: User,
            as: 'uploader',
            attributes: ['id', 'username']
          }
        ]
      })

      res.json({
        success: true,
        message: '媒体信息更新成功',
        data: updatedMedia
      })
    } catch (error) {
      console.error('更新媒体信息失败:', error)
      res.status(500).json({
        success: false,
        message: '更新媒体信息失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  /**
   * 删除媒体文件
   * DELETE /api/media/:id
   */
  public static async deleteMedia(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params
      const mediaId = getIdParam(req)

      if (isNaN(mediaId)) {
        res.status(400).json({
          success: false,
          message: '无效的媒体ID'
        })
        return
      }

      const media = await Media.findByPk(mediaId)

      if (!media) {
        res.status(404).json({
          success: false,
          message: '媒体文件不存在'
        })
        return
      }

      // 检查权限：只有上传者可以删除
      if (!req.user || req.user.id !== media.uploaderId) {
        res.status(403).json({
          success: false,
          message: '没有权限删除此媒体文件'
        })
        return
      }

      // 删除物理文件
      try {
        const filePath = path.join(process.cwd(), 'uploads', 'images', media.filename)
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath)
        }

        // 删除缩略图（如果存在）
        if (media.thumbnailUrl) {
          const thumbnailFilename = media.thumbnailUrl.split('/').pop()
          if (thumbnailFilename) {
            const thumbnailPath = path.join(process.cwd(), 'uploads', 'thumbnails', thumbnailFilename)
            if (fs.existsSync(thumbnailPath)) {
              fs.unlinkSync(thumbnailPath)
            }
          }
        }
      } catch (fileError) {
        console.warn('删除物理文件失败:', fileError)
        // 继续删除数据库记录，即使物理文件删除失败
      }

      // 删除数据库记录
      await media.destroy()

      res.json({
        success: true,
        message: '媒体文件删除成功'
      })
    } catch (error) {
      console.error('删除媒体文件失败:', error)
      res.status(500).json({
        success: false,
        message: '删除媒体文件失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  /**
   * 获取媒体统计信息
   * GET /api/media/stats
   */
  public static async getMediaStats(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?.id

      // 构建基础查询条件
      const baseWhere = userId ? { uploaderId: userId } : { isPublic: true }

      const [
        totalCount,
        imageCount,
        videoCount,
        audioCount,
        documentCount,
        totalSize
      ] = await Promise.all([
        Media.count({ where: baseWhere }),
        Media.count({ where: { ...baseWhere, category: 'image' } }),
        Media.count({ where: { ...baseWhere, category: 'video' } }),
        Media.count({ where: { ...baseWhere, category: 'audio' } }),
        Media.count({ where: { ...baseWhere, category: 'document' } }),
        Media.sum('size', { where: baseWhere }) || 0
      ])

      res.json({
        success: true,
        data: {
          total: totalCount,
          byCategory: {
            image: imageCount,
            video: videoCount,
            audio: audioCount,
            document: documentCount
          },
          totalSize,
          formattedTotalSize: formatFileSize(totalSize)
        }
      })
    } catch (error) {
      console.error('获取媒体统计失败:', error)
      res.status(500).json({
        success: false,
        message: '获取媒体统计失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
