interface EmailOptions {
    to: string | string[];
    subject: string;
    text?: string;
    html?: string;
    from?: string;
}
export declare class EmailService {
    private static transporter;
    private static config;
    static initialize(): Promise<void>;
    static sendEmail(options: EmailOptions): Promise<boolean>;
    static sendVerificationEmail(email: string, token: string, username: string): Promise<boolean>;
    static sendPasswordResetEmail(email: string, token: string, username: string): Promise<boolean>;
    static sendWelcomeEmail(email: string, username: string): Promise<boolean>;
    private static renderVerificationTemplate;
    private static renderPasswordResetTemplate;
    private static renderWelcomeTemplate;
}
export {};
//# sourceMappingURL=email.d.ts.map