import { QueryInterface, DataTypes } from 'sequelize'

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.createTable('article_likes', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    article_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'articles',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  })

  // 添加索引
  await queryInterface.addIndex('article_likes', ['article_id', 'user_id'], {
    unique: true
  })
  await queryInterface.addIndex('article_likes', ['article_id'])
  await queryInterface.addIndex('article_likes', ['user_id'])
  await queryInterface.addIndex('article_likes', ['created_at'])
}

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.dropTable('article_likes')
}
