{"version": 3, "file": "User.d.ts", "sourceRoot": "", "sources": ["../../src/models/User.ts"], "names": [], "mappings": "AAAA,OAAO,EAAa,KAAK,EAAE,QAAQ,EAAE,MAAM,WAAW,CAAA;AAQtD,MAAM,WAAW,cAAc;IAC7B,EAAE,EAAE,MAAM,CAAA;IACV,QAAQ,EAAE,MAAM,CAAA;IAChB,KAAK,EAAE,MAAM,CAAA;IACb,YAAY,EAAE,MAAM,CAAA;IACpB,QAAQ,EAAE,OAAO,CAAA;IACjB,aAAa,EAAE,OAAO,CAAA;IACtB,eAAe,CAAC,EAAE,IAAI,CAAA;IACtB,WAAW,CAAC,EAAE,IAAI,CAAA;IAClB,kBAAkB,CAAC,EAAE,MAAM,CAAA;IAC3B,oBAAoB,CAAC,EAAE,IAAI,CAAA;IAC3B,sBAAsB,CAAC,EAAE,MAAM,CAAA;IAC/B,wBAAwB,CAAC,EAAE,IAAI,CAAA;IAC/B,SAAS,EAAE,IAAI,CAAA;IACf,SAAS,EAAE,IAAI,CAAA;CAChB;AAMD,MAAM,WAAW,sBAAuB,SAAQ,QAAQ,CAAC,cAAc,EAAE,IAAI,GAAG,WAAW,GAAG,WAAW,CAAC;CAAI;AAO9G,qBAAa,IAAK,SAAQ,KAAK,CAAC,cAAc,EAAE,sBAAsB,CAAE,YAAW,cAAc;IACxF,EAAE,EAAG,MAAM,CAAA;IACX,QAAQ,EAAG,MAAM,CAAA;IACjB,KAAK,EAAG,MAAM,CAAA;IACd,YAAY,EAAG,MAAM,CAAA;IACrB,QAAQ,EAAG,OAAO,CAAA;IAClB,aAAa,EAAG,OAAO,CAAA;IACvB,eAAe,CAAC,EAAE,IAAI,CAAA;IACtB,WAAW,CAAC,EAAE,IAAI,CAAA;IAClB,kBAAkB,CAAC,EAAE,MAAM,CAAA;IAC3B,oBAAoB,CAAC,EAAE,IAAI,CAAA;IAC3B,sBAAsB,CAAC,EAAE,MAAM,CAAA;IAC/B,wBAAwB,CAAC,EAAE,IAAI,CAAA;IAC/B,SAAS,EAAG,IAAI,CAAA;IAChB,SAAS,EAAG,IAAI,CAAA;IAQV,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAQ1D,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE,cAAc,CAAC;WAYjC,YAAY,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;WAU/C,cAAc,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;WAStD,WAAW,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;CAGrE"}