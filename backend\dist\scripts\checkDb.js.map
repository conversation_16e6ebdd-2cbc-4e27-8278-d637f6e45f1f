{"version": 3, "file": "checkDb.js", "sourceRoot": "", "sources": ["../../src/scripts/checkDb.ts"], "names": [], "mappings": ";;AAmNS,sCAAa;AAnNtB,iDAA8C;AAC9C,yCAAsD;AAMtD,KAAK,UAAU,aAAa;IAC1B,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAA;IACnD,OAAO,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAA;IAE3E,IAAI,CAAC;QAEH,MAAM,oBAAS,CAAC,YAAY,EAAE,CAAA;QAC9B,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;QAEhD,MAAM,cAAc,GAAG,oBAAS,CAAC,iBAAiB,EAAE,CAAA;QAGpD,MAAM,cAAc,GAAG;YACrB,OAAO;YACP,YAAY;YACZ,MAAM;YACN,UAAU;YACV,cAAc;YACd,UAAU;YACV,OAAO;YACP,YAAY;YACZ,OAAO;YACP,eAAe;YACf,0BAA0B;YAC1B,UAAU;YACV,OAAO;YACP,aAAa;YACb,YAAY;YACZ,kBAAkB;YAClB,YAAY;YACZ,YAAY;YACZ,SAAS;SACV,CAAA;QAED,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAA;QACtC,KAAK,MAAM,SAAS,IAAI,cAAc,EAAE,CAAC;YACvC,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,aAAa,EAAE,CAAA;gBACxD,IAAI,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;oBACpC,OAAO,CAAC,GAAG,CAAC,YAAY,SAAS,UAAU,CAAC,CAAA;gBAC9C,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,GAAG,CAAC,YAAY,SAAS,WAAW,CAAC,CAAA;gBAC/C,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,GAAG,CAAC,2BAA2B,SAAS,IAAI,EAAE,KAAK,CAAC,CAAA;YAC9D,CAAC;QACH,CAAC;QAGD,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAA;QAE3C,MAAM,UAAU,GAAG;YACjB,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE;YACrC,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE;YAC1C,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE;YACpC,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE;YACxC,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE;YACxC,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE;YACrC,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,EAAE;YAC5C,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE;YACvC,EAAE,KAAK,EAAE,eAAe,EAAE,WAAW,EAAE,IAAI,EAAE;YAC7C,EAAE,KAAK,EAAE,0BAA0B,EAAE,WAAW,EAAE,MAAM,EAAE;YAC1D,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE;YAC1C,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE;YACrC,EAAE,KAAK,EAAE,aAAa,EAAE,WAAW,EAAE,IAAI,EAAE;YAC3C,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,EAAE;YAC5C,EAAE,KAAK,EAAE,kBAAkB,EAAE,WAAW,EAAE,MAAM,EAAE;SACnD,CAAA;QAED,KAAK,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,UAAU,EAAE,CAAC;YAChD,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,SAAS,CAAC,KAAK,CACjD,iCAAiC,KAAK,EAAE,EACxC,EAAE,IAAI,EAAE,sBAAU,CAAC,MAAM,EAAE,CACnB,CAAA;gBAEV,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAA;gBACnC,OAAO,CAAC,GAAG,CAAC,MAAM,WAAW,KAAK,KAAK,MAAM,KAAK,MAAM,CAAC,CAAA;YAC3D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,GAAG,CAAC,oBAAoB,KAAK,GAAG,EAAE,KAAK,CAAC,CAAA;YAClD,CAAC;QACH,CAAC;QAGD,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAA;QAEnD,IAAI,CAAC;YAEH,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,SAAS,CAAC,KAAK,CACtD;;;iBAGS,EACT,EAAE,IAAI,EAAE,sBAAU,CAAC,MAAM,EAAE,CACnB,CAAA;YAEV,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAA;gBAClC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,EAAE;oBAChC,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,CAAC,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,WAAW,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,UAAU,GAAG,CAAC,CAAA;gBACnH,CAAC,CAAC,CAAA;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAA;YACnC,CAAC;YAGD,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,SAAS,CAAC,KAAK,CACtD,gDAAgD,EAChD,EAAE,IAAI,EAAE,sBAAU,CAAC,MAAM,EAAE,CACnB,CAAA;YAEV,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,IAAI,CAAC,CAAA;YACnD,OAAO,CAAC,GAAG,CAAC,uBAAuB,UAAU,EAAE,CAAC,CAAA;YAGhD,MAAM,cAAc,GAAG,MAAM,cAAc,CAAC,SAAS,CAAC,KAAK,CACzD;;;uBAGe,EACf,EAAE,IAAI,EAAE,sBAAU,CAAC,MAAM,EAAE,CACnB,CAAA;YAEV,MAAM,YAAY,GAAG,cAAc,CAAC,CAAC,CAAC,CAAA;YACtC,OAAO,CAAC,GAAG,CAAC,eAAe,YAAY,CAAC,gBAAgB,sBAAsB,YAAY,CAAC,aAAa,gBAAgB,CAAC,CAAA;QAE3H,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAA;QAC7D,CAAC;QAGD,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAA;QAEjD,MAAM,gBAAgB,GAAG;YACvB,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE;YACvC,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE;YACxC,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE;YACxC,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE;YACxD,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE;SACzC,CAAA;QAED,KAAK,MAAM,SAAS,IAAI,gBAAgB,EAAE,CAAC;YACzC,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAU,CAAA;gBACxE,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,GAAQ,EAAE,EAAE;oBACzC,IAAI,SAAS,IAAI,SAAS,EAAE,CAAC;wBAC3B,OAAO,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAW,EAAE,EAAE,CAC7C,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,KAAK,GAAG,CAAC,CACzD,CAAA;oBACH,CAAC;yBAAM,CAAC;wBACN,OAAO,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,KAAK,SAAS,CAAC,MAAM,CAAC,CAAA;oBAC9E,CAAC;gBACH,CAAC,CAAC,CAAA;gBAEF,MAAM,SAAS,GAAG,SAAS,IAAI,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAA;gBAC1F,OAAO,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,aAAa,SAAS,CAAC,KAAK,IAAI,SAAS,MAAM,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAA;YACvH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,GAAG,CAAC,6BAA6B,SAAS,CAAC,KAAK,GAAG,EAAE,KAAK,CAAC,CAAA;YACrE,CAAC;QACH,CAAC;QAGD,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAA;QAE3C,IAAI,CAAC;YAEH,MAAM,cAAc,GAAG,MAAM,cAAc,CAAC,SAAS,CAAC,KAAK,CACzD;;;gEAGwD,EACxD,EAAE,IAAI,EAAE,sBAAU,CAAC,MAAM,EAAE,CACnB,CAAA;YAEV,MAAM,eAAe,GAAG,cAAc,CAAC,CAAC,CAAC,EAAE,gBAAgB,IAAI,CAAC,CAAA;YAChE,OAAO,CAAC,GAAG,CAAC,GAAG,eAAe,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,yBAAyB,eAAe,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,eAAe,kBAAkB,EAAE,CAAC,CAAA;QAEpJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAA;QACrD,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,gEAAgE,CAAC,CAAA;QAC7E,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAA;QAClD,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;QAC5B,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAA;QAC3C,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAA;QACzC,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAA;QAC7C,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAA;QACpD,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAA;IAE1E,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAA;QAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACjB,CAAC;YAAS,CAAC;QACT,MAAM,oBAAS,CAAC,KAAK,EAAE,CAAA;QACvB,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAA;IAC3C,CAAC;AACH,CAAC;AAGD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,aAAa,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;AACtC,CAAC"}