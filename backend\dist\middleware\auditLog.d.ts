import { Request, Response, NextFunction } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: number;
        username: string;
        email: string;
    };
    auditData?: {
        action: string;
        resource: string;
        resourceId?: number;
        oldData?: any;
        startTime: number;
    };
}
interface AuditLogConfig {
    action: string;
    resource: string;
    getResourceId?: (req: AuthenticatedRequest) => number | undefined;
    captureOldData?: boolean;
    captureNewData?: boolean;
    skipCondition?: (req: AuthenticatedRequest) => boolean;
}
export declare const createAuditLogMiddleware: (config: AuditLogConfig) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const auditUserCreate: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const auditUserUpdate: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const auditUserDelete: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const auditArticleCreate: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const auditArticleUpdate: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const auditArticleDelete: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const auditLogin: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const auditLogout: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const auditRoleAssign: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const auditPermissionGrant: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
declare const _default: {
    createAuditLogMiddleware: (config: AuditLogConfig) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    auditUserCreate: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    auditUserUpdate: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    auditUserDelete: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    auditArticleCreate: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    auditArticleUpdate: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    auditArticleDelete: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    auditLogin: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    auditLogout: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    auditRoleAssign: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    auditPermissionGrant: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
};
export default _default;
//# sourceMappingURL=auditLog.d.ts.map