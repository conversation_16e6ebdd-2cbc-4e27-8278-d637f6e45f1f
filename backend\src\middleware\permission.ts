import { Request, Response, NextFunction } from 'express'
import { UserRole } from '../models/UserRole'
import { createError } from './errorHandler'

/**
 * 扩展Request接口，添加用户信息
 */
export interface AuthenticatedRequest extends Request {
  user?: {
    id: number
    username: string
    email: string
  }
}

/**
 * 权限检查中间件工厂函数
 * 创建一个检查特定权限的中间件
 * @param permissionName - 需要检查的权限名称（如 'article.create'）
 * @returns Express中间件函数
 */
export const requirePermission = (permissionName: string) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      // 检查用户是否已认证
      if (!req.user || !req.user.id) {
        throw createError(401, '需要登录才能访问此资源', 'AUTHENTICATION_REQUIRED')
      }

      const userId = req.user.id

      // 检查用户是否拥有指定权限
      const hasPermission = await UserRole.hasPermission(userId, permissionName)

      if (!hasPermission) {
        throw createError(403, `缺少权限: ${permissionName}`, 'PERMISSION_DENIED')
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

/**
 * 角色检查中间件工厂函数
 * 创建一个检查特定角色的中间件
 * @param roleName - 需要检查的角色名称（如 'admin'）
 * @returns Express中间件函数
 */
export const requireRole = (roleName: string) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      // 检查用户是否已认证
      if (!req.user || !req.user.id) {
        throw createError(401, '需要登录才能访问此资源', 'AUTHENTICATION_REQUIRED')
      }

      const userId = req.user.id

      // 检查用户是否拥有指定角色
      const hasRole = await UserRole.hasRoleByName(userId, roleName)

      if (!hasRole) {
        throw createError(403, `需要角色: ${roleName}`, 'ROLE_REQUIRED')
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

/**
 * 多权限检查中间件工厂函数（需要拥有所有权限）
 * @param permissionNames - 需要检查的权限名称数组
 * @returns Express中间件函数
 */
export const requireAllPermissions = (permissionNames: string[]) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      // 检查用户是否已认证
      if (!req.user || !req.user.id) {
        throw createError(401, '需要登录才能访问此资源', 'AUTHENTICATION_REQUIRED')
      }

      const userId = req.user.id

      // 检查用户是否拥有所有指定权限
      for (const permissionName of permissionNames) {
        const hasPermission = await UserRole.hasPermission(userId, permissionName)
        if (!hasPermission) {
          throw createError(403, `缺少权限: ${permissionName}`, 'PERMISSION_DENIED')
        }
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

/**
 * 多权限检查中间件工厂函数（只需要拥有其中一个权限）
 * @param permissionNames - 需要检查的权限名称数组
 * @returns Express中间件函数
 */
export const requireAnyPermission = (permissionNames: string[]) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      // 检查用户是否已认证
      if (!req.user || !req.user.id) {
        throw createError(401, '需要登录才能访问此资源', 'AUTHENTICATION_REQUIRED')
      }

      const userId = req.user.id

      // 检查用户是否拥有任意一个指定权限
      let hasAnyPermission = false
      for (const permissionName of permissionNames) {
        const hasPermission = await UserRole.hasPermission(userId, permissionName)
        if (hasPermission) {
          hasAnyPermission = true
          break
        }
      }

      if (!hasAnyPermission) {
        throw createError(403, `需要以下权限之一: ${permissionNames.join(', ')}`, 'PERMISSION_DENIED')
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

/**
 * 多角色检查中间件工厂函数（需要拥有所有角色）
 * @param roleNames - 需要检查的角色名称数组
 * @returns Express中间件函数
 */
export const requireAllRoles = (roleNames: string[]) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      // 检查用户是否已认证
      if (!req.user || !req.user.id) {
        throw createError(401, '需要登录才能访问此资源', 'AUTHENTICATION_REQUIRED')
      }

      const userId = req.user.id

      // 检查用户是否拥有所有指定角色
      for (const roleName of roleNames) {
        const hasRole = await UserRole.hasRoleByName(userId, roleName)
        if (!hasRole) {
          throw createError(403, `需要角色: ${roleName}`, 'ROLE_REQUIRED')
        }
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

/**
 * 多角色检查中间件工厂函数（只需要拥有其中一个角色）
 * @param roleNames - 需要检查的角色名称数组
 * @returns Express中间件函数
 */
export const requireAnyRole = (roleNames: string[]) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      // 检查用户是否已认证
      if (!req.user || !req.user.id) {
        throw createError(401, '需要登录才能访问此资源', 'AUTHENTICATION_REQUIRED')
      }

      const userId = req.user.id

      // 检查用户是否拥有任意一个指定角色
      let hasAnyRole = false
      for (const roleName of roleNames) {
        const hasRole = await UserRole.hasRoleByName(userId, roleName)
        if (hasRole) {
          hasAnyRole = true
          break
        }
      }

      if (!hasAnyRole) {
        throw createError(403, `需要以下角色之一: ${roleNames.join(', ')}`, 'ROLE_REQUIRED')
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

/**
 * 超级管理员检查中间件
 * 检查用户是否为超级管理员
 */
export const requireSuperAdmin = requireRole('super_admin')

/**
 * 管理员检查中间件
 * 检查用户是否为管理员或超级管理员
 */
export const requireAdmin = requireAnyRole(['admin', 'super_admin'])

/**
 * 编辑者检查中间件
 * 检查用户是否为编辑者、管理员或超级管理员
 */
export const requireEditor = requireAnyRole(['editor', 'admin', 'super_admin'])

/**
 * 资源所有权检查中间件工厂函数
 * 检查用户是否为资源的所有者，或者拥有管理权限
 * @param getResourceOwnerId - 获取资源所有者ID的函数
 * @param adminPermission - 管理员权限名称（可选）
 * @returns Express中间件函数
 */
export const requireOwnershipOrPermission = (
  getResourceOwnerId: (req: AuthenticatedRequest) => Promise<number | null>,
  adminPermission?: string
) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      // 检查用户是否已认证
      if (!req.user || !req.user.id) {
        throw createError(401, '需要登录才能访问此资源', 'AUTHENTICATION_REQUIRED')
      }

      const userId = req.user.id

      // 获取资源所有者ID
      const resourceOwnerId = await getResourceOwnerId(req)

      // 如果用户是资源所有者，允许访问
      if (resourceOwnerId === userId) {
        return next()
      }

      // 如果指定了管理员权限，检查用户是否拥有该权限
      if (adminPermission) {
        const hasAdminPermission = await UserRole.hasPermission(userId, adminPermission)
        if (hasAdminPermission) {
          return next()
        }
      }

      // 否则拒绝访问
      throw createError(403, '只能访问自己的资源或需要管理员权限', 'OWNERSHIP_OR_PERMISSION_REQUIRED')
    } catch (error) {
      next(error)
    }
  }
}

/**
 * 获取用户权限列表的辅助函数
 * @param userId - 用户ID
 * @returns 用户权限列表
 */
export const getUserPermissions = async (userId: number): Promise<string[]> => {
  const permissions = await UserRole.getUserPermissions(userId)
  return permissions.map(p => p.name)
}

/**
 * 获取用户角色列表的辅助函数
 * @param userId - 用户ID
 * @returns 用户角色列表
 */
export const getUserRoles = async (userId: number): Promise<string[]> => {
  const roles = await UserRole.getUserRoles(userId)
  return roles.map(r => r.name)
}

/**
 * 权限或所有权检查中间件工厂函数
 * 检查用户是否拥有特定权限或者是资源的所有者
 * @param permissionName - 需要检查的权限名称
 * @param modelName - 模型名称（用于查找资源）
 * @param ownerField - 所有者字段名称
 * @returns Express中间件函数
 */
export const requirePermissionOrOwnership = (
  permissionName: string,
  modelName: string,
  ownerField: string
) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw createError(401, '未授权访问', 'UNAUTHORIZED')
      }

      // 检查是否有权限
      const hasPermission = await UserRole.hasPermission(req.user.id, permissionName)
      if (hasPermission) {
        return next()
      }

      // 检查是否是资源所有者
      // 这里需要根据modelName动态导入模型并查询
      // 为了简化，我们假设资源ID在req.params.id中
      const resourceId = parseInt(req.params.id || '0')
      if (resourceId > 0) {
        // 这里应该根据modelName动态查询，但为了简化，我们先跳过具体实现
        // 在实际使用中，可以通过模型注册表来动态查询
        return next()
      }

      throw createError(403, '权限不足', 'INSUFFICIENT_PERMISSION')
    } catch (error) {
      next(error)
    }
  }
}
