import { Request, Response, NextFunction } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: number;
        username: string;
        email: string;
    };
}
export declare class UserController {
    static getUsers(req: Request, res: Response, next: NextFunction): Promise<void>;
    static getUserById(req: Request, res: Response, next: NextFunction): Promise<void>;
    static createUser(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void>;
    static updateUser(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void>;
    static deleteUser(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void>;
    static getUserStats(req: Request, res: Response, next: NextFunction): Promise<void>;
}
export declare const getUsers: typeof UserController.getUsers, getUserById: typeof UserController.getUserById, createUser: typeof UserController.createUser, updateUser: typeof UserController.updateUser, deleteUser: typeof UserController.deleteUser, getUserStats: typeof UserController.getUserStats;
export default UserController;
//# sourceMappingURL=user.d.ts.map