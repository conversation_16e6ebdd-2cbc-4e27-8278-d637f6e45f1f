import { sequelize } from '../config/database'
import fs from 'fs'
import path from 'path'

/**
 * 运行所有迁移文件
 */
async function runFreshMigrations(): Promise<void> {
  const migrationsDir = path.join(__dirname, '../database/migrations')

  // 创建迁移记录表
  await sequelize.query(`
    CREATE TABLE IF NOT EXISTS migrations (
      name VARCHAR(255) NOT NULL,
      executed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (name)
    ) ENGINE=InnoDB;
  `)

  // 获取所有迁移文件
  const migrationFiles = fs.readdirSync(migrationsDir)
    .filter(file => file.endsWith('.ts'))
    .sort()

  console.log(`Found ${migrationFiles.length} migration files`)

  // 执行每个迁移
  for (const file of migrationFiles) {
    const migrationName = file.replace('.ts', '')
    console.log(`Running migration: ${migrationName}`)

    try {
      // 动态导入迁移文件
      const migrationPath = path.join(migrationsDir, file)
      const migration = require(migrationPath)

      // 执行up方法
      await migration.up(sequelize.getQueryInterface())

      // 记录迁移
      await sequelize.query(
        'INSERT INTO migrations (name) VALUES (?)',
        { replacements: [migrationName] }
      )

      console.log(`✅ Migration ${migrationName} completed`)
    } catch (error) {
      console.error(`❌ Migration ${migrationName} failed:`, error)
      throw error
    }
  }
}

/**
 * 重置数据库脚本
 * 删除所有表和迁移记录，然后重新运行迁移
 */
async function resetDatabase() {
  try {
    console.log('🚀 Starting database reset...')
    console.log('==================================================')
    console.log(`📊 Database: ${process.env.DB_NAME}`)
    console.log(`🏠 Host: ${process.env.DB_HOST}:${process.env.DB_PORT}`)
    console.log(`👤 User: ${process.env.DB_USER}`)
    console.log('')

    // 测试数据库连接
    console.log('🔍 Testing database connection...')
    await sequelize.authenticate()
    console.log('✅ Database connection successful!')
    console.log('')

    // 获取所有表名
    console.log('📋 Getting all tables...')
    const [tables] = await sequelize.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = '${process.env.DB_NAME}' 
      AND TABLE_TYPE = 'BASE TABLE'
    `) as [Array<{ TABLE_NAME: string }>, any]

    if (tables.length > 0) {
      console.log(`Found ${tables.length} tables to drop:`)
      tables.forEach(table => console.log(`  - ${table.TABLE_NAME}`))
      console.log('')

      // 禁用外键检查
      console.log('🔓 Disabling foreign key checks...')
      await sequelize.query('SET FOREIGN_KEY_CHECKS = 0')

      // 删除所有表
      console.log('🗑️ Dropping all tables...')
      for (const table of tables) {
        console.log(`  Dropping table: ${table.TABLE_NAME}`)
        await sequelize.query(`DROP TABLE IF EXISTS \`${table.TABLE_NAME}\``)
      }

      // 重新启用外键检查
      console.log('🔒 Re-enabling foreign key checks...')
      await sequelize.query('SET FOREIGN_KEY_CHECKS = 1')
      console.log('✅ All tables dropped successfully!')
    } else {
      console.log('ℹ️ No tables found to drop.')
    }

    console.log('')
    console.log('📦 Running fresh migrations...')

    // 重新运行迁移
    await runFreshMigrations()

    console.log('')
    console.log('==================================================')
    console.log('🎉 Database reset completed successfully!')

  } catch (error) {
    console.error('❌ Database reset failed:', error)
    throw error
  } finally {
    await sequelize.close()
    console.log('Database connection closed')
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  resetDatabase()
    .then(() => {
      console.log('✅ Reset completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Reset failed:', error)
      process.exit(1)
    })
}

export { resetDatabase }
