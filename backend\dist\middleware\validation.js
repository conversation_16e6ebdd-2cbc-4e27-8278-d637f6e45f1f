"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validatePostUpdate = exports.validatePostCreation = exports.validateCommentQuery = exports.validateCommentStatus = exports.validateCommentUpdate = exports.validateComment = exports.validateCategoryUpdate = exports.validateCategory = exports.validateArticleUpdate = exports.validateArticle = exports.validateParams = exports.validateQuery = exports.validateRequest = exports.validate = void 0;
const joi_1 = __importDefault(require("joi"));
const errorHandler_1 = require("./errorHandler");
const validate = (schema) => {
    return (req, res, next) => {
        const { error } = schema.validate(req.body, { abortEarly: false });
        if (error) {
            const errorMessage = error.details.map(detail => detail.message).join(', ');
            throw (0, errorHandler_1.createError)(400, errorMessage, 'VALIDATION_ERROR');
        }
        next();
    };
};
exports.validate = validate;
const validateRequest = (schema) => {
    return (req, res, next) => {
        const { error } = schema.validate(req.body, { abortEarly: false });
        if (error) {
            const errorMessage = error.details.map(detail => detail.message).join(', ');
            throw (0, errorHandler_1.createError)(400, errorMessage, 'VALIDATION_ERROR');
        }
        next();
    };
};
exports.validateRequest = validateRequest;
const validateQuery = (schema) => {
    return (req, res, next) => {
        const { error } = schema.validate(req.query, { abortEarly: false });
        if (error) {
            const errorMessage = error.details.map(detail => detail.message).join(', ');
            throw (0, errorHandler_1.createError)(400, errorMessage, 'VALIDATION_ERROR');
        }
        next();
    };
};
exports.validateQuery = validateQuery;
const validateParams = (schema) => {
    return (req, res, next) => {
        const { error } = schema.validate(req.params, { abortEarly: false });
        if (error) {
            const errorMessage = error.details.map(detail => detail.message).join(', ');
            throw (0, errorHandler_1.createError)(400, errorMessage, 'VALIDATION_ERROR');
        }
        next();
    };
};
exports.validateParams = validateParams;
const articleSchema = joi_1.default.object({
    title: joi_1.default.string().min(1).max(200).required(),
    content: joi_1.default.string().min(1).required(),
    excerpt: joi_1.default.string().optional().allow(''),
    status: joi_1.default.string().valid('draft', 'published').default('draft'),
    tags: joi_1.default.array().items(joi_1.default.string().trim().min(1)).default([]),
    categoryId: joi_1.default.number().integer().positive().optional().allow(null)
});
const articleUpdateSchema = joi_1.default.object({
    title: joi_1.default.string().min(1).max(200).optional(),
    content: joi_1.default.string().min(1).optional(),
    excerpt: joi_1.default.string().optional().allow(''),
    status: joi_1.default.string().valid('draft', 'published').optional(),
    tags: joi_1.default.array().items(joi_1.default.string().trim().min(1)).optional(),
    categoryId: joi_1.default.number().integer().positive().optional().allow(null)
});
const categorySchema = joi_1.default.object({
    name: joi_1.default.string().min(1).max(100).required(),
    slug: joi_1.default.string().min(1).max(100).pattern(/^[a-z0-9-]+$/i).optional(),
    description: joi_1.default.string().optional().allow(''),
    parentId: joi_1.default.number().integer().positive().optional().allow(null),
    sort: joi_1.default.number().integer().min(0).default(0)
});
const categoryUpdateSchema = joi_1.default.object({
    name: joi_1.default.string().min(1).max(100).optional(),
    slug: joi_1.default.string().min(1).max(100).pattern(/^[a-z0-9-]+$/i).optional(),
    description: joi_1.default.string().optional().allow(''),
    parentId: joi_1.default.number().integer().positive().optional().allow(null),
    sort: joi_1.default.number().integer().min(0).optional()
});
exports.validateArticle = (0, exports.validate)(articleSchema);
exports.validateArticleUpdate = (0, exports.validate)(articleUpdateSchema);
exports.validateCategory = (0, exports.validate)(categorySchema);
exports.validateCategoryUpdate = (0, exports.validate)(categoryUpdateSchema);
const commentSchema = joi_1.default.object({
    content: joi_1.default.string().min(1).max(2000).required()
        .custom((value, helpers) => {
        if (/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(value)) {
            return helpers.error('any.invalid');
        }
        return value;
    }, 'XSS Protection'),
    articleId: joi_1.default.number().integer().positive().required(),
    parentId: joi_1.default.number().integer().positive().optional()
});
const commentUpdateSchema = joi_1.default.object({
    content: joi_1.default.string().min(1).max(2000).required()
        .custom((value, helpers) => {
        if (/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(value)) {
            return helpers.error('any.invalid');
        }
        return value;
    }, 'XSS Protection')
});
const commentStatusSchema = joi_1.default.object({
    status: joi_1.default.string().valid('pending', 'approved', 'rejected').required()
});
const commentQuerySchema = joi_1.default.object({
    articleId: joi_1.default.number().integer().positive().optional(),
    page: joi_1.default.number().integer().min(1).default(1),
    limit: joi_1.default.number().integer().min(1).max(100).default(10),
    status: joi_1.default.string().valid('pending', 'approved', 'rejected', 'all').default('approved')
});
exports.validateComment = (0, exports.validate)(commentSchema);
exports.validateCommentUpdate = (0, exports.validate)(commentUpdateSchema);
exports.validateCommentStatus = (0, exports.validate)(commentStatusSchema);
exports.validateCommentQuery = (0, exports.validateQuery)(commentQuerySchema);
const postCreationSchema = joi_1.default.object({
    content: joi_1.default.string().trim().min(1).max(1000).required().messages({
        'string.empty': '说说内容不能为空',
        'string.min': '说说内容不能为空',
        'string.max': '说说内容不能超过1000个字符',
        'any.required': '说说内容是必填项'
    }),
    images: joi_1.default.array().items(joi_1.default.string().uri().messages({
        'string.uri': '图片URL格式不正确'
    })).max(9).optional().messages({
        'array.max': '最多只能上传9张图片'
    }),
    visibility: joi_1.default.string().valid('public', 'private').default('public').messages({
        'any.only': '可见性设置无效，只能是 public 或 private'
    }),
    location: joi_1.default.string().max(200).optional().messages({
        'string.max': '位置信息不能超过200个字符'
    })
});
const postUpdateSchema = joi_1.default.object({
    content: joi_1.default.string().trim().min(1).max(1000).optional().messages({
        'string.empty': '说说内容不能为空',
        'string.min': '说说内容不能为空',
        'string.max': '说说内容不能超过1000个字符'
    }),
    images: joi_1.default.array().items(joi_1.default.string().uri().messages({
        'string.uri': '图片URL格式不正确'
    })).max(9).optional().messages({
        'array.max': '最多只能上传9张图片'
    }),
    visibility: joi_1.default.string().valid('public', 'private').optional().messages({
        'any.only': '可见性设置无效，只能是 public 或 private'
    }),
    location: joi_1.default.string().max(200).optional().messages({
        'string.max': '位置信息不能超过200个字符'
    })
});
exports.validatePostCreation = (0, exports.validate)(postCreationSchema);
exports.validatePostUpdate = (0, exports.validate)(postUpdateSchema);
//# sourceMappingURL=validation.js.map