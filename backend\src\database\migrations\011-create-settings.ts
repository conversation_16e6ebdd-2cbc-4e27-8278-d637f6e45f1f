import { QueryInterface, DataTypes } from 'sequelize'

/**
 * 创建用户设置表的数据库迁移
 * 该迁移创建 settings 表，用于存储用户的个人设置信息
 * 迁移序号：013 (重命名自 20241229_create_settings_table.ts)
 */
export const up = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.createTable('settings', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },

    // 个人信息设置
    display_name: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '显示名称'
    },
    avatar: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '头像URL'
    },
    bio: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '个人简介'
    },
    website: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '个人网站'
    },
    location: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '所在地'
    },

    // 偏好设置
    theme: {
      type: DataTypes.ENUM('light', 'dark', 'auto'),
      allowNull: false,
      defaultValue: 'auto',
      comment: '主题偏好'
    },
    language: {
      type: DataTypes.STRING(10),
      allowNull: false,
      defaultValue: 'zh-CN',
      comment: '语言设置'
    },
    timezone: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: 'Asia/Shanghai',
      comment: '时区设置'
    },
    items_per_page: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 10,
      comment: '每页显示数量'
    },

    // 通知设置
    email_notifications: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '邮件通知'
    },
    comment_notifications: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '评论通知'
    },
    system_notifications: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '系统通知'
    },

    // 隐私设置
    profile_visibility: {
      type: DataTypes.ENUM('public', 'private'),
      allowNull: false,
      defaultValue: 'public',
      comment: '个人资料可见性'
    },
    default_post_visibility: {
      type: DataTypes.ENUM('public', 'private'),
      allowNull: false,
      defaultValue: 'public',
      comment: '默认文章可见性'
    },
    show_email: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '显示邮箱地址'
    },

    // 安全设置
    two_factor_enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '两步验证'
    },

    // 媒体设置
    max_file_size: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 10,
      comment: '最大文件大小(MB)'
    },
    allowed_file_types: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
      comment: '允许的文件类型'
    },
    auto_generate_thumbnail: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '自动生成缩略图'
    },

    // 内容设置
    default_article_visibility: {
      type: DataTypes.ENUM('public', 'private'),
      allowNull: false,
      defaultValue: 'public',
      comment: '默认文章可见性'
    },
    enable_comments: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '启用评论'
    },
    require_comment_approval: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '需要评论审核'
    },
    enable_article_likes: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '启用文章点赞'
    },

    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci'
  })

  // 创建索引
  try {
    await queryInterface.addIndex('settings', ['user_id'], {
      unique: true,
      name: 'settings_user_id_unique'
    })
  } catch {
    console.log('Index settings_user_id_unique already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('settings', ['theme'], {
      name: 'settings_theme_index'
    })
  } catch {
    console.log('Index settings_theme_index already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('settings', ['profile_visibility'], {
      name: 'settings_profile_visibility_index'
    })
  } catch {
    console.log('Index settings_profile_visibility_index already exists, skipping...')
  }

  try {
    await queryInterface.addIndex('settings', ['created_at'], {
      name: 'settings_created_at_index'
    })
  } catch {
    console.log('Index settings_created_at_index already exists, skipping...')
  }
}

/**
 * 回滚迁移，删除 settings 表
 */
export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.dropTable('settings')
}

/**
 * 迁移信息
 */
export const migrationInfo = {
  name: '20241229_create_settings_table',
  description: '创建用户设置表',
  version: '1.0.0',
  author: 'System',
  createdAt: new Date('2024-12-29')
}
