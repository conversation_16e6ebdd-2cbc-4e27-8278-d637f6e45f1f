{"version": 3, "file": "011-create-media.js", "sourceRoot": "", "sources": ["../../../src/database/migrations/011-create-media.ts"], "names": [], "mappings": ";;;AAAA,yCAAqD;AAM9C,MAAM,EAAE,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IACxE,MAAM,cAAc,CAAC,WAAW,CAAC,OAAO,EAAE;QACxC,EAAE,EAAE;YACF,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,QAAQ;SAClB;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,KAAK;YAChB,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,kBAAkB;SAC5B;QACD,aAAa,EAAE;YACb,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,kBAAkB;SAC5B;QACD,SAAS,EAAE;YACT,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,QAAQ;SAClB;QACD,IAAI,EAAE;YACJ,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,UAAU;SACpB;QACD,GAAG,EAAE;YACH,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,SAAS;SACnB;QACD,aAAa,EAAE;YACb,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,mBAAmB;SAC7B;QACD,KAAK,EAAE;YACL,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,aAAa;SACvB;QACD,MAAM,EAAE;YACN,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,aAAa;SACvB;QACD,WAAW,EAAE;YACX,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;YACnB,OAAO,EAAE,SAAS;SACnB;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC;YAC3D,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,OAAO;YACrB,OAAO,EAAE,MAAM;SAChB;QACD,IAAI,EAAE;YACJ,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,cAAc;SACxB;QACD,WAAW,EAAE;YACX,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,MAAM;SAChB;QACD,SAAS,EAAE;YACT,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,QAAQ;SAClB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;KACF,CAAC,CAAA;IAGF,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,aAAa,CAAC,EAAE;YACtD,IAAI,EAAE,uBAAuB;SAC9B,CAAC,CAAA;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAA;IACxE,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC,EAAE;YACnD,IAAI,EAAE,oBAAoB;SAC3B,CAAC,CAAA;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAA;IACrE,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,WAAW,CAAC,EAAE;YACpD,IAAI,EAAE,qBAAqB;SAC5B,CAAC,CAAA;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAA;IACtE,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,WAAW,CAAC,EAAE;YACpD,IAAI,EAAE,qBAAqB;SAC5B,CAAC,CAAA;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAA;IACtE,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,YAAY,CAAC,EAAE;YACrD,IAAI,EAAE,sBAAsB;SAC7B,CAAC,CAAA;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAA;IACvE,CAAC;IAGD,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE;YAChE,IAAI,EAAE,2BAA2B;SAClC,CAAC,CAAA;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAA;IAC5E,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,aAAa,EAAE,UAAU,CAAC,EAAE;YAClE,IAAI,EAAE,6BAA6B;SACpC,CAAC,CAAA;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,+DAA+D,CAAC,CAAA;IAC9E,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAA;AACnD,CAAC,CAAA;AA5JY,QAAA,EAAE,MA4Jd;AAKM,MAAM,IAAI,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IAC1E,MAAM,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;IACvC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAA;AACnD,CAAC,CAAA;AAHY,QAAA,IAAI,QAGhB"}