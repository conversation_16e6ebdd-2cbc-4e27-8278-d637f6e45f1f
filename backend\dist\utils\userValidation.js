"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserValidationService = void 0;
const models_1 = require("../models");
const verification_1 = require("./verification");
class UserValidationService {
    static async validateRegistrationData(data) {
        const errors = [];
        const usernameValidation = verification_1.VerificationService.validateUsername(data.username);
        if (!usernameValidation.valid) {
            errors.push(...usernameValidation.errors);
        }
        if (!verification_1.VerificationService.isValidEmail(data.email)) {
            errors.push('邮箱格式无效');
        }
        const passwordValidation = verification_1.VerificationService.validatePasswordStrength(data.password);
        if (!passwordValidation.valid) {
            errors.push(...passwordValidation.errors);
        }
        if (data.confirmPassword && data.password !== data.confirmPassword) {
            errors.push('两次输入的密码不一致');
        }
        const usernameAvailability = await this.checkUsernameAvailability(data.username);
        if (!usernameAvailability.available) {
            errors.push(usernameAvailability.message || '用户名已存在');
        }
        const emailAvailability = await this.checkEmailAvailability(data.email);
        if (!emailAvailability.available) {
            errors.push(emailAvailability.message || '邮箱已被注册');
        }
        return {
            valid: errors.length === 0,
            errors
        };
    }
    static async checkUsernameAvailability(username) {
        try {
            const validation = verification_1.VerificationService.validateUsername(username);
            if (!validation.valid) {
                return {
                    available: false,
                    message: validation.errors[0] || '用户名格式无效'
                };
            }
            const existingUser = await models_1.User.findByUsername(username);
            if (existingUser) {
                return {
                    available: false,
                    message: '用户名已存在'
                };
            }
            return {
                available: true
            };
        }
        catch (error) {
            console.error('检查用户名可用性失败:', error);
            return {
                available: false,
                message: '检查用户名可用性时发生错误'
            };
        }
    }
    static async checkEmailAvailability(email) {
        try {
            if (!verification_1.VerificationService.isValidEmail(email)) {
                return {
                    available: false,
                    message: '邮箱格式无效'
                };
            }
            const existingUser = await models_1.User.findByEmail(email);
            if (existingUser) {
                return {
                    available: false,
                    message: '邮箱已被注册'
                };
            }
            return {
                available: true
            };
        }
        catch (error) {
            console.error('检查邮箱可用性失败:', error);
            return {
                available: false,
                message: '检查邮箱可用性时发生错误'
            };
        }
    }
    static validateLoginData(data) {
        const errors = [];
        if (!data.username || data.username.trim().length === 0) {
            errors.push('用户名不能为空');
        }
        if (!data.password || data.password.length === 0) {
            errors.push('密码不能为空');
        }
        if (data.username && data.username.length > 50) {
            errors.push('用户名长度不能超过50位');
        }
        if (data.password && data.password.length > 128) {
            errors.push('密码长度不能超过128位');
        }
        return {
            valid: errors.length === 0,
            errors
        };
    }
    static validatePasswordResetData(data) {
        const errors = [];
        const passwordValidation = verification_1.VerificationService.validatePasswordStrength(data.password);
        if (!passwordValidation.valid) {
            errors.push(...passwordValidation.errors);
        }
        if (data.password !== data.confirmPassword) {
            errors.push('两次输入的密码不一致');
        }
        return {
            valid: errors.length === 0,
            errors
        };
    }
    static async validateUserUpdateData(userId, data) {
        const errors = [];
        if (data.username !== undefined) {
            const usernameValidation = verification_1.VerificationService.validateUsername(data.username);
            if (!usernameValidation.valid) {
                errors.push(...usernameValidation.errors);
            }
            else {
                const existingUser = await models_1.User.findByUsername(data.username);
                if (existingUser && existingUser.id !== userId) {
                    errors.push('用户名已被其他用户使用');
                }
            }
        }
        if (data.email !== undefined) {
            if (!verification_1.VerificationService.isValidEmail(data.email)) {
                errors.push('邮箱格式无效');
            }
            else {
                const existingUser = await models_1.User.findByEmail(data.email);
                if (existingUser && existingUser.id !== userId) {
                    errors.push('邮箱已被其他用户使用');
                }
            }
        }
        if (data.password !== undefined) {
            const passwordValidation = verification_1.VerificationService.validatePasswordStrength(data.password);
            if (!passwordValidation.valid) {
                errors.push(...passwordValidation.errors);
            }
            if (data.confirmPassword && data.password !== data.confirmPassword) {
                errors.push('两次输入的密码不一致');
            }
        }
        return {
            valid: errors.length === 0,
            errors
        };
    }
    static validateEmailVerificationRequest(email) {
        const errors = [];
        if (!email || email.trim().length === 0) {
            errors.push('邮箱不能为空');
        }
        else if (!verification_1.VerificationService.isValidEmail(email)) {
            errors.push('邮箱格式无效');
        }
        return {
            valid: errors.length === 0,
            errors
        };
    }
    static validatePasswordResetRequest(email) {
        return this.validateEmailVerificationRequest(email);
    }
    static async canPerformSensitiveOperation(userId) {
        try {
            const user = await models_1.User.findByPk(userId);
            if (!user) {
                return {
                    allowed: false,
                    reason: '用户不存在'
                };
            }
            if (!user.isActive) {
                return {
                    allowed: false,
                    reason: '用户账户已被禁用'
                };
            }
            if (!user.emailVerified) {
                return {
                    allowed: false,
                    reason: '请先验证邮箱地址'
                };
            }
            return {
                allowed: true
            };
        }
        catch (error) {
            console.error('检查用户权限失败:', error);
            return {
                allowed: false,
                reason: '检查用户权限时发生错误'
            };
        }
    }
    static generateDisplayNameSuggestions(username) {
        const suggestions = [];
        suggestions.push(username);
        suggestions.push(username.charAt(0).toUpperCase() + username.slice(1));
        if (username.length > 3) {
            suggestions.push(username.substring(0, 3) + '***');
        }
        return suggestions.slice(0, 3);
    }
    static containsProfanity(username) {
        const profanityList = [
            'admin', 'administrator', 'moderator', 'system', 'root',
            'fuck', 'shit', 'damn', 'hell', 'ass', 'bitch'
        ];
        const lowerUsername = username.toLowerCase();
        return profanityList.some(word => lowerUsername.includes(word));
    }
}
exports.UserValidationService = UserValidationService;
//# sourceMappingURL=userValidation.js.map