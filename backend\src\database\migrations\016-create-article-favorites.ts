import { QueryInterface, DataTypes } from 'sequelize'

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.createTable('article_favorites', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    article_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'articles',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  })

  // 添加索引
  await queryInterface.addIndex('article_favorites', ['article_id', 'user_id'], {
    unique: true,
    name: 'article_favorites_article_user_unique'
  })

  await queryInterface.addIndex('article_favorites', ['article_id'], {
    name: 'article_favorites_article_id_index'
  })

  await queryInterface.addIndex('article_favorites', ['user_id'], {
    name: 'article_favorites_user_id_index'
  })

  await queryInterface.addIndex('article_favorites', ['created_at'], {
    name: 'article_favorites_created_at_index'
  })
}

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.dropTable('article_favorites')
}
