import { QueryInterface } from 'sequelize'

/**
 * 创建说说点赞种子数据
 * 为说说添加点赞记录，用于测试和演示
 * 注意：对应迁移文件 012-create-post-likes.ts
 */
export const up = async (queryInterface: QueryInterface): Promise<void> => {
  console.log('Creating sample post likes...')

  // 检查是否已存在点赞记录
  const existingLikes = await queryInterface.select(null, 'post_likes', {})
  if (existingLikes.length > 0) {
    console.log('Post likes already exist, skipping...')
    return
  }

  // 获取用户和说说数据
  const users = await queryInterface.select(null, 'users', {})
  const posts = await queryInterface.select(null, 'posts', {})

  if (users.length === 0 || posts.length === 0) {
    console.log('No users or posts found, skipping post likes creation...')
    return
  }

  const adminUser = users.find((u: any) => u.username === 'admin') as any
  const regularUser = users.find((u: any) => u.username === 'john_doe') as any
  const testUser = users.find((u: any) => u.username === 'jane_smith') as any

  if (!adminUser || !regularUser) {
    console.log('Required users not found, skipping post likes creation...')
    return
  }

  // 定义点赞数据
  const postLikes = []

  // 为每个说说添加一些点赞
  for (const post of posts) {
    const postData = post as any

    // admin的说说被其他用户点赞
    if (postData.author_id === adminUser.id) {
      // regular user 点赞
      postLikes.push({
        post_id: postData.id,
        user_id: regularUser.id,
        created_at: new Date(new Date(postData.created_at).getTime() + 30 * 60 * 1000) // 30分钟后点赞
      })

      // test user 点赞（如果存在）
      if (testUser) {
        postLikes.push({
          post_id: postData.id,
          user_id: testUser.id,
          created_at: new Date(new Date(postData.created_at).getTime() + 60 * 60 * 1000) // 1小时后点赞
        })
      }
    }

    // regular user的说说被其他用户点赞
    if (postData.author_id === regularUser.id) {
      // admin 点赞
      postLikes.push({
        post_id: postData.id,
        user_id: adminUser.id,
        created_at: new Date(new Date(postData.created_at).getTime() + 45 * 60 * 1000) // 45分钟后点赞
      })

      // test user 点赞（如果存在且说说是公开的）
      if (testUser && postData.visibility === 'public') {
        postLikes.push({
          post_id: postData.id,
          user_id: testUser.id,
          created_at: new Date(new Date(postData.created_at).getTime() + 90 * 60 * 1000) // 1.5小时后点赞
        })
      }
    }

    // test user的说说被其他用户点赞
    if (testUser && postData.author_id === testUser.id) {
      // admin 点赞
      postLikes.push({
        post_id: postData.id,
        user_id: adminUser.id,
        created_at: new Date(new Date(postData.created_at).getTime() + 20 * 60 * 1000) // 20分钟后点赞
      })

      // regular user 点赞
      postLikes.push({
        post_id: postData.id,
        user_id: regularUser.id,
        created_at: new Date(new Date(postData.created_at).getTime() + 75 * 60 * 1000) // 1小时15分钟后点赞
      })
    }
  }

  // 为其他用户的说说添加点赞
  const otherUsers = users.filter((u: any) =>
    u.username !== 'admin' &&
    u.username !== 'john_doe' &&
    u.username !== 'jane_smith'
  )

  for (const user of otherUsers) {
    const userData = user as any
    const userPosts = posts.filter((p: any) => p.author_id === userData.id)

    for (const post of userPosts) {
      const postData = post as any

      // admin 点赞
      if (Math.random() > 0.3) { // 70% 概率点赞
        postLikes.push({
          post_id: postData.id,
          user_id: adminUser.id,
          created_at: new Date(new Date(postData.created_at).getTime() + Math.random() * 2 * 60 * 60 * 1000) // 随机时间点赞
        })
      }

      // regular user 点赞
      if (Math.random() > 0.5) { // 50% 概率点赞
        postLikes.push({
          post_id: postData.id,
          user_id: regularUser.id,
          created_at: new Date(new Date(postData.created_at).getTime() + Math.random() * 3 * 60 * 60 * 1000) // 随机时间点赞
        })
      }

      // test user 点赞
      if (testUser && Math.random() > 0.4) { // 60% 概率点赞
        postLikes.push({
          post_id: postData.id,
          user_id: testUser.id,
          created_at: new Date(new Date(postData.created_at).getTime() + Math.random() * 4 * 60 * 60 * 1000) // 随机时间点赞
        })
      }
    }
  }

  // 插入点赞数据
  if (postLikes.length > 0) {
    await queryInterface.bulkInsert('post_likes', postLikes)
    console.log(`Created ${postLikes.length} sample post likes`)
  } else {
    console.log('No post likes to create')
  }
}

/**
 * 删除说说点赞种子数据
 */
export const down = async (queryInterface: QueryInterface): Promise<void> => {
  console.log('Removing sample post likes...')
  await queryInterface.bulkDelete('post_likes', {}, {})
  console.log('Sample post likes removed')
}
