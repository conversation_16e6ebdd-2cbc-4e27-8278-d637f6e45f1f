import { Request, Response, NextFunction } from 'express'
import { AuthService } from '../services/auth'
import { createError } from '../middleware/errorHandler'
import { EmailService } from '../utils/email'
import { VerificationService } from '../utils/verification'
import { UserValidationService } from '../utils/userValidation'
import { User } from '../models'
import { hashPassword } from '../utils/password'
import Joi from 'joi'


/**
 * 登录请求参数的校验规则
 * - username: 字符串类型，仅允许字母数字，长度3-50位，必填
 * - password: 字符串类型，长度6-100位，必填
 */
const loginSchema = Joi.object({
  username: Joi.string().alphanum().min(3).max(50).required(),
  password: Joi.string().min(6).max(100).required()
})

/**
 * 刷新Token请求参数的校验规则
 * - token: 字符串类型，必填
 */
const refreshTokenSchema = Joi.object({
  token: Joi.string().required()
})

/**
 * 注册请求参数的校验规则
 */
const registerSchema = Joi.object({
  username: Joi.string().min(3).max(50).required(),
  email: Joi.string().email().required(),
  password: Joi.string().min(8).max(128).required(),
  confirmPassword: Joi.string().required()
})


/**
 * 用户登录接口
 * @param req - Express请求对象，包含登录所需的用户名和密码
 * @param res - Express响应对象，用于返回登录结果
 * @param next - Express中间件的next函数，用于传递错误
 * @returns 无返回值，通过res.json返回登录结果或next传递错误
 */
export const login = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {

    // 校验请求体参数
    const { error, value } = loginSchema.validate(req.body)
    if (error) {
      throw createError(400, error.details[0]?.message || 'Validation error', 'VALIDATION_ERROR')
    }

    const { username, password } = value


    // 调用认证服务进行登录
    const result = await AuthService.login({ username, password })

    if (!result.success) {
      throw createError(401, result.message || 'Authentication failed', 'AUTH_FAILED')
    }


    // 返回登录成功信息及用户数据和token
    res.json({
      success: true,
      data: {
        user: result.user,
        token: result.token
      },
      message: 'Login successful'
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 用户登出接口
 * @param req - Express请求对象
 * @param res - Express响应对象，用于返回登出结果
 * @param next - Express中间件的next函数，用于传递错误
 * @returns 无返回值，通过res.json返回登出结果或next传递错误
 */
export const logout = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {

    res.json({
      success: true,
      message: 'Logout successful'
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 获取当前用户信息接口
 * @param req - Express请求对象，应包含已解析的用户信息
 * @param res - Express响应对象，用于返回用户信息
 * @param next - Express中间件的next函数，用于传递错误
 * @returns 无返回值，通过res.json返回用户信息或next传递错误
 */
export const getProfile = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {

    // 从请求中获取用户信息
    const user = (req as any).user

    if (!user) {
      throw createError(401, 'User not authenticated', 'UNAUTHORIZED')
    }

    res.json({
      success: true,
      data: {
        user
      }
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 刷新访问令牌接口
 * @param req - Express请求对象，包含旧的刷新令牌
 * @param res - Express响应对象，用于返回新的用户信息和访问令牌
 * @param next - Express中间件的next函数，用于传递错误
 * @returns 无返回值，通过res.json返回刷新结果或next传递错误
 */
export const refreshToken = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {

    // 校验请求体中的token参数
    const { error, value } = refreshTokenSchema.validate(req.body)
    if (error) {
      throw createError(400, error.details[0]?.message || 'Validation error', 'VALIDATION_ERROR')
    }

    const { token } = value


    // 调用认证服务刷新token
    const result = await AuthService.refreshToken(token)

    if (!result.success) {
      throw createError(401, result.message || 'Token refresh failed', 'TOKEN_REFRESH_FAILED')
    }

    res.json({
      success: true,
      data: {
        user: result.user,
        token: result.token
      },
      message: 'Token refreshed successfully'
    })
  } catch (error) {
    next(error)
  }
}


/**
 * 验证访问令牌有效性接口
 * @param req - Express请求对象，包含Authorization头部
 * @param res - Express响应对象，用于返回验证结果
 * @param next - Express中间件的next函数，用于传递错误
 * @returns 无返回值，通过res.json返回验证结果或next传递错误
 */
export const validateToken = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // 从请求头中提取token
    const authHeader = req.headers['authorization']
    const token = AuthService.extractTokenFromHeader(authHeader)

    if (!token) {
      throw createError(400, 'Token is required', 'TOKEN_REQUIRED')
    }

    // 调用认证服务验证token
    const result = await AuthService.validateToken(token)

    if (!result.success) {
      throw createError(401, result.message || 'Token validation failed', 'TOKEN_INVALID')
    }

    res.json({
      success: true,
      data: {
        user: result.user,
        valid: true
      },
      message: 'Token is valid'
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 用户注册接口
 */
export const register = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // 校验请求体参数
    const { error, value } = registerSchema.validate(req.body)
    if (error) {
      throw createError(400, error.details[0]?.message || 'Validation error', 'VALIDATION_ERROR')
    }

    const { username, email, password, confirmPassword } = value

    // 验证密码确认
    if (password !== confirmPassword) {
      throw createError(400, '两次输入的密码不一致', 'PASSWORD_MISMATCH')
    }

    // 验证注册数据
    const validation = await UserValidationService.validateRegistrationData({
      username,
      email,
      password,
      confirmPassword
    })

    if (!validation.valid) {
      throw createError(400, validation.errors.join('; '), 'VALIDATION_ERROR')
    }

    // 创建用户
    const hashedPassword = await hashPassword(password)
    const user = await User.create({
      username,
      email,
      passwordHash: hashedPassword,
      isActive: true,
      emailVerified: false
    })

    // 生成邮箱验证token
    const verificationToken = VerificationService.generateEmailVerificationToken(email, user.id)

    // 发送验证邮件
    const emailSent = await EmailService.sendVerificationEmail(email, verificationToken, username)

    res.status(201).json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          emailVerified: user.emailVerified,
          isActive: user.isActive
        },
        emailSent
      },
      message: '注册成功，请检查邮箱并验证邮箱地址'
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 邮箱验证接口
 */
export const verifyEmail = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { token } = req.body

    // 验证token
    const result = VerificationService.verifyEmailVerificationToken(token)
    if (!result.valid || !result.data) {
      throw createError(400, result.error || '无效的验证令牌', 'INVALID_TOKEN')
    }

    // 查找用户
    const user = await User.findByEmail(result.data.email!)
    if (!user) {
      throw createError(404, '用户不存在', 'USER_NOT_FOUND')
    }

    // 检查是否已验证
    if (user.emailVerified) {
      throw createError(400, '邮箱已经验证过了', 'EMAIL_ALREADY_VERIFIED')
    }

    // 更新用户验证状态
    await user.update({ emailVerified: true })

    // 发送欢迎邮件
    await EmailService.sendWelcomeEmail(user.email, user.username)

    res.json({
      success: true,
      message: '邮箱验证成功'
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 重新发送验证邮件接口
 */
export const resendVerificationEmail = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { email } = req.body

    if (!email) {
      throw createError(400, '邮箱地址不能为空', 'EMAIL_REQUIRED')
    }

    // 验证邮箱格式
    if (!VerificationService.isValidEmail(email)) {
      throw createError(400, '邮箱格式无效', 'INVALID_EMAIL')
    }

    // 查找用户
    const user = await User.findByEmail(email)
    if (!user) {
      throw createError(404, '用户不存在', 'USER_NOT_FOUND')
    }

    // 检查是否已验证
    if (user.emailVerified) {
      throw createError(400, '邮箱已经验证过了', 'EMAIL_ALREADY_VERIFIED')
    }

    // 生成新的验证token
    const verificationToken = VerificationService.generateEmailVerificationToken(email, user.id)

    // 发送验证邮件
    const emailSent = await EmailService.sendVerificationEmail(email, verificationToken, user.username)

    res.json({
      success: true,
      data: { emailSent },
      message: '验证邮件已重新发送'
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 请求密码重置接口
 */
export const requestPasswordReset = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { email } = req.body

    if (!email) {
      throw createError(400, '邮箱地址不能为空', 'EMAIL_REQUIRED')
    }

    // 验证邮箱格式
    if (!VerificationService.isValidEmail(email)) {
      throw createError(400, '邮箱格式无效', 'INVALID_EMAIL')
    }

    // 查找用户
    const user = await User.findByEmail(email)
    if (!user) {
      // 为了安全，即使用户不存在也返回成功
      res.json({
        success: true,
        message: '如果该邮箱已注册，您将收到密码重置邮件'
      })
      return
    }

    // 生成密码重置token
    const resetToken = VerificationService.generatePasswordResetToken(email, user.id)

    // 发送密码重置邮件
    const emailSent = await EmailService.sendPasswordResetEmail(email, resetToken, user.username)

    res.json({
      success: true,
      data: { emailSent },
      message: '如果该邮箱已注册，您将收到密码重置邮件'
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 重置密码接口
 */
export const resetPassword = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { token, password, confirmPassword } = req.body

    // 验证密码确认
    if (password !== confirmPassword) {
      throw createError(400, '两次输入的密码不一致', 'PASSWORD_MISMATCH')
    }

    // 验证token
    const result = VerificationService.verifyPasswordResetToken(token)
    if (!result.valid || !result.data) {
      throw createError(400, result.error || '无效的重置令牌', 'INVALID_TOKEN')
    }

    // 验证密码强度
    const passwordValidation = VerificationService.validatePasswordStrength(password)
    if (!passwordValidation.valid) {
      throw createError(400, passwordValidation.errors.join('; '), 'WEAK_PASSWORD')
    }

    // 查找用户
    const user = await User.findByPk(result.data.userId!)
    if (!user) {
      throw createError(404, '用户不存在', 'USER_NOT_FOUND')
    }

    // 更新密码
    const hashedPassword = await hashPassword(password)
    await user.update({ passwordHash: hashedPassword })

    res.json({
      success: true,
      message: '密码重置成功'
    })
  } catch (error) {
    next(error)
  }
}