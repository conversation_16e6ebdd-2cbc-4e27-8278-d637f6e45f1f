import { QueryInterface, DataTypes } from 'sequelize'

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.createTable('user_follows', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    follower_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    following_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  })

  // 添加索引
  await queryInterface.addIndex('user_follows', ['follower_id', 'following_id'], {
    unique: true,
    name: 'user_follows_follower_following_unique'
  })

  await queryInterface.addIndex('user_follows', ['follower_id'], {
    name: 'user_follows_follower_id_index'
  })

  await queryInterface.addIndex('user_follows', ['following_id'], {
    name: 'user_follows_following_id_index'
  })

  await queryInterface.addIndex('user_follows', ['created_at'], {
    name: 'user_follows_created_at_index'
  })
}

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.dropTable('user_follows')
}
