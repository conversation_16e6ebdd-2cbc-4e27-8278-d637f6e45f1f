"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('media', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
            comment: '媒体文件ID'
        },
        filename: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: false,
            unique: true,
            comment: '文件名（存储在服务器上的文件名）'
        },
        original_name: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: false,
            comment: '原始文件名（用户上传时的文件名）'
        },
        mime_type: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false,
            comment: 'MIME类型'
        },
        size: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            comment: '文件大小（字节）'
        },
        url: {
            type: sequelize_1.DataTypes.STRING(500),
            allowNull: false,
            comment: '文件访问URL'
        },
        thumbnail_url: {
            type: sequelize_1.DataTypes.STRING(500),
            allowNull: true,
            comment: '缩略图URL（仅适用于图片和视频）'
        },
        width: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            comment: '图片/视频宽度（像素）'
        },
        height: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            comment: '图片/视频高度（像素）'
        },
        uploader_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE',
            comment: '上传者用户ID'
        },
        category: {
            type: sequelize_1.DataTypes.ENUM('image', 'video', 'audio', 'document'),
            allowNull: false,
            defaultValue: 'image',
            comment: '媒体类别'
        },
        tags: {
            type: sequelize_1.DataTypes.JSON,
            allowNull: true,
            comment: '标签数组（JSON格式）'
        },
        description: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            comment: '媒体描述'
        },
        is_public: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true,
            comment: '是否公开可见'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '创建时间'
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '更新时间'
        }
    });
    try {
        await queryInterface.addIndex('media', ['uploader_id'], {
            name: 'idx_media_uploader_id'
        });
    }
    catch {
        console.log('Index idx_media_uploader_id already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('media', ['category'], {
            name: 'idx_media_category'
        });
    }
    catch {
        console.log('Index idx_media_category already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('media', ['mime_type'], {
            name: 'idx_media_mime_type'
        });
    }
    catch {
        console.log('Index idx_media_mime_type already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('media', ['is_public'], {
            name: 'idx_media_is_public'
        });
    }
    catch {
        console.log('Index idx_media_is_public already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('media', ['created_at'], {
            name: 'idx_media_created_at'
        });
    }
    catch {
        console.log('Index idx_media_created_at already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('media', ['category', 'is_public'], {
            name: 'idx_media_category_public'
        });
    }
    catch {
        console.log('Index idx_media_category_public already exists, skipping...');
    }
    try {
        await queryInterface.addIndex('media', ['uploader_id', 'category'], {
            name: 'idx_media_uploader_category'
        });
    }
    catch {
        console.log('Index idx_media_uploader_category already exists, skipping...');
    }
    console.log('✅ Media table created successfully');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('media');
    console.log('✅ Media table dropped successfully');
};
exports.down = down;
//# sourceMappingURL=011-create-media.js.map