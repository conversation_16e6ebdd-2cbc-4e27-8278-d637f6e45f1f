import { Request, Response, NextFunction } from 'express';
export declare const registrationValidationRules: import("express-validator").ValidationChain[];
export declare const loginValidationRules: import("express-validator").ValidationChain[];
export declare const passwordResetRequestRules: import("express-validator").ValidationChain[];
export declare const passwordResetRules: import("express-validator").ValidationChain[];
export declare const emailVerificationRules: import("express-validator").ValidationChain[];
export declare const validateRequest: (req: Request, res: Response, next: NextFunction) => void;
export declare const checkUsernameAvailability: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const checkEmailAvailability: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const validatePasswordResetToken: (req: Request, res: Response, next: NextFunction) => void;
export declare const validateEmailVerificationToken: (req: Request, res: Response, next: NextFunction) => void;
export declare const registrationRateLimit: (req: Request, res: Response, next: NextFunction) => void;
export declare const registrationMiddleware: (import("express-validator").ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[];
export declare const loginMiddleware: (import("express-validator").ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[];
export declare const passwordResetRequestMiddleware: (import("express-validator").ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[];
export declare const passwordResetMiddleware: (import("express-validator").ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[];
export declare const emailVerificationMiddleware: (import("express-validator").ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[];
//# sourceMappingURL=registration.d.ts.map