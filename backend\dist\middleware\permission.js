"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requirePermissionOrOwnership = exports.getUserRoles = exports.getUserPermissions = exports.requireOwnershipOrPermission = exports.requireEditor = exports.requireAdmin = exports.requireSuperAdmin = exports.requireAnyRole = exports.requireAllRoles = exports.requireAnyPermission = exports.requireAllPermissions = exports.requireRole = exports.requirePermission = void 0;
const UserRole_1 = require("../models/UserRole");
const errorHandler_1 = require("./errorHandler");
const requirePermission = (permissionName) => {
    return async (req, res, next) => {
        try {
            if (!req.user || !req.user.id) {
                throw (0, errorHandler_1.createError)(401, '需要登录才能访问此资源', 'AUTHENTICATION_REQUIRED');
            }
            const userId = req.user.id;
            const hasPermission = await UserRole_1.UserRole.hasPermission(userId, permissionName);
            if (!hasPermission) {
                throw (0, errorHandler_1.createError)(403, `缺少权限: ${permissionName}`, 'PERMISSION_DENIED');
            }
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
exports.requirePermission = requirePermission;
const requireRole = (roleName) => {
    return async (req, res, next) => {
        try {
            if (!req.user || !req.user.id) {
                throw (0, errorHandler_1.createError)(401, '需要登录才能访问此资源', 'AUTHENTICATION_REQUIRED');
            }
            const userId = req.user.id;
            const hasRole = await UserRole_1.UserRole.hasRoleByName(userId, roleName);
            if (!hasRole) {
                throw (0, errorHandler_1.createError)(403, `需要角色: ${roleName}`, 'ROLE_REQUIRED');
            }
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
exports.requireRole = requireRole;
const requireAllPermissions = (permissionNames) => {
    return async (req, res, next) => {
        try {
            if (!req.user || !req.user.id) {
                throw (0, errorHandler_1.createError)(401, '需要登录才能访问此资源', 'AUTHENTICATION_REQUIRED');
            }
            const userId = req.user.id;
            for (const permissionName of permissionNames) {
                const hasPermission = await UserRole_1.UserRole.hasPermission(userId, permissionName);
                if (!hasPermission) {
                    throw (0, errorHandler_1.createError)(403, `缺少权限: ${permissionName}`, 'PERMISSION_DENIED');
                }
            }
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
exports.requireAllPermissions = requireAllPermissions;
const requireAnyPermission = (permissionNames) => {
    return async (req, res, next) => {
        try {
            if (!req.user || !req.user.id) {
                throw (0, errorHandler_1.createError)(401, '需要登录才能访问此资源', 'AUTHENTICATION_REQUIRED');
            }
            const userId = req.user.id;
            let hasAnyPermission = false;
            for (const permissionName of permissionNames) {
                const hasPermission = await UserRole_1.UserRole.hasPermission(userId, permissionName);
                if (hasPermission) {
                    hasAnyPermission = true;
                    break;
                }
            }
            if (!hasAnyPermission) {
                throw (0, errorHandler_1.createError)(403, `需要以下权限之一: ${permissionNames.join(', ')}`, 'PERMISSION_DENIED');
            }
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
exports.requireAnyPermission = requireAnyPermission;
const requireAllRoles = (roleNames) => {
    return async (req, res, next) => {
        try {
            if (!req.user || !req.user.id) {
                throw (0, errorHandler_1.createError)(401, '需要登录才能访问此资源', 'AUTHENTICATION_REQUIRED');
            }
            const userId = req.user.id;
            for (const roleName of roleNames) {
                const hasRole = await UserRole_1.UserRole.hasRoleByName(userId, roleName);
                if (!hasRole) {
                    throw (0, errorHandler_1.createError)(403, `需要角色: ${roleName}`, 'ROLE_REQUIRED');
                }
            }
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
exports.requireAllRoles = requireAllRoles;
const requireAnyRole = (roleNames) => {
    return async (req, res, next) => {
        try {
            if (!req.user || !req.user.id) {
                throw (0, errorHandler_1.createError)(401, '需要登录才能访问此资源', 'AUTHENTICATION_REQUIRED');
            }
            const userId = req.user.id;
            let hasAnyRole = false;
            for (const roleName of roleNames) {
                const hasRole = await UserRole_1.UserRole.hasRoleByName(userId, roleName);
                if (hasRole) {
                    hasAnyRole = true;
                    break;
                }
            }
            if (!hasAnyRole) {
                throw (0, errorHandler_1.createError)(403, `需要以下角色之一: ${roleNames.join(', ')}`, 'ROLE_REQUIRED');
            }
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
exports.requireAnyRole = requireAnyRole;
exports.requireSuperAdmin = (0, exports.requireRole)('super_admin');
exports.requireAdmin = (0, exports.requireAnyRole)(['admin', 'super_admin']);
exports.requireEditor = (0, exports.requireAnyRole)(['editor', 'admin', 'super_admin']);
const requireOwnershipOrPermission = (getResourceOwnerId, adminPermission) => {
    return async (req, res, next) => {
        try {
            if (!req.user || !req.user.id) {
                throw (0, errorHandler_1.createError)(401, '需要登录才能访问此资源', 'AUTHENTICATION_REQUIRED');
            }
            const userId = req.user.id;
            const resourceOwnerId = await getResourceOwnerId(req);
            if (resourceOwnerId === userId) {
                return next();
            }
            if (adminPermission) {
                const hasAdminPermission = await UserRole_1.UserRole.hasPermission(userId, adminPermission);
                if (hasAdminPermission) {
                    return next();
                }
            }
            throw (0, errorHandler_1.createError)(403, '只能访问自己的资源或需要管理员权限', 'OWNERSHIP_OR_PERMISSION_REQUIRED');
        }
        catch (error) {
            next(error);
        }
    };
};
exports.requireOwnershipOrPermission = requireOwnershipOrPermission;
const getUserPermissions = async (userId) => {
    const permissions = await UserRole_1.UserRole.getUserPermissions(userId);
    return permissions.map(p => p.name);
};
exports.getUserPermissions = getUserPermissions;
const getUserRoles = async (userId) => {
    const roles = await UserRole_1.UserRole.getUserRoles(userId);
    return roles.map(r => r.name);
};
exports.getUserRoles = getUserRoles;
const requirePermissionOrOwnership = (permissionName, getResourceOwnerId) => {
    return async (req, res, next) => {
        try {
            if (!req.user) {
                throw (0, errorHandler_1.createError)(401, '未授权访问', 'UNAUTHORIZED');
            }
            const hasPermission = await UserRole_1.UserRole.hasPermission(req.user.id, permissionName);
            if (hasPermission) {
                return next();
            }
            const resourceOwnerId = await getResourceOwnerId(req);
            if (resourceOwnerId === req.user.id) {
                return next();
            }
            throw (0, errorHandler_1.createError)(403, '权限不足', 'INSUFFICIENT_PERMISSION');
        }
        catch (error) {
            next(error);
        }
    };
};
exports.requirePermissionOrOwnership = requirePermissionOrOwnership;
//# sourceMappingURL=permission.js.map