"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bulkCreatePermissions = exports.deletePermission = exports.updatePermission = exports.createPermission = exports.getPermissionRoles = exports.getResourceActions = exports.getResources = exports.getPermissionsByResource = exports.getPermissionById = exports.getPermissions = void 0;
const Permission_1 = require("../models/Permission");
const Role_1 = require("../models/Role");
const errorHandler_1 = require("../middleware/errorHandler");
const paramValidation_1 = require("../utils/paramValidation");
const getPermissions = async (req, res, next) => {
    try {
        const { page = 1, limit = 50, search, resource, action, isActive } = req.query;
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const offset = (pageNum - 1) * limitNum;
        const whereClause = {};
        if (search) {
            const { Op } = require('sequelize');
            whereClause[Op.or] = [
                { name: { [Op.like]: `%${search}%` } },
                { description: { [Op.like]: `%${search}%` } }
            ];
        }
        if (resource) {
            whereClause.resource = resource;
        }
        if (action) {
            whereClause.action = action;
        }
        if (isActive !== undefined) {
            whereClause.isActive = isActive === 'true';
        }
        const { rows: permissions, count: total } = await Permission_1.Permission.findAndCountAll({
            where: whereClause,
            limit: limitNum,
            offset,
            order: [['resource', 'ASC'], ['action', 'ASC']],
            include: [
                {
                    model: Role_1.Role,
                    as: 'roles',
                    through: { attributes: [] }
                }
            ]
        });
        const permissionsWithStats = await Promise.all(permissions.map(async (permission) => {
            const roleCount = await permission.getRoleCount();
            return {
                ...permission.toJSON(),
                roleCount
            };
        }));
        res.json({
            success: true,
            data: {
                permissions: permissionsWithStats,
                pagination: {
                    page: pageNum,
                    limit: limitNum,
                    total,
                    pages: Math.ceil(total / limitNum)
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getPermissions = getPermissions;
const getPermissionById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const permission = await Permission_1.Permission.findByPk(id, {
            include: [
                {
                    model: Role_1.Role,
                    as: 'roles',
                    through: { attributes: ['assignedAt', 'assignedBy'] }
                }
            ]
        });
        if (!permission) {
            throw (0, errorHandler_1.createError)(404, '权限不存在', 'PERMISSION_NOT_FOUND');
        }
        const roleCount = await permission.getRoleCount();
        res.json({
            success: true,
            data: {
                ...permission.toJSON(),
                roleCount
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getPermissionById = getPermissionById;
const getPermissionsByResource = async (req, res, next) => {
    try {
        const { isActive } = req.query;
        const whereClause = {};
        if (isActive !== undefined) {
            whereClause.isActive = isActive === 'true';
        }
        const permissions = await Permission_1.Permission.findAll({
            where: whereClause,
            order: [['resource', 'ASC'], ['action', 'ASC']]
        });
        const groupedPermissions = {};
        permissions.forEach(permission => {
            const resource = permission.resource;
            if (!groupedPermissions[resource]) {
                groupedPermissions[resource] = [];
            }
            groupedPermissions[resource].push(permission.toJSON());
        });
        res.json({
            success: true,
            data: groupedPermissions
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getPermissionsByResource = getPermissionsByResource;
const getResources = async (req, res, next) => {
    try {
        const resources = await Permission_1.Permission.getResources();
        res.json({
            success: true,
            data: resources
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getResources = getResources;
const getResourceActions = async (req, res, next) => {
    try {
        const { resource } = req.params;
        const resourceName = (0, paramValidation_1.getStringParam)(req, 'resource');
        const actions = await Permission_1.Permission.getActionsByResource(resourceName);
        res.json({
            success: true,
            data: actions
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getResourceActions = getResourceActions;
const getPermissionRoles = async (req, res, next) => {
    try {
        const { id } = req.params;
        const permission = await Permission_1.Permission.findByPk(id);
        if (!permission) {
            throw (0, errorHandler_1.createError)(404, '权限不存在', 'PERMISSION_NOT_FOUND');
        }
        const roles = await permission.getRoles();
        res.json({
            success: true,
            data: roles
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getPermissionRoles = getPermissionRoles;
const createPermission = async (req, res, next) => {
    try {
        const { name, description, resource, action, isActive = true } = req.body;
        const existingPermission = await Permission_1.Permission.findByName(name);
        if (existingPermission) {
            throw (0, errorHandler_1.createError)(400, '权限名称已存在', 'PERMISSION_NAME_EXISTS');
        }
        const existingResourceAction = await Permission_1.Permission.findByResourceAndAction(resource, action);
        if (existingResourceAction) {
            throw (0, errorHandler_1.createError)(400, '该资源和操作的权限已存在', 'RESOURCE_ACTION_EXISTS');
        }
        const permission = await Permission_1.Permission.create({
            name,
            description,
            resource,
            action,
            isActive
        });
        res.status(201).json({
            success: true,
            data: permission,
            message: '权限创建成功'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.createPermission = createPermission;
const updatePermission = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { name, description, resource, action, isActive } = req.body;
        const permission = await Permission_1.Permission.findByPk(id);
        if (!permission) {
            throw (0, errorHandler_1.createError)(404, '权限不存在', 'PERMISSION_NOT_FOUND');
        }
        if (name && name !== permission.name) {
            const existingPermission = await Permission_1.Permission.findByName(name);
            if (existingPermission) {
                throw (0, errorHandler_1.createError)(400, '权限名称已存在', 'PERMISSION_NAME_EXISTS');
            }
        }
        const newResource = resource || permission.resource;
        const newAction = action || permission.action;
        if ((resource && resource !== permission.resource) || (action && action !== permission.action)) {
            const existingResourceAction = await Permission_1.Permission.findByResourceAndAction(newResource, newAction);
            if (existingResourceAction && existingResourceAction.id !== permission.id) {
                throw (0, errorHandler_1.createError)(400, '该资源和操作的权限已存在', 'RESOURCE_ACTION_EXISTS');
            }
        }
        await permission.update({
            name: name || permission.name,
            description: description !== undefined ? description : permission.description,
            resource: resource || permission.resource,
            action: action || permission.action,
            isActive: isActive !== undefined ? isActive : permission.isActive
        });
        res.json({
            success: true,
            data: permission,
            message: '权限更新成功'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.updatePermission = updatePermission;
const deletePermission = async (req, res, next) => {
    try {
        const { id } = req.params;
        const permission = await Permission_1.Permission.findByPk(id);
        if (!permission) {
            throw (0, errorHandler_1.createError)(404, '权限不存在', 'PERMISSION_NOT_FOUND');
        }
        const roleCount = await permission.getRoleCount();
        if (roleCount > 0) {
            throw (0, errorHandler_1.createError)(400, `权限正在被 ${roleCount} 个角色使用，无法删除`, 'PERMISSION_IN_USE');
        }
        await permission.destroy();
        res.json({
            success: true,
            message: '权限删除成功'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.deletePermission = deletePermission;
const bulkCreatePermissions = async (req, res, next) => {
    try {
        const { permissions } = req.body;
        if (!Array.isArray(permissions) || permissions.length === 0) {
            throw (0, errorHandler_1.createError)(400, '权限数据不能为空', 'INVALID_PERMISSIONS_DATA');
        }
        for (const perm of permissions) {
            if (!perm.name || !perm.resource || !perm.action) {
                throw (0, errorHandler_1.createError)(400, '权限数据格式不正确，缺少必要字段', 'INVALID_PERMISSION_FORMAT');
            }
        }
        const createdPermissions = await Permission_1.Permission.bulkCreatePermissions(permissions);
        res.status(201).json({
            success: true,
            data: createdPermissions,
            message: `成功创建 ${createdPermissions.length} 个权限`
        });
    }
    catch (error) {
        next(error);
    }
};
exports.bulkCreatePermissions = bulkCreatePermissions;
//# sourceMappingURL=permission.js.map