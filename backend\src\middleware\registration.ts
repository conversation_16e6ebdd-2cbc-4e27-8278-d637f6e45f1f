import { Request, Response, NextFunction } from 'express'
import { body, validationResult } from 'express-validator'
import { createError } from './errorHandler'
import { UserValidationService } from '../utils/userValidation'
import { VerificationService } from '../utils/verification'

/**
 * 注册数据验证规则
 */
export const registrationValidationRules = [
  body('username')
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50位之间')
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('用户名只能包含字母、数字、下划线和连字符')
    .custom((value) => {
      if (/^[0-9]/.test(value)) {
        throw new Error('用户名不能以数字开头')
      }
      if (UserValidationService.containsProfanity(value)) {
        throw new Error('用户名包含不当内容')
      }
      return true
    }),

  body('email')
    .trim()
    .isEmail()
    .withMessage('邮箱格式无效')
    .normalizeEmail()
    .isLength({ max: 255 })
    .withMessage('邮箱长度不能超过255位'),

  body('password')
    .isLength({ min: 8, max: 128 })
    .withMessage('密码长度必须在8-128位之间')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])/)
    .withMessage('密码必须包含大小写字母、数字和特殊字符'),

  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('两次输入的密码不一致')
      }
      return true
    })
]

/**
 * 登录数据验证规则
 */
export const loginValidationRules = [
  body('username')
    .trim()
    .notEmpty()
    .withMessage('用户名不能为空')
    .isLength({ max: 50 })
    .withMessage('用户名长度不能超过50位'),

  body('password')
    .notEmpty()
    .withMessage('密码不能为空')
    .isLength({ max: 128 })
    .withMessage('密码长度不能超过128位')
]

/**
 * 密码重置请求验证规则
 */
export const passwordResetRequestRules = [
  body('email')
    .trim()
    .isEmail()
    .withMessage('邮箱格式无效')
    .normalizeEmail()
]

/**
 * 密码重置验证规则
 */
export const passwordResetRules = [
  body('token')
    .notEmpty()
    .withMessage('重置令牌不能为空'),

  body('password')
    .isLength({ min: 8, max: 128 })
    .withMessage('密码长度必须在8-128位之间')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])/)
    .withMessage('密码必须包含大小写字母、数字和特殊字符'),

  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('两次输入的密码不一致')
      }
      return true
    })
]

/**
 * 邮箱验证规则
 */
export const emailVerificationRules = [
  body('token')
    .notEmpty()
    .withMessage('验证令牌不能为空')
]

/**
 * 验证请求数据的中间件
 */
export const validateRequest = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req)
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg)
    throw createError(400, errorMessages.join('; '), 'VALIDATION_ERROR')
  }
  
  next()
}

/**
 * 检查用户名可用性的中间件
 */
export const checkUsernameAvailability = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { username } = req.body

    if (!username) {
      next()
      return
    }

    const availability = await UserValidationService.checkUsernameAvailability(username)
    if (!availability.available) {
      throw createError(400, availability.message || '用户名不可用', 'USERNAME_UNAVAILABLE')
    }

    next()
  } catch (error) {
    next(error)
  }
}

/**
 * 检查邮箱可用性的中间件
 */
export const checkEmailAvailability = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { email } = req.body

    if (!email) {
      next()
      return
    }

    const availability = await UserValidationService.checkEmailAvailability(email)
    if (!availability.available) {
      throw createError(400, availability.message || '邮箱不可用', 'EMAIL_UNAVAILABLE')
    }

    next()
  } catch (error) {
    next(error)
  }
}

/**
 * 验证密码重置token的中间件
 */
export const validatePasswordResetToken = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  try {
    const { token } = req.body

    const result = VerificationService.verifyPasswordResetToken(token)
    if (!result.valid) {
      throw createError(400, result.error || '无效的重置令牌', 'INVALID_RESET_TOKEN')
    }

    // 将解析的数据添加到请求对象中
    req.body.tokenData = result.data
    next()
  } catch (error) {
    next(error)
  }
}

/**
 * 验证邮箱验证token的中间件
 */
export const validateEmailVerificationToken = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  try {
    const { token } = req.body

    const result = VerificationService.verifyEmailVerificationToken(token)
    if (!result.valid) {
      throw createError(400, result.error || '无效的验证令牌', 'INVALID_VERIFICATION_TOKEN')
    }

    // 将解析的数据添加到请求对象中
    req.body.tokenData = result.data
    next()
  } catch (error) {
    next(error)
  }
}

/**
 * 限制注册频率的中间件
 */
export const registrationRateLimit = (() => {
  const attempts = new Map<string, { count: number; resetTime: number }>()
  const MAX_ATTEMPTS = 5 // 最大尝试次数
  const WINDOW_MS = 15 * 60 * 1000 // 15分钟窗口

  return (req: Request, res: Response, next: NextFunction): void => {
    const clientIP = req.ip || req.connection.remoteAddress || 'unknown'
    const now = Date.now()

    // 清理过期记录
    for (const [ip, data] of attempts.entries()) {
      if (now > data.resetTime) {
        attempts.delete(ip)
      }
    }

    const clientAttempts = attempts.get(clientIP)

    if (!clientAttempts) {
      // 首次尝试
      attempts.set(clientIP, { count: 1, resetTime: now + WINDOW_MS })
      next()
      return
    }

    if (now > clientAttempts.resetTime) {
      // 窗口已过期，重置计数
      attempts.set(clientIP, { count: 1, resetTime: now + WINDOW_MS })
      next()
      return
    }

    if (clientAttempts.count >= MAX_ATTEMPTS) {
      throw createError(429, '注册尝试过于频繁，请稍后再试', 'RATE_LIMIT_EXCEEDED')
    }

    // 增加尝试次数
    clientAttempts.count++
    next()
  }
})()

/**
 * 组合的注册验证中间件
 */
export const registrationMiddleware = [
  registrationRateLimit,
  ...registrationValidationRules,
  validateRequest,
  checkUsernameAvailability,
  checkEmailAvailability
]

/**
 * 组合的登录验证中间件
 */
export const loginMiddleware = [
  ...loginValidationRules,
  validateRequest
]

/**
 * 组合的密码重置请求中间件
 */
export const passwordResetRequestMiddleware = [
  ...passwordResetRequestRules,
  validateRequest
]

/**
 * 组合的密码重置中间件
 */
export const passwordResetMiddleware = [
  ...passwordResetRules,
  validateRequest,
  validatePasswordResetToken
]

/**
 * 组合的邮箱验证中间件
 */
export const emailVerificationMiddleware = [
  ...emailVerificationRules,
  validateRequest,
  validateEmailVerificationToken
]
