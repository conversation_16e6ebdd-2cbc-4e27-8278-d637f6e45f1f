import { Request, Response, NextFunction } from 'express';
import Jo<PERSON> from 'joi';
export declare const validate: (schema: Joi.ObjectSchema) => (req: Request, res: Response, next: NextFunction) => void;
export declare const validateRequest: (schema: Joi.ObjectSchema) => (req: Request, res: Response, next: NextFunction) => void;
export declare const validateQuery: (schema: Joi.ObjectSchema) => (req: Request, res: Response, next: NextFunction) => void;
export declare const validateParams: (schema: Joi.ObjectSchema) => (req: Request, res: Response, next: NextFunction) => void;
export declare const validateArticle: (req: Request, res: Response, next: NextFunction) => void;
export declare const validateArticleUpdate: (req: Request, res: Response, next: NextFunction) => void;
export declare const validateCategory: (req: Request, res: Response, next: NextFunction) => void;
export declare const validateCategoryUpdate: (req: Request, res: Response, next: NextFunction) => void;
export declare const validateComment: (req: Request, res: Response, next: NextFunction) => void;
export declare const validateCommentUpdate: (req: Request, res: Response, next: NextFunction) => void;
export declare const validateCommentStatus: (req: Request, res: Response, next: NextFunction) => void;
export declare const validateCommentQuery: (req: Request, res: Response, next: NextFunction) => void;
export declare const validatePostCreation: (req: Request, res: Response, next: NextFunction) => void;
export declare const validatePostUpdate: (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=validation.d.ts.map