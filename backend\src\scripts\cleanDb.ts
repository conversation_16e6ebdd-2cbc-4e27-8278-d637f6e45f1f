#!/usr/bin/env ts-node

import { sequelize } from '../config/database'
import { dbConnection } from '../database/connection'
import dotenv from 'dotenv'

// 加载环境变量
dotenv.config()

/**
 * 数据库清理脚本
 * 删除所有表和数据，用于重新初始化
 */
class CleanDbScript {
  /**
   * 清理数据库
   */
  async run(): Promise<void> {
    console.log('🧹 Starting database cleanup...')
    console.log('='.repeat(50))

    try {
      // 测试数据库连接
      console.log('🔍 Testing database connection...')
      const isConnected = await dbConnection.testConnection()

      if (!isConnected) {
        console.error('❌ Database connection failed!')
        process.exit(1)
      }

      console.log('✅ Database connection successful!')
      console.log('')

      // 获取查询接口
      const queryInterface = sequelize.getQueryInterface()

      // 禁用外键检查
      await queryInterface.sequelize.query('SET FOREIGN_KEY_CHECKS = 0')
      console.log('🔓 Disabled foreign key checks')

      // 删除表（按依赖关系倒序）
      const tablesToDrop = [
        'audit_logs',
        'comments',
        'post_likes',
        'posts',
        'article_tags',
        'articles',
        'categories',
        'tags',
        'notifications',
        'notification_preferences',
        'media',
        'settings',
        'role_permissions',
        'user_roles',
        'permissions',
        'roles',
        'users',
        'seeders',
        'migrations'
      ]

      for (const tableName of tablesToDrop) {
        try {
          await queryInterface.dropTable(tableName)
          console.log(`🗑️  Dropped table: ${tableName}`)
        } catch (error) {
          console.log(`⚠️  Table ${tableName} does not exist, skipping...`)
        }
      }

      // 重新启用外键检查
      await queryInterface.sequelize.query('SET FOREIGN_KEY_CHECKS = 1')
      console.log('🔒 Re-enabled foreign key checks')

      console.log('')
      console.log('='.repeat(50))
      console.log('✅ Database cleanup completed successfully!')
      console.log('💡 You can now run "npm run db:init" to reinitialize the database')

    } catch (error) {
      console.error('')
      console.error('='.repeat(50))
      console.error('❌ Database cleanup failed!')
      console.error('Error details:', error)
      process.exit(1)
    } finally {
      // 关闭数据库连接
      await dbConnection.closeConnection()
    }
  }
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  const args = process.argv.slice(2)

  // 检查是否请求帮助
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
📚 Database Cleanup Tool

Usage:
  ts-node src/scripts/cleanDb.ts    Clean all database tables

⚠️  WARNING: This will delete ALL data in the database!

Examples:
  ts-node src/scripts/cleanDb.ts
`)
    return
  }

  // 确认操作
  console.log('⚠️  WARNING: This will delete ALL data in the database!')
  console.log('Are you sure you want to continue? (This action cannot be undone)')
  console.log('Press Ctrl+C to cancel, or press Enter to continue...')

  // 等待用户确认（在脚本环境中直接执行）
  const script = new CleanDbScript()
  await script.run()
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error)
  process.exit(1)
})

// 执行主函数
if (require.main === module) {
  main().catch((error) => {
    console.error('Script execution failed:', error)
    process.exit(1)
  })
}
