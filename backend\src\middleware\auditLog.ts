import { Request, Response, NextFunction } from 'express'
import { AuditLog } from '../models/AuditLog'
import { getIdParam, getUserIdParam, getRoleIdParam } from '../utils/paramValidation'

/**
 * 扩展 Express 的 Request 接口，添加用户认证信息和审计相关字段
 */
interface AuthenticatedRequest extends Request {
  user?: {
    id: number
    username: string
    email: string
  }
  auditData?: {
    action: string
    resource: string
    resourceId?: number
    oldData?: any
    startTime: number
  }
}

/**
 * 操作日志配置接口
 */
interface AuditLogConfig {
  action: string
  resource: string
  getResourceId?: (req: AuthenticatedRequest) => number | undefined
  captureOldData?: boolean
  captureNewData?: boolean
  skipCondition?: (req: AuthenticatedRequest) => boolean
}

/**
 * 获取客户端真实IP地址
 * @param req - Express请求对象
 * @returns IP地址字符串
 */
const getClientIP = (req: Request): string => {
  const forwardedFor = req.headers['x-forwarded-for']
  const realIP = req.headers['x-real-ip']

  if (typeof forwardedFor === 'string') {
    return forwardedFor.split(',')[0]?.trim() || 'unknown'
  }

  if (Array.isArray(forwardedFor) && forwardedFor[0]) {
    return forwardedFor[0]
  }

  if (typeof realIP === 'string') {
    return realIP
  }

  return req.socket?.remoteAddress || req.ip || 'unknown'
}

/**
 * 获取会话ID
 * @param req - Express请求对象
 * @returns 会话ID字符串
 */
const getSessionId = (req: Request): string | undefined => {
  // 从JWT token中提取会话信息，或从session中获取
  const authHeader = req.headers.authorization
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7, 47) // 取token的一部分作为会话标识
  }
  return undefined
}

/**
 * 创建操作日志中间件
 * @param config - 审计配置
 * @returns Express中间件函数
 */
export const createAuditLogMiddleware = (config: AuditLogConfig) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      // 检查是否需要跳过记录
      if (config.skipCondition && config.skipCondition(req)) {
        return next()
      }

      const startTime = Date.now()

      const resourceId = config.getResourceId ? config.getResourceId(req) : undefined

      // 在请求对象中存储审计数据
      req.auditData = {
        action: config.action,
        resource: config.resource,
        ...(resourceId !== undefined && { resourceId }),
        startTime
      }

      // 如果需要捕获旧数据，在操作前获取
      if (config.captureOldData && resourceId) {
        try {
          // 这里需要根据资源类型动态获取旧数据
          // 为了简化，我们先存储资源ID，在响应后处理时再获取
          req.auditData.oldData = await getResourceData(config.resource, resourceId)
        } catch (error) {
          console.warn('Failed to capture old data:', error)
        }
      }

      // 保存原始的res.json方法
      const originalJson = res.json.bind(res)

      // 重写res.json方法以捕获响应数据
      res.json = function (data: any) {
        // 异步记录审计日志，不阻塞响应
        setImmediate(async () => {
          try {
            await recordAuditLog(req, res, config, data)
          } catch (error) {
            console.error('Failed to record audit log:', error)
          }
        })

        // 调用原始的json方法
        return originalJson(data)
      }

      next()
    } catch (error) {
      console.error('Audit middleware error:', error)
      next() // 即使审计失败也要继续处理请求
    }
  }
}

/**
 * 记录审计日志
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param config - 审计配置
 * @param responseData - 响应数据
 */
const recordAuditLog = async (
  req: AuthenticatedRequest,
  res: Response,
  config: AuditLogConfig,
  responseData: any
): Promise<void> => {
  try {
    const endTime = Date.now()
    const duration = req.auditData ? endTime - req.auditData.startTime : 0

    // 确定操作状态
    const status = res.statusCode >= 200 && res.statusCode < 300 ? 'success' : 'failed'

    // 准备新数据（如果需要捕获）
    let newData: string | undefined
    if (config.captureNewData && responseData && status === 'success') {
      try {
        // 只保存关键数据，避免存储过大的响应
        if (responseData.data) {
          newData = JSON.stringify(responseData.data)
        } else if (typeof responseData === 'object') {
          newData = JSON.stringify(responseData)
        }
      } catch (error) {
        console.warn('Failed to serialize new data:', error)
      }
    }

    // 准备错误信息
    let errorMessage: string | undefined
    if (status === 'failed') {
      if (responseData && responseData.error && responseData.error.message) {
        errorMessage = responseData.error.message
      } else if (responseData && responseData.message) {
        errorMessage = responseData.message
      } else {
        errorMessage = `HTTP ${res.statusCode}`
      }
    }

    // 创建审计日志记录
    const logData: any = {
      action: req.auditData?.action || config.action,
      resource: req.auditData?.resource || config.resource,
      ipAddress: getClientIP(req),
      status,
      duration
    }

    // 只添加非undefined的可选字段
    if (req.user?.id !== undefined) logData.userId = req.user.id
    if (req.auditData?.resourceId !== undefined) logData.resourceId = req.auditData.resourceId
    if (req.auditData?.oldData) logData.oldData = JSON.stringify(req.auditData.oldData)
    if (newData) logData.newData = newData
    if (req.headers['user-agent']) logData.userAgent = req.headers['user-agent']
    if (getSessionId(req)) logData.sessionId = getSessionId(req)
    if (errorMessage) logData.errorMessage = errorMessage

    await AuditLog.logAction(logData)
  } catch (error) {
    console.error('Failed to record audit log:', error)
  }
}

/**
 * 根据资源类型和ID获取资源数据
 * @param resource - 资源类型
 * @param resourceId - 资源ID
 * @returns 资源数据或null
 */
const getResourceData = async (resource: string, resourceId: number): Promise<any> => {
  try {
    // 动态导入模型以避免循环依赖
    const models = await import('../models')

    switch (resource.toLowerCase()) {
      case 'user':
        const user = await models.User.findByPk(resourceId, {
          attributes: { exclude: ['passwordHash'] }
        })
        return user?.toJSON()

      case 'article':
        const article = await models.Article.findByPk(resourceId)
        return article?.toJSON()

      case 'post':
        const post = await models.Post.findByPk(resourceId)
        return post?.toJSON()

      case 'comment':
        const comment = await models.Comment.findByPk(resourceId)
        return comment?.toJSON()

      case 'category':
        const category = await models.Category.findByPk(resourceId)
        return category?.toJSON()

      case 'tag':
        const tag = await models.Tag.findByPk(resourceId)
        return tag?.toJSON()

      case 'role':
        const role = await models.Role.findByPk(resourceId)
        return role?.toJSON()

      case 'permission':
        const permission = await models.Permission.findByPk(resourceId)
        return permission?.toJSON()

      default:
        return null
    }
  } catch (error) {
    console.warn(`Failed to get ${resource} data:`, error)
    return null
  }
}

/**
 * 预定义的审计中间件
 */

// 用户相关操作
export const auditUserCreate = createAuditLogMiddleware({
  action: 'CREATE',
  resource: 'user',
  captureNewData: true
})

export const auditUserUpdate = createAuditLogMiddleware({
  action: 'UPDATE',
  resource: 'user',
  getResourceId: (req) => getIdParam(req),
  captureOldData: true,
  captureNewData: true
})

export const auditUserDelete = createAuditLogMiddleware({
  action: 'DELETE',
  resource: 'user',
  getResourceId: (req) => getIdParam(req),
  captureOldData: true
})

// 文章相关操作
export const auditArticleCreate = createAuditLogMiddleware({
  action: 'CREATE',
  resource: 'article',
  captureNewData: true
})

export const auditArticleUpdate = createAuditLogMiddleware({
  action: 'UPDATE',
  resource: 'article',
  getResourceId: (req) => getIdParam(req),
  captureOldData: true,
  captureNewData: true
})

export const auditArticleDelete = createAuditLogMiddleware({
  action: 'DELETE',
  resource: 'article',
  getResourceId: (req) => getIdParam(req),
  captureOldData: true
})

// 登录操作
export const auditLogin = createAuditLogMiddleware({
  action: 'LOGIN',
  resource: 'auth',
  captureNewData: false
})

// 登出操作
export const auditLogout = createAuditLogMiddleware({
  action: 'LOGOUT',
  resource: 'auth',
  captureNewData: false
})

// 角色权限相关操作
export const auditRoleAssign = createAuditLogMiddleware({
  action: 'ASSIGN_ROLE',
  resource: 'user_role',
  getResourceId: (req) => getUserIdParam(req),
  captureOldData: true,
  captureNewData: true
})

export const auditPermissionGrant = createAuditLogMiddleware({
  action: 'GRANT_PERMISSION',
  resource: 'role_permission',
  getResourceId: (req) => getRoleIdParam(req),
  captureOldData: true,
  captureNewData: true
})

export default {
  createAuditLogMiddleware,
  auditUserCreate,
  auditUserUpdate,
  auditUserDelete,
  auditArticleCreate,
  auditArticleUpdate,
  auditArticleDelete,
  auditLogin,
  auditLogout,
  auditRoleAssign,
  auditPermissionGrant
}
