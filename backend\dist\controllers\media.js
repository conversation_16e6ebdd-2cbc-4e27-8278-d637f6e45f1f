"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MediaController = void 0;
const Media_1 = require("../models/Media");
const User_1 = require("../models/User");
const sequelize_1 = require("sequelize");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const paramValidation_1 = require("../utils/paramValidation");
class MediaController {
    static async getMediaList(req, res) {
        try {
            const { page = '1', limit = '20', category, search, uploaderId, isPublic, sortBy = 'createdAt', sortOrder = 'DESC' } = req.query;
            const pageNum = parseInt(page, 10);
            const limitNum = parseInt(limit, 10);
            const offset = (pageNum - 1) * limitNum;
            const whereClause = {};
            if (category) {
                whereClause.category = category;
            }
            if (uploaderId) {
                whereClause.uploaderId = parseInt(uploaderId, 10);
            }
            if (isPublic !== undefined) {
                whereClause.isPublic = isPublic === 'true';
            }
            if (search) {
                whereClause[sequelize_1.Op.or] = [
                    { originalName: { [sequelize_1.Op.like]: `%${search}%` } },
                    { description: { [sequelize_1.Op.like]: `%${search}%` } },
                    { filename: { [sequelize_1.Op.like]: `%${search}%` } }
                ];
            }
            if (req.user) {
                whereClause[sequelize_1.Op.or] = [
                    { isPublic: true },
                    { uploaderId: req.user.id }
                ];
            }
            else {
                whereClause.isPublic = true;
            }
            const { count, rows } = await Media_1.Media.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: User_1.User,
                        as: 'uploader',
                        attributes: ['id', 'username']
                    }
                ],
                order: [[sortBy, sortOrder]],
                limit: limitNum,
                offset
            });
            const totalPages = Math.ceil(count / limitNum);
            res.json({
                success: true,
                data: {
                    media: rows,
                    pagination: {
                        currentPage: pageNum,
                        totalPages,
                        totalItems: count,
                        itemsPerPage: limitNum,
                        hasNextPage: pageNum < totalPages,
                        hasPrevPage: pageNum > 1
                    }
                }
            });
        }
        catch (error) {
            console.error('获取媒体列表失败:', error);
            res.status(500).json({
                success: false,
                message: '获取媒体列表失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    static async getMediaById(req, res) {
        try {
            const { id } = req.params;
            const mediaId = (0, paramValidation_1.getIdParam)(req);
            if (isNaN(mediaId)) {
                res.status(400).json({
                    success: false,
                    message: '无效的媒体ID'
                });
                return;
            }
            const media = await Media_1.Media.findByPk(mediaId, {
                include: [
                    {
                        model: User_1.User,
                        as: 'uploader',
                        attributes: ['id', 'username', 'email']
                    }
                ]
            });
            if (!media) {
                res.status(404).json({
                    success: false,
                    message: '媒体文件不存在'
                });
                return;
            }
            if (!media.isPublic && (!req.user || req.user.id !== media.uploaderId)) {
                res.status(403).json({
                    success: false,
                    message: '没有权限访问此媒体文件'
                });
                return;
            }
            res.json({
                success: true,
                data: media
            });
        }
        catch (error) {
            console.error('获取媒体详情失败:', error);
            res.status(500).json({
                success: false,
                message: '获取媒体详情失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    static async updateMedia(req, res) {
        try {
            const { id } = req.params;
            const mediaId = (0, paramValidation_1.getIdParam)(req);
            const { description, tags, isPublic } = req.body;
            if (isNaN(mediaId)) {
                res.status(400).json({
                    success: false,
                    message: '无效的媒体ID'
                });
                return;
            }
            const media = await Media_1.Media.findByPk(mediaId);
            if (!media) {
                res.status(404).json({
                    success: false,
                    message: '媒体文件不存在'
                });
                return;
            }
            if (!req.user || req.user.id !== media.uploaderId) {
                res.status(403).json({
                    success: false,
                    message: '没有权限修改此媒体文件'
                });
                return;
            }
            const updateData = {};
            if (description !== undefined)
                updateData.description = description;
            if (tags !== undefined)
                updateData.tags = tags;
            if (isPublic !== undefined)
                updateData.isPublic = isPublic;
            await media.update(updateData);
            const updatedMedia = await Media_1.Media.findByPk(mediaId, {
                include: [
                    {
                        model: User_1.User,
                        as: 'uploader',
                        attributes: ['id', 'username']
                    }
                ]
            });
            res.json({
                success: true,
                message: '媒体信息更新成功',
                data: updatedMedia
            });
        }
        catch (error) {
            console.error('更新媒体信息失败:', error);
            res.status(500).json({
                success: false,
                message: '更新媒体信息失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    static async deleteMedia(req, res) {
        try {
            const { id } = req.params;
            const mediaId = (0, paramValidation_1.getIdParam)(req);
            if (isNaN(mediaId)) {
                res.status(400).json({
                    success: false,
                    message: '无效的媒体ID'
                });
                return;
            }
            const media = await Media_1.Media.findByPk(mediaId);
            if (!media) {
                res.status(404).json({
                    success: false,
                    message: '媒体文件不存在'
                });
                return;
            }
            if (!req.user || req.user.id !== media.uploaderId) {
                res.status(403).json({
                    success: false,
                    message: '没有权限删除此媒体文件'
                });
                return;
            }
            try {
                const filePath = path_1.default.join(process.cwd(), 'uploads', 'images', media.filename);
                if (fs_1.default.existsSync(filePath)) {
                    fs_1.default.unlinkSync(filePath);
                }
                if (media.thumbnailUrl) {
                    const thumbnailFilename = media.thumbnailUrl.split('/').pop();
                    if (thumbnailFilename) {
                        const thumbnailPath = path_1.default.join(process.cwd(), 'uploads', 'thumbnails', thumbnailFilename);
                        if (fs_1.default.existsSync(thumbnailPath)) {
                            fs_1.default.unlinkSync(thumbnailPath);
                        }
                    }
                }
            }
            catch (fileError) {
                console.warn('删除物理文件失败:', fileError);
            }
            await media.destroy();
            res.json({
                success: true,
                message: '媒体文件删除成功'
            });
        }
        catch (error) {
            console.error('删除媒体文件失败:', error);
            res.status(500).json({
                success: false,
                message: '删除媒体文件失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    static async getMediaStats(req, res) {
        try {
            const userId = req.user?.id;
            const baseWhere = userId ? { uploaderId: userId } : { isPublic: true };
            const [totalCount, imageCount, videoCount, audioCount, documentCount, totalSize] = await Promise.all([
                Media_1.Media.count({ where: baseWhere }),
                Media_1.Media.count({ where: { ...baseWhere, category: 'image' } }),
                Media_1.Media.count({ where: { ...baseWhere, category: 'video' } }),
                Media_1.Media.count({ where: { ...baseWhere, category: 'audio' } }),
                Media_1.Media.count({ where: { ...baseWhere, category: 'document' } }),
                Media_1.Media.sum('size', { where: baseWhere }) || 0
            ]);
            res.json({
                success: true,
                data: {
                    total: totalCount,
                    byCategory: {
                        image: imageCount,
                        video: videoCount,
                        audio: audioCount,
                        document: documentCount
                    },
                    totalSize,
                    formattedTotalSize: formatFileSize(totalSize)
                }
            });
        }
        catch (error) {
            console.error('获取媒体统计失败:', error);
            res.status(500).json({
                success: false,
                message: '获取媒体统计失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
}
exports.MediaController = MediaController;
function formatFileSize(bytes) {
    if (bytes === 0)
        return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
//# sourceMappingURL=media.js.map